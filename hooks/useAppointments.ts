import { useState, useEffect } from 'react'

export interface AppointmentEvent {
  id: number
  title: string
  start: Date
  end: Date
  appointmentType: 'CONSULTATION' | 'TREATMENT' | 'CHECKUP'
  status: 'SCHEDULED' | 'COMPLETED' | 'CANCELLED'
  notes?: string
  patient: {
    id: number
    firstName: string
    lastName: string
    phoneNumber: string
    email?: string
  }
  primaryProvider?: {
    id: number
    firstName: string
    lastName: string
  }
  backgroundColor: string
  color: string
}

interface UseAppointmentsReturn {
  appointments: AppointmentEvent[]
  loading: boolean
  error: string | null
  refetch: () => void
  createAppointment: (appointment: Partial<AppointmentEvent>) => Promise<boolean>
  updateAppointment: (id: number, appointment: Partial<AppointmentEvent>) => Promise<boolean>
  deleteAppointment: (id: number) => Promise<boolean>
}

export function useAppointments(startDate?: Date, endDate?: Date): UseAppointmentsReturn {
  const [appointments, setAppointments] = useState<AppointmentEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAppointments = async () => {
    try {
      setLoading(true)
      setError(null)
      
      let url = '/api/appointments'
      const params = new URLSearchParams()
      
      if (startDate) {
        params.append('start', startDate.toISOString())
      }
      if (endDate) {
        params.append('end', endDate.toISOString())
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`
      }

      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch appointments')
      }

      if (data.success) {
        // Convert date strings back to Date objects
        const appointmentsWithDates = data.appointments.map((appointment: any) => ({
          ...appointment,
          start: new Date(appointment.start),
          end: new Date(appointment.end)
        }))
        setAppointments(appointmentsWithDates)
      } else {
        throw new Error(data.message || 'Failed to fetch appointments')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const createAppointment = async (appointment: Partial<AppointmentEvent>): Promise<boolean> => {
    try {
      const response = await fetch('/api/appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId: appointment.patient?.id,
          appointmentDate: appointment.start,
          durationMinutes: appointment.end && appointment.start 
            ? Math.round((appointment.end.getTime() - appointment.start.getTime()) / 60000)
            : 60,
          appointmentType: appointment.appointmentType || 'CONSULTATION',
          notes: appointment.notes,
          primaryProviderId: appointment.primaryProvider?.id
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create appointment')
      }

      if (data.success) {
        // Add the new appointment to the list
        const newAppointment = {
          ...data.appointment,
          start: new Date(data.appointment.start),
          end: new Date(data.appointment.end)
        }
        setAppointments(prev => [...prev, newAppointment])
        return true
      } else {
        throw new Error(data.message || 'Failed to create appointment')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      return false
    }
  }

  const updateAppointment = async (id: number, appointment: Partial<AppointmentEvent>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/appointments/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appointmentDate: appointment.start,
          durationMinutes: appointment.end && appointment.start 
            ? Math.round((appointment.end.getTime() - appointment.start.getTime()) / 60000)
            : undefined,
          appointmentType: appointment.appointmentType,
          status: appointment.status,
          notes: appointment.notes,
          primaryProviderId: appointment.primaryProvider?.id
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to update appointment')
      }

      if (data.success) {
        // Update the appointment in the list
        const updatedAppointment = {
          ...data.appointment,
          start: new Date(data.appointment.start),
          end: new Date(data.appointment.end)
        }
        setAppointments(prev => prev.map(apt => apt.id === id ? updatedAppointment : apt))
        return true
      } else {
        throw new Error(data.message || 'Failed to update appointment')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      return false
    }
  }

  const deleteAppointment = async (id: number): Promise<boolean> => {
    try {
      const response = await fetch(`/api/appointments/${id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to delete appointment')
      }

      if (data.success) {
        // Remove the appointment from the list
        setAppointments(prev => prev.filter(apt => apt.id !== id))
        return true
      } else {
        throw new Error(data.message || 'Failed to delete appointment')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      return false
    }
  }

  useEffect(() => {
    fetchAppointments()
  }, [startDate, endDate])

  return {
    appointments,
    loading,
    error,
    refetch: fetchAppointments,
    createAppointment,
    updateAppointment,
    deleteAppointment
  }
}
