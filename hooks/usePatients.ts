"use client"

import * as React from "react"
import type { PatientFormData } from "@/lib/types/patient-schemas"

export type PatientSearchType = "phone" | "name" | "id"

export interface SearchCriteria {
  query: string
  searchType: PatientSearchType
}

export interface Patient {
  id: number | string
  firstName: string
  lastName: string
  phoneNumber: string
  lastVisitDate?: string | Date | null
  caseSheetStatus?: string | null
  email?: string
  address?: string
  dateOfBirth?: string | Date | null
}

export interface UsePatientsState {
  patients: Patient[]
  selectedPatient?: Patient
  loading: boolean
  error?: string
}

export interface UsePatientsApi {
  searchPatients: (criteria: SearchCriteria) => Promise<void>
  createPatient: (data: PatientFormData) => Promise<Patient | undefined>
  selectPatient: (patient: Patient | undefined) => void
  clearError: () => void
}

export interface UsePatientsOptions {
  searchFn?: (criteria: SearchCriteria, signal?: AbortSignal) => Promise<Patient[]>
  createFn?: (data: PatientFormData, signal?: AbortSignal) => Promise<Patient>
  basePath?: string // default /api/patients
}

export function usePatients(options: UsePatientsOptions = {}): UsePatientsState & UsePatientsApi {
  const { searchFn, createFn, basePath = "/api/patients" } = options

  const [patients, setPatients] = React.useState<Patient[]>([])
  const [selectedPatient, setSelectedPatient] = React.useState<Patient | undefined>(undefined)
  const [loading, setLoading] = React.useState<boolean>(false)
  const [error, setError] = React.useState<string | undefined>(undefined)

  const searchAbortRef = React.useRef<AbortController | null>(null)

  const defaultSearchFn = React.useCallback(async (criteria: SearchCriteria, signal?: AbortSignal) => {
    const params = new URLSearchParams({ searchType: criteria.searchType, query: criteria.query })
    const res = await fetch(`${basePath}?${params.toString()}`, { method: "GET", signal })
    if (!res.ok) {
      throw new Error("Failed to search patients")
    }
    const data = (await res.json()) as Patient[]
    return Array.isArray(data) ? data : []
  }, [basePath])

  const defaultCreateFn = React.useCallback(async (data: PatientFormData, signal?: AbortSignal) => {
    const res = await fetch(basePath, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
      signal,
    })
    if (!res.ok) {
      const text = await res.text().catch(() => "")
      throw new Error(text || "Failed to create patient")
    }
    const created = (await res.json()) as Patient
    return created
  }, [basePath])

  const clearError = React.useCallback(() => setError(undefined), [])

  const searchPatients = React.useCallback(async (criteria: SearchCriteria) => {
    if (searchAbortRef.current) {
      searchAbortRef.current.abort()
    }
    const controller = new AbortController()
    searchAbortRef.current = controller

    setLoading(true)
    setError(undefined)
    try {
      const impl = searchFn ?? defaultSearchFn
      const results = await impl(criteria, controller.signal)
      setPatients(results)
    } catch (e) {
      if ((e as any)?.name === "AbortError") return
      const message = e instanceof Error ? e.message : "Search failed"
      setError(message)
      setPatients([])
    } finally {
      setLoading(false)
      if (searchAbortRef.current === controller) {
        searchAbortRef.current = null
      }
    }
  }, [defaultSearchFn, searchFn])

  const createPatient = React.useCallback(async (data: PatientFormData) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = createFn ?? defaultCreateFn
      const created = await impl(data)
      setPatients((prev) => [created, ...prev])
      setSelectedPatient(created)
      return created
    } catch (e) {
      const message = e instanceof Error ? e.message : "Create patient failed"
      setError(message)
      return undefined
    } finally {
      setLoading(false)
    }
  }, [createFn, defaultCreateFn])

  const selectPatient = React.useCallback((patient: Patient | undefined) => {
    setSelectedPatient(patient)
  }, [])

  return {
    patients,
    selectedPatient,
    loading,
    error,
    searchPatients,
    createPatient,
    selectPatient,
    clearError,
  }
}
