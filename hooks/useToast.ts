"use client"

import { toast } from "sonner"

export type ToastVariant = "default" | "success" | "destructive"

export interface ToastConfig {
  title: string
  description?: string
  variant?: ToastVariant
  duration?: number
}

export function useToast() {
  const show = ({ title, description, variant = "default", duration = 2500 }: ToastConfig) => {
    if (variant === "success") {
      toast.success(title, { description, duration })
    } else if (variant === "destructive") {
      toast.error(title, { description, duration })
    } else {
      toast(title, { description, duration })
    }
  }

  const success = (title: string, options?: Omit<ToastConfig, "title" | "variant">) => {
    toast.success(title, { description: options?.description, duration: options?.duration })
  }

  const error = (title: string, options?: Omit<ToastConfig, "title" | "variant">) => {
    toast.error(title, { description: options?.description, duration: options?.duration })
  }

  const message = (title: string, options?: Omit<ToastConfig, "title" | "variant">) => {
    toast(title, { description: options?.description, duration: options?.duration })
  }

  return { show, success, error, message }
}


