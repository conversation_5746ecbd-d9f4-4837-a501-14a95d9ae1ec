"use client"

import * as React from "react"

// Minimal domain types for clinical workflow
export interface Tooth {
  id: number | string
  fdiNumber: number
  name?: string
  present?: boolean
}

export type TreatmentStatus = "pending" | "in-progress" | "completed"

export interface Finding {
  id: number | string
  toothId: number | string
  description: string
  severity?: "low" | "medium" | "high"
  createdAt?: string | Date
}

export interface Treatment {
  id: number | string
  toothId: number | string
  findingId?: number | string | null
  procedureName: string
  cost: number
  status: TreatmentStatus
  notes?: string
  createdAt?: string | Date
  completedAt?: string | Date | null
}

export interface CaseSheetWithTeeth {
  id: number | string
  patientId: number | string
  teeth: Tooth[]
}

export interface CreateFindingData {
  toothId: number | string
  description: string
  severity?: "low" | "medium" | "high"
}

export interface UpdateFindingData {
  description?: string
  severity?: "low" | "medium" | "high"
}

export interface CreateTreatmentData {
  toothId: number | string
  findingId?: number | string
  procedureName: string
  cost: number
  notes?: string
}

export interface UpdateTreatmentData {
  procedureName?: string
  cost?: number
  notes?: string
  status?: TreatmentStatus
}

export interface UseClinicalState {
  caseSheet?: CaseSheetWithTeeth
  selectedTooth?: Tooth
  findings: Finding[]
  treatments: Treatment[]
  loading: boolean
  error?: string
}

export interface UseClinicalApi {
  loadCaseSheet: (caseSheetId: number | string) => Promise<void>
  selectTooth: (tooth: Tooth | undefined) => void
  addFinding: (data: CreateFindingData) => Promise<Finding | undefined>
  updateFinding: (id: number | string, data: UpdateFindingData) => Promise<Finding | undefined>
  deleteFinding: (id: number | string) => Promise<boolean>
  addTreatment: (data: CreateTreatmentData) => Promise<Treatment | undefined>
  updateTreatment: (id: number | string, data: UpdateTreatmentData) => Promise<Treatment | undefined>
  deleteTreatment: (id: number | string) => Promise<boolean>
  clearError: () => void
}

export interface UseClinicalOptions {
  basePath?: string // default "/api/clinical"
  // Pluggable API functions
  fetchCaseSheetFn?: (caseSheetId: number | string, signal?: AbortSignal) => Promise<CaseSheetWithTeeth>
  fetchFindingsFn?: (caseSheetId: number | string, signal?: AbortSignal) => Promise<Finding[]>
  fetchTreatmentsFn?: (caseSheetId: number | string, signal?: AbortSignal) => Promise<Treatment[]>
  createFindingFn?: (data: CreateFindingData, signal?: AbortSignal) => Promise<Finding>
  updateFindingFn?: (id: number | string, data: UpdateFindingData, signal?: AbortSignal) => Promise<Finding>
  deleteFindingFn?: (id: number | string, signal?: AbortSignal) => Promise<void>
  createTreatmentFn?: (data: CreateTreatmentData, signal?: AbortSignal) => Promise<Treatment>
  updateTreatmentFn?: (id: number | string, data: UpdateTreatmentData, signal?: AbortSignal) => Promise<Treatment>
  deleteTreatmentFn?: (id: number | string, signal?: AbortSignal) => Promise<void>
}

export function useClinical(options: UseClinicalOptions = {}): UseClinicalState & UseClinicalApi {
  const {
    basePath = "/api/clinical",
    fetchCaseSheetFn,
    fetchFindingsFn,
    fetchTreatmentsFn,
    createFindingFn,
    updateFindingFn,
    deleteFindingFn,
    createTreatmentFn,
    updateTreatmentFn,
    deleteTreatmentFn,
  } = options

  const [caseSheet, setCaseSheet] = React.useState<CaseSheetWithTeeth | undefined>(undefined)
  const [selectedTooth, setSelectedTooth] = React.useState<Tooth | undefined>(undefined)
  const [findings, setFindings] = React.useState<Finding[]>([])
  const [treatments, setTreatments] = React.useState<Treatment[]>([])
  const [loading, setLoading] = React.useState<boolean>(false)
  const [error, setError] = React.useState<string | undefined>(undefined)

  const loadAbortRef = React.useRef<AbortController | null>(null)

  // Default API implementations
  const defaultFetchCaseSheet = React.useCallback(async (id: number | string, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/case-sheets/${id}`, { method: "GET", signal })
    if (!res.ok) throw new Error("Failed to load case sheet")
    return (await res.json()) as CaseSheetWithTeeth
  }, [basePath])

  const defaultFetchFindings = React.useCallback(async (id: number | string, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/case-sheets/${id}/findings`, { method: "GET", signal })
    if (!res.ok) throw new Error("Failed to load findings")
    return (await res.json()) as Finding[]
  }, [basePath])

  const defaultFetchTreatments = React.useCallback(async (id: number | string, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/case-sheets/${id}/treatments`, { method: "GET", signal })
    if (!res.ok) throw new Error("Failed to load treatments")
    return (await res.json()) as Treatment[]
  }, [basePath])

  const defaultCreateFinding = React.useCallback(async (data: CreateFindingData, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/findings`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
      signal,
    })
    if (!res.ok) throw new Error("Failed to create finding")
    return (await res.json()) as Finding
  }, [basePath])

  const defaultUpdateFinding = React.useCallback(async (id: number | string, data: UpdateFindingData, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/findings/${id}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
      signal,
    })
    if (!res.ok) throw new Error("Failed to update finding")
    return (await res.json()) as Finding
  }, [basePath])

  const defaultDeleteFinding = React.useCallback(async (id: number | string, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/findings/${id}`, { method: "DELETE", signal })
    if (!res.ok) throw new Error("Failed to delete finding")
  }, [basePath])

  const defaultCreateTreatment = React.useCallback(async (data: CreateTreatmentData, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/treatments`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
      signal,
    })
    if (!res.ok) throw new Error("Failed to create treatment")
    return (await res.json()) as Treatment
  }, [basePath])

  const defaultUpdateTreatment = React.useCallback(async (id: number | string, data: UpdateTreatmentData, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/treatments/${id}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
      signal,
    })
    if (!res.ok) throw new Error("Failed to update treatment")
    return (await res.json()) as Treatment
  }, [basePath])

  const defaultDeleteTreatment = React.useCallback(async (id: number | string, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/treatments/${id}`, { method: "DELETE", signal })
    if (!res.ok) throw new Error("Failed to delete treatment")
  }, [basePath])

  const clearError = React.useCallback(() => setError(undefined), [])

  const loadCaseSheet = React.useCallback(async (caseSheetId: number | string) => {
    if (loadAbortRef.current) {
      loadAbortRef.current.abort()
    }
    const controller = new AbortController()
    loadAbortRef.current = controller
    setLoading(true)
    setError(undefined)
    try {
      const cs = await (fetchCaseSheetFn ?? defaultFetchCaseSheet)(caseSheetId, controller.signal)
      setCaseSheet(cs)
      const [loadedFindings, loadedTreatments] = await Promise.all([
        (fetchFindingsFn ?? defaultFetchFindings)(caseSheetId, controller.signal),
        (fetchTreatmentsFn ?? defaultFetchTreatments)(caseSheetId, controller.signal),
      ])
      setFindings(loadedFindings)
      setTreatments(loadedTreatments)
      // Keep selected tooth if it exists in the new case sheet; otherwise clear
      setSelectedTooth((prev) => (prev && cs.teeth.some((t) => String(t.id) === String(prev.id)) ? prev : undefined))
    } catch (e) {
      if ((e as any)?.name === "AbortError") return
      const message = e instanceof Error ? e.message : "Failed to load clinical data"
      setError(message)
      setCaseSheet(undefined)
      setFindings([])
      setTreatments([])
      setSelectedTooth(undefined)
    } finally {
      setLoading(false)
      if (loadAbortRef.current === controller) {
        loadAbortRef.current = null
      }
    }
  }, [defaultFetchCaseSheet, defaultFetchFindings, defaultFetchTreatments, fetchCaseSheetFn, fetchFindingsFn, fetchTreatmentsFn])

  const selectTooth = React.useCallback((tooth: Tooth | undefined) => {
    setSelectedTooth(tooth)
  }, [])

  const addFinding = React.useCallback(async (data: CreateFindingData) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = createFindingFn ?? defaultCreateFinding
      const created = await impl(data)
      setFindings((prev) => [created, ...prev])
      return created
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to add finding"
      setError(message)
      return undefined
    } finally {
      setLoading(false)
    }
  }, [createFindingFn, defaultCreateFinding])

  const updateFinding = React.useCallback(async (id: number | string, data: UpdateFindingData) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = updateFindingFn ?? defaultUpdateFinding
      const updated = await impl(id, data)
      setFindings((prev) => prev.map((f) => (String(f.id) === String(id) ? { ...f, ...updated } : f)))
      return updated
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to update finding"
      setError(message)
      return undefined
    } finally {
      setLoading(false)
    }
  }, [defaultUpdateFinding, updateFindingFn])

  const deleteFinding = React.useCallback(async (id: number | string) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = deleteFindingFn ?? defaultDeleteFinding
      await impl(id)
      setFindings((prev) => prev.filter((f) => String(f.id) !== String(id)))
      // Also remove treatments that were linked to this finding if any
      setTreatments((prev) => prev.filter((t) => String(t.findingId ?? "") !== String(id)))
      return true
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to delete finding"
      setError(message)
      return false
    } finally {
      setLoading(false)
    }
  }, [defaultDeleteFinding, deleteFindingFn])

  const addTreatment = React.useCallback(async (data: CreateTreatmentData) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = createTreatmentFn ?? defaultCreateTreatment
      const created = await impl(data)
      setTreatments((prev) => [created, ...prev])
      return created
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to add treatment"
      setError(message)
      return undefined
    } finally {
      setLoading(false)
    }
  }, [createTreatmentFn, defaultCreateTreatment])

  const updateTreatment = React.useCallback(async (id: number | string, data: UpdateTreatmentData) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = updateTreatmentFn ?? defaultUpdateTreatment
      const updated = await impl(id, data)
      setTreatments((prev) => prev.map((t) => (String(t.id) === String(id) ? { ...t, ...updated } : t)))
      return updated
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to update treatment"
      setError(message)
      return undefined
    } finally {
      setLoading(false)
    }
  }, [defaultUpdateTreatment, updateTreatmentFn])

  const deleteTreatment = React.useCallback(async (id: number | string) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = deleteTreatmentFn ?? defaultDeleteTreatment
      await impl(id)
      setTreatments((prev) => prev.filter((t) => String(t.id) !== String(id)))
      return true
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to delete treatment"
      setError(message)
      return false
    } finally {
      setLoading(false)
    }
  }, [defaultDeleteTreatment, deleteTreatmentFn])

  return {
    caseSheet,
    selectedTooth,
    findings,
    treatments,
    loading,
    error,
    loadCaseSheet,
    selectTooth,
    addFinding,
    updateFinding,
    deleteFinding,
    addTreatment,
    updateTreatment,
    deleteTreatment,
    clearError,
  }
}
