"use client"

import * as React from "react"

export type InvoiceStatus = "draft" | "sent" | "paid" | "overdue"

export interface Invoice {
  id: number | string
  serial: string
  date: string | Date
  status: InvoiceStatus
  patientId: number | string
  totalAmount: number
  amountPaid: number
  balanceDue: number
}

export interface Payment {
  id: number | string
  invoiceId: number | string
  amount: number
  method: string
  date: string | Date
  notes?: string
}

export interface CreatePaymentData {
  invoiceId: number | string
  amount: number
  method: string
  date: string | Date
  notes?: string
}

export interface UseBillingState {
  invoices: Invoice[]
  selectedInvoice?: Invoice
  payments: Payment[]
  loading: boolean
  error?: string
}

export interface UseBillingApi {
  loadInvoices: () => Promise<void>
  loadInvoice: (invoiceId: number | string) => Promise<Invoice | undefined>
  loadPayments: (invoiceId: number | string) => Promise<void>
  selectInvoice: (invoice: Invoice | undefined) => void
  processPayment: (data: CreatePaymentData) => Promise<Payment | undefined>
  updateInvoice: (id: number | string, data: Partial<Invoice>) => Promise<Invoice | undefined>
  clearError: () => void
}

export interface UseBillingOptions {
  basePath?: string // default "/api/billing"
  fetchInvoicesFn?: (signal?: AbortSignal) => Promise<Invoice[]>
  fetchInvoiceFn?: (invoiceId: number | string, signal?: AbortSignal) => Promise<Invoice>
  fetchPaymentsFn?: (invoiceId: number | string, signal?: AbortSignal) => Promise<Payment[]>
  processPaymentFn?: (data: CreatePaymentData, signal?: AbortSignal) => Promise<Payment>
  updateInvoiceFn?: (id: number | string, data: Partial<Invoice>, signal?: AbortSignal) => Promise<Invoice>
}

export function useBilling(options: UseBillingOptions = {}): UseBillingState & UseBillingApi {
  const {
    basePath = "/api/billing",
    fetchInvoicesFn,
    fetchInvoiceFn,
    fetchPaymentsFn,
    processPaymentFn,
    updateInvoiceFn,
  } = options

  const [invoices, setInvoices] = React.useState<Invoice[]>([])
  const [selectedInvoice, setSelectedInvoice] = React.useState<Invoice | undefined>(undefined)
  const [payments, setPayments] = React.useState<Payment[]>([])
  const [loading, setLoading] = React.useState<boolean>(false)
  const [error, setError] = React.useState<string | undefined>(undefined)

  const listAbortRef = React.useRef<AbortController | null>(null)

  const defaultFetchInvoices = React.useCallback(async (signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/invoices`, { method: "GET", signal })
    if (!res.ok) throw new Error("Failed to load invoices")
    return (await res.json()) as Invoice[]
  }, [basePath])

  const defaultFetchInvoice = React.useCallback(async (invoiceId: number | string, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/invoices/${invoiceId}`, { method: "GET", signal })
    if (!res.ok) throw new Error("Failed to load invoice")
    return (await res.json()) as Invoice
  }, [basePath])

  const defaultFetchPayments = React.useCallback(async (invoiceId: number | string, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/invoices/${invoiceId}/payments`, { method: "GET", signal })
    if (!res.ok) throw new Error("Failed to load payments")
    return (await res.json()) as Payment[]
  }, [basePath])

  const defaultProcessPayment = React.useCallback(async (data: CreatePaymentData, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/invoices/${data.invoiceId}/payments`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
      signal,
    })
    if (!res.ok) throw new Error("Failed to process payment")
    return (await res.json()) as Payment
  }, [basePath])

  const defaultUpdateInvoice = React.useCallback(async (id: number | string, data: Partial<Invoice>, signal?: AbortSignal) => {
    const res = await fetch(`${basePath}/invoices/${id}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
      signal,
    })
    if (!res.ok) throw new Error("Failed to update invoice")
    return (await res.json()) as Invoice
  }, [basePath])

  const clearError = React.useCallback(() => setError(undefined), [])

  const loadInvoices = React.useCallback(async () => {
    if (listAbortRef.current) listAbortRef.current.abort()
    const controller = new AbortController()
    listAbortRef.current = controller

    setLoading(true)
    setError(undefined)
    try {
      const impl = fetchInvoicesFn ?? defaultFetchInvoices
      const data = await impl(controller.signal)
      setInvoices(Array.isArray(data) ? data : [])
    } catch (e) {
      if ((e as any)?.name === "AbortError") return
      const message = e instanceof Error ? e.message : "Failed to load invoices"
      setError(message)
      setInvoices([])
    } finally {
      setLoading(false)
      if (listAbortRef.current === controller) listAbortRef.current = null
    }
  }, [defaultFetchInvoices, fetchInvoicesFn])

  const loadInvoice = React.useCallback(async (invoiceId: number | string) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = fetchInvoiceFn ?? defaultFetchInvoice
      const inv = await impl(invoiceId)
      setSelectedInvoice(inv)
      return inv
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to load invoice"
      setError(message)
      setSelectedInvoice(undefined)
      return undefined
    } finally {
      setLoading(false)
    }
  }, [defaultFetchInvoice, fetchInvoiceFn])

  const loadPayments = React.useCallback(async (invoiceId: number | string) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = fetchPaymentsFn ?? defaultFetchPayments
      const data = await impl(invoiceId)
      setPayments(Array.isArray(data) ? data : [])
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to load payments"
      setError(message)
      setPayments([])
    } finally {
      setLoading(false)
    }
  }, [defaultFetchPayments, fetchPaymentsFn])

  const selectInvoice = React.useCallback((invoice: Invoice | undefined) => {
    setSelectedInvoice(invoice)
  }, [])

  const processPayment = React.useCallback(async (data: CreatePaymentData) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = processPaymentFn ?? defaultProcessPayment
      const created = await impl(data)
      setPayments((prev) => [created, ...prev])
      setInvoices((prev) =>
        prev.map((inv) =>
          String(inv.id) === String(data.invoiceId)
            ? {
                ...inv,
                amountPaid: inv.amountPaid + created.amount,
                balanceDue: Math.max(0, inv.totalAmount - (inv.amountPaid + created.amount)),
                status: Math.max(0, inv.totalAmount - (inv.amountPaid + created.amount)) === 0 ? "paid" : inv.status,
              }
            : inv
        )
      )
      // Keep selected invoice in sync
      setSelectedInvoice((prev) =>
        prev && String(prev.id) === String(data.invoiceId)
          ? {
              ...prev,
              amountPaid: prev.amountPaid + created.amount,
              balanceDue: Math.max(0, prev.totalAmount - (prev.amountPaid + created.amount)),
              status: Math.max(0, prev.totalAmount - (prev.amountPaid + created.amount)) === 0 ? "paid" : prev.status,
            }
          : prev
      )
      return created
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to process payment"
      setError(message)
      return undefined
    } finally {
      setLoading(false)
    }
  }, [defaultProcessPayment, processPaymentFn])

  const updateInvoice = React.useCallback(async (id: number | string, data: Partial<Invoice>) => {
    setLoading(true)
    setError(undefined)
    try {
      const impl = updateInvoiceFn ?? defaultUpdateInvoice
      const updated = await impl(id, data)
      setInvoices((prev) => prev.map((inv) => (String(inv.id) === String(id) ? { ...inv, ...updated } : inv)))
      setSelectedInvoice((prev) => (prev && String(prev.id) === String(id) ? { ...prev, ...updated } : prev))
      return updated
    } catch (e) {
      const message = e instanceof Error ? e.message : "Failed to update invoice"
      setError(message)
      return undefined
    } finally {
      setLoading(false)
    }
  }, [defaultUpdateInvoice, updateInvoiceFn])

  return {
    invoices,
    selectedInvoice,
    payments,
    loading,
    error,
    loadInvoices,
    loadInvoice,
    loadPayments,
    selectInvoice,
    processPayment,
    updateInvoice,
    clearError,
  }
}
