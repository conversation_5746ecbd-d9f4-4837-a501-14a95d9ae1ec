"use client"

import * as React from "react"

export function useMediaQuery(query: string): boolean {
  const getMatch = React.useCallback(() => {
    if (typeof window === "undefined") return false
    return window.matchMedia(query).matches
  }, [query])

  const [matches, setMatches] = React.useState<boolean>(getMatch)

  React.useEffect(() => {
    if (typeof window === "undefined") return
    const mediaQueryList = window.matchMedia(query)
    const handler = () => setMatches(mediaQueryList.matches)
    handler()
    mediaQueryList.addEventListener("change", handler)
    return () => mediaQueryList.removeEventListener("change", handler)
  }, [query])

  return matches
}

export default useMediaQuery


