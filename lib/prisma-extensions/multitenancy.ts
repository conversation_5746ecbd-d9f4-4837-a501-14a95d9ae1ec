import { Prisma } from "@prisma/client";
import { TenantContextManager } from "../tenant-context";

// Re-export for backward compatibility
export { TenantContext } from "../tenant-context";

// Type for bypass operations
type BypassTenantOperation<T> = () => Promise<T>;

export const multitenancyExtension = Prisma.defineExtension({
  name: "multitenancy",
  client: {
    // Method to bypass tenant filtering for administrative operations
    async bypassTenant<T>(operation: BypassTenantOperation<T>): Promise<T> {
      const originalContext = TenantContextManager.getCurrentContext();
      TenantContextManager.clearGlobalContext(); // Temporarily disable tenant filtering
      try {
        return await operation();
      } finally {
        if (originalContext) {
          TenantContextManager.setGlobalContext(originalContext); // Restore original tenant context
        }
      }
    },

    // Helper method to get current tenant ID
    getCurrentTenantId(): string | null {
      return TenantContextManager.getCurrentTenantId();
    },
  },
  query: {
    // Apply tenant filtering to all models that have tenantId field
    $allModels: {
      // Read operations - automatically filter by tenant
      async findMany({ model, args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        // Skip tenant filtering if no tenant is set (bypass mode) or if explicitly bypassed
        if (!tenantId) {
          return query(args);
        }

        // Add tenant filter to where clause
        const where = args.where || {};
        const enhancedArgs = {
          ...args,
          where: {
            ...where,
            tenantId,
          },
        };
        return query(enhancedArgs as any);
      },

      async findFirst({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        const where = args.where || {};
        return query({
          ...args,
          where: {
            ...where,
            tenantId,
          },
        } as any);
      },

      async findUnique({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        const where = args.where || {};
        return query({
          ...args,
          where: {
            ...where,
            tenantId,
          },
        } as any);
      },

      async findUniqueOrThrow({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        const where = args.where || {};
        return query({
          ...args,
          where: {
            ...where,
            tenantId,
          },
        } as any);
      },

      // Write operations - automatically inject tenant ID
      async create({ model, args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        // Skip tenant injection if no tenant is set (bypass mode)
        if (!tenantId) {
          return query(args);
        }

        // Inject tenant ID into data
        const data = args.data || {};
        
        // If tenantId is already provided, don't override it
        if ((data as any).tenantId) {
          return query(args);
        }

        // If tenant relationship is already provided, don't override it
        if ((data as any).tenant) {
          return query(args);
        }

        const enhancedArgs = {
          ...args,
          data: {
            ...data,
            tenantId,
          },
        };
        return query(enhancedArgs as any);
      },

      async createMany({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        // Inject tenant ID into all data items
        const data = Array.isArray(args.data) ? args.data : [args.data];
        return query({
          ...args,
          data: data.map((item) => ({
            ...item,
            tenantId,
          })),
        } as any);
      },

      async update({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        // Add tenant filter to where clause
        const where = args.where || {};
        return query({
          ...args,
          where: {
            ...where,
            tenantId,
          },
        } as any);
      },

      async updateMany({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        // Add tenant filter to where clause
        const where = args.where || {};
        return query({
          ...args,
          where: {
            ...where,
            tenantId,
          },
        } as any);
      },

      async upsert({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        // Add tenant filter to where clause and inject into create data
        const where = args.where || {};
        const create = args.create || {};
        return query({
          ...args,
          where: {
            ...where,
            tenantId,
          },
          create: {
            ...create,
            tenantId,
          },
        } as any);
      },

      async delete({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        // Add tenant filter to where clause
        const where = args.where || {};
        return query({
          ...args,
          where: {
            ...where,
            tenantId,
          },
        } as any);
      },

      async deleteMany({ args, query }) {
        const tenantId = TenantContextManager.getCurrentTenantId();
        
        if (!tenantId) {
          return query(args);
        }

        // Add tenant filter to where clause
        const where = args.where || {};
        return query({
          ...args,
          where: {
            ...where,
            tenantId,
          },
        } as any);
      },
    },
  },
});