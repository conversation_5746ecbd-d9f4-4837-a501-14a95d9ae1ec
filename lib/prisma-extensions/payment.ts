import { Prisma, PaymentMethod, PaymentStatus, InvoiceStatus, Invoice, Payment } from "@prisma/client";

export const paymentExtension = Prisma.defineExtension({
  name: "paymentExtension",
  model: {
    payment: {
      // Create a payment and update invoice (if provided)
      async processPayment(
        patientId: number,
        invoiceId: number | null | undefined,
        amount: number | string,
        paymentMethod: PaymentMethod,
      ) {
        const numericAmount = typeof amount === "string" ? Number(amount) : amount;
        if (!Number.isFinite(numericAmount)) {
          throw new Error("amount must be a finite number or numeric string");
        }

        const prisma = Prisma.getExtensionContext(this) as unknown as {
          patient: { findUnique: (args: { where: { id: number } }) => Promise<{ id: number; tenantId: number } | null> };
          invoice: {
            findUnique: (args: { where: { id: number } }) => Promise<Invoice | null>;
            update: (args: { where: { id: number }; data: { amountPaid: number; balanceDue: number; status: InvoiceStatus } }) => Promise<Invoice>;
          };
        };

        // Verify patient and infer tenant
        const patient = await prisma.patient.findUnique({ where: { id: patientId } });
        if (!patient) {
          throw new Error("Patient not found");
        }

        // Optional invoice validation
        let invoice: Invoice | null = null;
        if (invoiceId) {
          invoice = await prisma.invoice.findUnique({ where: { id: invoiceId } });
          if (!invoice) {
            throw new Error("Invoice not found");
          }
          if (invoice.patientId !== patientId) {
            throw new Error("Invoice does not belong to the specified patient");
          }
          if (invoice.tenantId !== patient.tenantId) {
            throw new Error("Tenant mismatch between patient and invoice");
          }
        }

        // Create payment
        const payment = await (this as any).create({
          data: {
            tenantId: patient.tenantId,
            patientId,
            invoiceId: invoice ? invoice.id : undefined,
            amount: numericAmount,
            paymentDate: new Date(),
            paymentMethod,
            status: PaymentStatus.COMPLETED,
          },
        });

        // Update invoice aggregates if linked
        if (invoice) {
          const payments = await (this as any).findMany({
            where: { invoiceId: invoice.id, status: PaymentStatus.COMPLETED },
          });

          const totalPaidNumber = payments.reduce((sum: number, p: Payment) => sum + Number(p.amount), 0);
          const newBalanceNumber = Number(invoice.totalAmount) - totalPaidNumber;

          let newStatus = invoice.status;
          if (totalPaidNumber >= Number(invoice.totalAmount)) {
            newStatus = InvoiceStatus.PAID;
          } else if (invoice.status === InvoiceStatus.DRAFT) {
            newStatus = InvoiceStatus.SENT;
          }

          await prisma.invoice.update({
            where: { id: invoice.id },
            data: {
              amountPaid: totalPaidNumber,
              balanceDue: newBalanceNumber,
              status: newStatus,
            },
          });
        }

        return payment;
      },

      // List payments for a given invoice
      async findByInvoice(invoiceId: number) {
        return (this as any).findMany({
          where: { invoiceId },
          orderBy: { paymentDate: "desc" },
        });
      },

      // Calculate invoice balance from completed payments
      async calculateInvoiceBalance(invoiceId: number) {
        const prisma = Prisma.getExtensionContext(this) as unknown as {
          invoice: {
            findUnique: (args: { where: { id: number }; include: { payments: { where: { status: PaymentStatus } } } }) => Promise<Invoice & { payments: Array<{ amount: number }> } | null>;
          };
        };

        const invoice = await prisma.invoice.findUnique({
          where: { id: invoiceId },
          include: { payments: { where: { status: PaymentStatus.COMPLETED } } },
        });

        if (!invoice) {
          throw new Error("Invoice not found");
        }

        const totalPaidNumber = invoice.payments.reduce((sum, p) => sum + Number(p.amount), 0);
        const balanceDueNumber = Number(invoice.totalAmount) - totalPaidNumber;

        return {
          invoiceId: invoice.id,
          totalAmount: Number(invoice.totalAmount),
          amountPaid: totalPaidNumber,
          balanceDue: balanceDueNumber,
          status: balanceDueNumber <= 0 ? InvoiceStatus.PAID : invoice.status,
        };
      },
    },
  },
});