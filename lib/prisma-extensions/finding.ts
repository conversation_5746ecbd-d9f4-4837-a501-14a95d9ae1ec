import { Prisma, PrismaClient } from "@prisma/client";

export const findingExtension = Prisma.defineExtension({
  name: "findingExtension",
  model: {
    finding: {
      // Create finding with tenant isolation inferred from the tooth
      async createFinding(toothId: number, description: string, recordedById?: number) {
        if (!description || !description.trim()) {
          throw new Error("Description is required");
        }

        // Look up tooth to infer tenantId and validate existence
        const prisma = Prisma.getExtensionContext(this) as unknown as PrismaClient;
        const tooth = await prisma.tooth.findFirst({ where: { id: toothId } });
        if (!tooth) {
          throw new Error("Tooth not found");
        }

        try {
          return await (this as any).create({
            data: {
              tenantId: tooth.tenantId,
              toothId,
              description: description.trim(),
              recordedById,
              ...(recordedById ? { createdById: recordedById } : {}),
            },
          });
        } catch (err: unknown) {
          // Surface underlying issue to caller for easier debugging
          const errorMessage = err instanceof Error ? err.message : String(err);
          throw new Error(`createFinding failed: ${errorMessage}`);
        }
      },

      // Find a finding with its treatments included
      async findWithTreatments(findingId: number) {
        return (this as any).findUnique({
          where: { id: findingId },
          include: {
            treatments: true,
          },
        });
      },

      // Update description text for a finding
      async updateDescription(findingId: number, description: string, updatedById?: number) {
        if (!description || !description.trim()) {
          throw new Error("Description is required");
        }

        // Ensure the finding exists
        const existing = await (this as any).findUnique({ where: { id: findingId } });
        if (!existing) {
          throw new Error("Finding not found");
        }

        return (this as any).update({
          where: { id: findingId },
          data: {
            description: description.trim(),
            ...(updatedById ? { updatedById } : {}),
          },
        });
      },
    },
  },
});