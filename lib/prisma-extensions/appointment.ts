import { Prisma, AppointmentType, AppointmentStatus } from "@prisma/client";

interface CreateAppointmentInput {
  patientId: number;
  appointmentDate: Date;
  durationMinutes?: number;
  appointmentType?: AppointmentType;
  status?: AppointmentStatus;
  primaryProviderId?: number;
  notes?: string;
  tenantId: number;
  createdById?: number;
}

export const appointmentExtension = Prisma.defineExtension({
  name: "appointmentExtension",
  model: {
    appointment: {
      // Create appointment with basic validation
      async createAppointment(data: CreateAppointmentInput) {
        const durationMinutes = data.durationMinutes || 60;

        return (this as any).create({
          data: {
            ...data,
            durationMinutes,
            status: data.status || AppointmentStatus.SCHEDULED,
            appointmentType: data.appointmentType || AppointmentType.CONSULTATION,
          },
        });
      },

      // Get appointments for a specific date range
      async getAppointmentsByDateRange(
        tenantId: number,
        startDate: Date,
        endDate: Date,
        providerId?: number
      ) {
        const where: {
          tenantId: number;
          appointmentDate: { gte: Date; lte: Date };
          primaryProviderId?: number;
        } = {
          tenantId,
          appointmentDate: {
            gte: startDate,
            lte: endDate,
          },
        };

        if (providerId) {
          where.primaryProviderId = providerId;
        }

        return (this as any).findMany({
          where,
          include: {
            patient: true,
            primaryProvider: true,
          },
          orderBy: {
            appointmentDate: 'asc',
          },
        });
      },

      // Get today's appointments
      async getTodaysAppointments(tenantId: number, providerId?: number) {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

        return (this as any).getAppointmentsByDateRange(tenantId, startOfDay, endOfDay, providerId);
      },

      // Get upcoming appointments for a patient
      async getUpcomingAppointmentsForPatient(tenantId: number, patientId: number, limit: number = 10) {
        const now = new Date();
        
        return (this as any).findMany({
          where: {
            tenantId,
            patientId,
            appointmentDate: {
              gte: now,
            },
            status: AppointmentStatus.SCHEDULED,
          },
          include: {
            primaryProvider: true,
          },
          orderBy: {
            appointmentDate: 'asc',
          },
          take: limit,
        });
      },

      // Get appointments by status
      async getAppointmentsByStatus(tenantId: number, status: AppointmentStatus) {
        return (this as any).findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            patient: true,
            primaryProvider: true,
          },
          orderBy: {
            appointmentDate: 'asc',
          },
        });
      },

      // Basic appointment validation
      async validateAppointment(data: Partial<CreateAppointmentInput & { id?: number }>) {
        const errors: string[] = [];

        // Validate required fields
        if (!data.patientId) {
          errors.push('Patient is required');
        }

        if (!data.appointmentDate) {
          errors.push('Appointment date is required');
        }

        if (!data.tenantId) {
          errors.push('Tenant ID is required');
        }

        // Validate appointment date is not in the past (for new appointments)
        if (data.appointmentDate && !data.id) {
          const now = new Date();
          if (data.appointmentDate < now) {
            errors.push('Appointment date cannot be in the past');
          }
        }

        // Validate duration
        if (data.durationMinutes && (data.durationMinutes < 15 || data.durationMinutes > 480)) {
          errors.push('Duration must be between 15 minutes and 8 hours');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Update appointment status
      async updateAppointmentStatus(
        appointmentId: number,
        newStatus: AppointmentStatus,
        updatedById?: number
      ) {
        const appointment = await (this as any).findUnique({
          where: { id: appointmentId },
        });

        if (!appointment) {
          throw new Error('Appointment not found');
        }

        return (this as any).update({
          where: { id: appointmentId },
          data: {
            status: newStatus,
            updatedById,
          },
        });
      },

      // Reschedule appointment to new date/time
      async rescheduleAppointment(
        appointmentId: number,
        newAppointmentDate: Date,
        newDurationMinutes?: number,
        updatedById?: number
      ) {
        const appointment = await (this as any).findUnique({
          where: { id: appointmentId },
        });

        if (!appointment) {
          throw new Error('Appointment not found');
        }

        if (appointment.status === AppointmentStatus.COMPLETED) {
          throw new Error('Cannot reschedule completed appointments');
        }

        const durationMinutes = newDurationMinutes || appointment.durationMinutes;

        // Validate the new time slot
        const validation = await (this as any).validateAppointment({
          id: appointmentId,
          tenantId: appointment.tenantId,
          patientId: appointment.patientId,
          appointmentDate: newAppointmentDate,
          durationMinutes,
          primaryProviderId: appointment.primaryProviderId || undefined,
        });

        if (!validation.isValid) {
          throw new Error(`Cannot reschedule: ${validation.errors.join(', ')}`);
        }

        return (this as any).update({
          where: { id: appointmentId },
          data: {
            appointmentDate: newAppointmentDate,
            durationMinutes,
            updatedById,
          },
        });
      },
    },
  },
  result: {
    appointment: {
      // Format duration for display
      durationDisplay: {
        needs: { durationMinutes: true },
        compute(appointment) {
          const hours = Math.floor(appointment.durationMinutes / 60);
          const minutes = appointment.durationMinutes % 60;
          
          if (hours === 0) {
            return `${minutes}m`;
          } else if (minutes === 0) {
            return `${hours}h`;
          } else {
            return `${hours}h ${minutes}m`;
          }
        },
      },

      // Check if appointment is today
      isToday: {
        needs: { appointmentDate: true },
        compute(appointment) {
          const today = new Date();
          const appointmentDate = new Date(appointment.appointmentDate);
          
          return (
            appointmentDate.getDate() === today.getDate() &&
            appointmentDate.getMonth() === today.getMonth() &&
            appointmentDate.getFullYear() === today.getFullYear()
          );
        },
      },

      // Get status color for UI
      statusColor: {
        needs: { status: true },
        compute(appointment) {
          switch (appointment.status) {
            case AppointmentStatus.SCHEDULED:
              return 'blue';
            case AppointmentStatus.COMPLETED:
              return 'green';
            case AppointmentStatus.CANCELLED:
              return 'gray';
            default:
              return 'gray';
          }
        },
      },
    },
  },
});