import { put, del, type PutBlobResult } from '@vercel/blob';

// Core utilities for Vercel Blob operations
export interface BlobUploadOptions {
  access: 'public';
  addRandomSuffix?: boolean;
  cacheControlMaxAge?: number;
  multipart?: boolean;
  onUploadProgress?: (event: { loaded: number; total: number; percentage: number }) => void;
  allowOverwrite?: boolean;
}

export class BlobService {
  // Upload file with tenant isolation
  static async uploadFile(
    file: File | Buffer | ReadableStream, 
    pathname: string, 
    tenantId: number,
    options: Partial<BlobUploadOptions> = {}
  ): Promise<PutBlobResult> {
    const tenantPath = this.generatePath(tenantId, pathname);
    
    return await put(tenantPath, file, {
      access: "public",
      addRandomSuffix: false, // We control naming
      multipart: file instanceof File && file.size > 20 * 1024 * 1024, // Use multipart for files > 20MB
      allowOverwrite: true, // Allow replacing existing files
      ...options,
    });
  }
  
  // Delete file by URL
  static async deleteFile(url: string): Promise<void> {
    await del(url);
  }
  
  // Generate tenant-specific path
  static generatePath(
    tenantId: number, 
    pathname: string
  ): string {
    // Remove leading slash if present
    const cleanPathname = pathname.startsWith('/') ? pathname.slice(1) : pathname;
    return `tenants/${tenantId}/${cleanPathname}`;
  }

  // Generate unique filename with tenant prefix and timestamp
  static generateFilename(
    tenantId: number,
    originalName: string,
    type: 'logos' | 'promotions/banners' | 'promotions/previous-work' | 'promotions/general'
  ): string {
    const timestamp = Date.now();
    const extension = originalName.split('.').pop() || 'bin';
    const filename = `${tenantId}-${timestamp}.${extension}`;
    return `${type}/${filename}`;
  }

  // Check if URL is a blob URL
  static isBlobUrl(url: string): boolean {
    return url.includes('blob.vercel-storage.com');
  }

  // Extract blob pathname from URL for use with del()
  static extractPathnameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname;
    } catch {
      return url;
    }
  }

  // Get file extension from MIME type
  static getExtensionFromMime(mime: string): string {
    const map: Record<string, string> = {
      "image/jpeg": "jpg",
      "image/jpg": "jpg", 
      "image/png": "png",
      "image/webp": "webp",
      "image/gif": "gif",
      "video/mp4": "mp4",
      "video/webm": "webm",
      "video/ogg": "ogv",
      "video/quicktime": "mov",
    };
    return map[mime] || "bin";
  }

  // Create filename from file with fallback extension handling
  static createFilename(file: File, tenantId: number, type: 'logos' | 'promotions/banners' | 'promotions/previous-work' | 'promotions/general'): string {
    const origName = file.name || "upload";
    const extFromName = origName.includes(".") ? origName.split(".").pop() || "" : "";
    const ext = extFromName || this.getExtensionFromMime(file.type);
    return this.generateFilename(tenantId, `file.${ext}`, type);
  }
}
