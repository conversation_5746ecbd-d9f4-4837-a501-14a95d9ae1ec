import prisma from "./prisma"
import { Decimal } from "@prisma/client/runtime/library"

// Define specific types for report data structures
export interface ReportQuery {
  tenantId: number
  dateRange: { start: Date; end: Date }
  filters?: Record<string, string | number | boolean>
}

export interface ReportDataItem {
  [key: string]: string | number | boolean | Date | null | undefined | object
}

export interface ReportResult {
  data: ReportDataItem[]
  metadata: {
    totalRecords: number
    generatedAt: Date
    dateRange: { start: Date; end: Date }
  }
}

// Payment-related types
export interface PaymentWithInvoice {
  id: number
  amount: Decimal
  paymentDate: Date
  paymentMethod: string
  status: string
  invoiceId?: number
  invoice?: {
    serial?: string
    patient?: {
      firstName: string
      lastName: string
    }
  }
}

// Invoice-related types
export interface InvoiceWithPatient {
  id: number
  serial: string
  invoiceDate: Date
  totalAmount: Decimal
  amountPaid: Decimal
  balanceDue: Decimal
  status: string
  patient: {
    firstName: string
    lastName: string
  }
}

// Appointment-related types
export interface AppointmentWithDetails {
  id: number
  appointmentDate: Date
  appointmentType: string
  status: string
  durationMinutes: number
  patient: {
    firstName: string
    lastName: string
  }
  primaryProvider?: {
    firstName: string
    lastName: string
  }
}

// Patient-related types
export interface PatientWithAppointments {
  id: number
  firstName: string
  lastName: string
  dateOfBirth?: Date
  createdAt: Date
  appointments: Array<{
    id: number
    appointmentDate: Date
  }>
}

// Treatment-related types
export interface TreatmentWithDetails {
  id: number
  procedureName: string
  cost: Decimal
  status: string
  completedDate?: Date
  finding?: {
    tooth?: {
      toothNumber: number
    }
  }
  completedBy?: {
    firstName: string
    lastName: string
  }
}

export type ReportType = "revenue-summary" | "accounts-receivable" | "appointment-utilization" | "patient-demographics" | "treatment-completion"

export class ReportService {
  static async generateRevenueSummary(query: ReportQuery): Promise<ReportResult> {
    const { tenantId, dateRange } = query

    // Use database aggregation for better performance
    const [payments, paymentMethodBreakdown, totalRevenue] = await Promise.all([
      // Individual payments for detail view (limit to prevent memory issues)
      prisma.payment.findMany({
        where: {
          tenantId,
          paymentDate: {
            gte: dateRange.start,
            lte: dateRange.end
          },
          status: "COMPLETED"
        },
        include: {
          invoice: {
            include: {
              patient: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        },
        orderBy: {
          paymentDate: "desc"
        },
        take: 1000 // Limit to first 1000 records for performance
      }),

      // Aggregated data by payment method
      prisma.payment.groupBy({
        by: ['paymentMethod'],
        where: {
          tenantId,
          paymentDate: {
            gte: dateRange.start,
            lte: dateRange.end
          },
          status: "COMPLETED"
        },
        _sum: {
          amount: true
        },
        _count: {
          id: true
        }
      }),

      // Total revenue calculation
      prisma.payment.aggregate({
        where: {
          tenantId,
          paymentDate: {
            gte: dateRange.start,
            lte: dateRange.end
          },
          status: "COMPLETED"
        },
        _sum: {
          amount: true
        },
        _count: {
          id: true
        }
      })
    ])

    const data = payments.map((payment) => ({
      date: payment.paymentDate.toISOString().split('T')[0],
      patientName: payment.invoice?.patient ? `${payment.invoice.patient.firstName} ${payment.invoice.patient.lastName}` : 'Unknown',
      amount: Number(payment.amount),
      method: payment.paymentMethod,
      invoiceId: payment.invoice?.serial || payment.invoiceId?.toString() || 'N/A'
    }))

    // Convert aggregated data to object format
    const byMethod = paymentMethodBreakdown.reduce((acc: Record<string, number>, item) => {
      acc[item.paymentMethod] = Number(item._sum.amount || 0)
      return acc
    }, {} as Record<string, number>)

    return {
      data: [
        ...data,
        { separator: true },
        { summary: "Payment Method Breakdown", ...byMethod },
        { summary: "Total Revenue", total: Number(totalRevenue._sum.amount || 0) }
      ],
      metadata: {
        totalRecords: totalRevenue._count.id || 0,
        generatedAt: new Date(),
        dateRange
      }
    }
  }

  static async generateAccountsReceivable(query: ReportQuery): Promise<ReportResult> {
    const { tenantId } = query

    // Get all invoices with outstanding balances (including DRAFT, SENT, PARTIALLY_PAID, and OVERDUE)
    const pendingInvoices = await prisma.invoice.findMany({
      where: {
        tenantId,
        status: {
          in: ["DRAFT", "SENT", "PARTIALLY_PAID", "OVERDUE"]
        },
        balanceDue: {
          gt: 0
        }
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        invoiceDate: "asc" // Oldest first for aging report
      }
    })

    // Calculate aging and categorize invoices
    const currentDate = new Date()
    const agingBuckets = {
      "0-30 days": [] as any[],
      "31-60 days": [] as any[],
      "61-90 days": [] as any[],
      "90+ days": [] as any[]
    }
    
    const agingTotals = {
      "0-30 days": 0,
      "31-60 days": 0,
      "61-90 days": 0,
      "90+ days": 0
    }

    const processedInvoices = pendingInvoices.map((invoice) => {
      const daysOutstanding = Math.floor((currentDate.getTime() - new Date(invoice.invoiceDate).getTime()) / (1000 * 60 * 60 * 24))
      const balanceDue = Number(invoice.balanceDue)
      
      const invoiceData = {
        invoiceId: invoice.serial,
        patientName: `${invoice.patient.firstName} ${invoice.patient.lastName}`,
        invoiceDate: invoice.invoiceDate.toISOString().split('T')[0],
        totalAmount: Number(invoice.totalAmount),
        amountPaid: Number(invoice.amountPaid),
        balanceDue: balanceDue,
        daysOutstanding,
        agingPeriod: "",
        status: invoice.status
      }

      // Categorize into aging buckets
      if (daysOutstanding <= 30) {
        invoiceData.agingPeriod = "0-30 days"
        agingBuckets["0-30 days"].push(invoiceData)
        agingTotals["0-30 days"] += balanceDue
      } else if (daysOutstanding <= 60) {
        invoiceData.agingPeriod = "31-60 days"
        agingBuckets["31-60 days"].push(invoiceData)
        agingTotals["31-60 days"] += balanceDue
      } else if (daysOutstanding <= 90) {
        invoiceData.agingPeriod = "61-90 days"
        agingBuckets["61-90 days"].push(invoiceData)
        agingTotals["61-90 days"] += balanceDue
      } else {
        invoiceData.agingPeriod = "90+ days"
        agingBuckets["90+ days"].push(invoiceData)
        agingTotals["90+ days"] += balanceDue
      }

      return invoiceData
    })

    // Sort by days outstanding (oldest first) for better aging analysis
    processedInvoices.sort((a, b) => b.daysOutstanding - a.daysOutstanding)

    const totalReceivable = Object.values(agingTotals).reduce((sum, amount) => sum + amount, 0)

    // Build data array with aging sections
    const data: any[] = []
    
    // Add invoices grouped by aging period
    Object.entries(agingBuckets).forEach(([period, invoices]) => {
      if (invoices.length > 0) {
        // Add section header
        data.push({ agingHeader: period, count: invoices.length, total: agingTotals[period as keyof typeof agingTotals] })
        // Add invoices in this aging period
        data.push(...invoices)
        // Add separator between aging periods
        data.push({ separator: true })
      }
    })

    // Add summary totals
    data.push({ summary: "Aging Breakdown", ...agingTotals })
    data.push({ summary: "Total Outstanding", amount: totalReceivable })

    return {
      data,
      metadata: {
        totalRecords: processedInvoices.length,
        generatedAt: new Date(),
        dateRange: query.dateRange
      }
    }
  }

  static async generateAppointmentUtilization(query: ReportQuery): Promise<ReportResult> {
    const { tenantId, dateRange } = query

    const [appointments, statusBreakdown, totalStats] = await Promise.all([
      // Individual appointments for detail view (limited for performance)
      prisma.appointment.findMany({
        where: {
          tenantId,
          appointmentDate: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        },
        include: {
          patient: {
            select: {
              firstName: true,
              lastName: true
            }
          },
          primaryProvider: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        },
        orderBy: {
          appointmentDate: "desc"
        },
        take: 1000 // Limit for performance
      }),

      // Aggregated status breakdown
      prisma.appointment.groupBy({
        by: ['status'],
        where: {
          tenantId,
          appointmentDate: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        },
        _count: {
          id: true
        }
      }),

      // Total appointment count
      prisma.appointment.aggregate({
        where: {
          tenantId,
          appointmentDate: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        },
        _count: {
          id: true
        }
      })
    ])

    const data = appointments.map((apt) => ({
      date: apt.appointmentDate.toISOString().split('T')[0],
      patientName: `${apt.patient.firstName} ${apt.patient.lastName}`,
      type: apt.appointmentType,
      status: apt.status,
      provider: apt.primaryProvider ? `${apt.primaryProvider.firstName} ${apt.primaryProvider.lastName}` : 'Unassigned',
      durationMinutes: apt.durationMinutes
    }))

    // Convert aggregated status data to object format
    const statusStats = statusBreakdown.reduce((acc: Record<string, number>, item: { status: string; _count: { id: number } }) => {
      acc[item.status] = item._count.id
      return acc
    }, {} as Record<string, number>)

    const totalCount = totalStats._count.id || 0
    const completionRate = totalCount > 0
      ? ((statusStats["COMPLETED"] || 0) / totalCount * 100).toFixed(1)
      : "0.0"

    return {
      data: [
        ...data,
        { separator: true },
        { summary: "Status Breakdown", ...statusStats },
        { summary: "Completion Rate", percentage: `${completionRate}%` }
      ],
      metadata: {
        totalRecords: totalCount,
        generatedAt: new Date(),
        dateRange
      }
    }
  }

  static async generatePatientDemographics(query: ReportQuery): Promise<ReportResult> {
    const { tenantId } = query

    const patients = await prisma.patient.findMany({
      where: {
        tenantId,
        status: "ACTIVE"
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        dateOfBirth: true,
        createdAt: true,
        appointments: {
          select: {
            id: true,
            appointmentDate: true
          },
          orderBy: {
            appointmentDate: "desc"
          }
        }
      }
    })

    const data = patients.map((patient) => {
      const age = patient.dateOfBirth
        ? Math.floor((Date.now() - new Date(patient.dateOfBirth).getTime()) / (1000 * 60 * 60 * 24 * 365.25))
        : null

      const lastVisit = patient.appointments.length > 0
        ? patient.appointments[0].appointmentDate.toISOString().split('T')[0]
        : null

      const totalVisits = patient.appointments.length

      return {
        patientName: `${patient.firstName} ${patient.lastName}`,
        age: age || 'Unknown',
        totalVisits,
        lastVisit,
        registrationDate: patient.createdAt.toISOString().split('T')[0]
      }
    })

    // Age group breakdown
    const ageGroups = data.reduce((acc: Record<string, number>, patient: { age: number | string }) => {
      const age = patient.age
      if (typeof age === 'number') {
        if (age < 18) acc["Under 18"] = (acc["Under 18"] || 0) + 1
        else if (age < 30) acc["18-29"] = (acc["18-29"] || 0) + 1
        else if (age < 45) acc["30-44"] = (acc["30-44"] || 0) + 1
        else if (age < 65) acc["45-64"] = (acc["45-64"] || 0) + 1
        else acc["65+"] = (acc["65+"] || 0) + 1
      } else {
        acc["Unknown"] = (acc["Unknown"] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    return {
      data: [
        ...data,
        { separator: true },
        { summary: "Age Distribution", ...ageGroups }
      ],
      metadata: {
        totalRecords: patients.length,
        generatedAt: new Date(),
        dateRange: query.dateRange
      }
    }
  }

  static async generateTreatmentCompletion(query: ReportQuery): Promise<ReportResult> {
    const { tenantId, dateRange } = query

    const [treatments, statusBreakdown, totalStats] = await Promise.all([
      // Individual treatments for detail view (limited for performance)
      prisma.treatment.findMany({
        where: {
          tenantId,
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        },
        include: {
          finding: {
            include: {
              tooth: {
                select: {
                  toothNumber: true
                }
              }
            }
          },
          completedBy: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        },
        orderBy: {
          createdAt: "desc"
        },
        take: 1000 // Limit for performance
      }),

      // Aggregated status breakdown
      prisma.treatment.groupBy({
        by: ['status'],
        where: {
          tenantId,
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        },
        _count: {
          id: true
        }
      }),

      // Total treatment count and cost
      prisma.treatment.aggregate({
        where: {
          tenantId,
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        },
        _count: {
          id: true
        },
        _sum: {
          cost: true
        }
      })
    ])

    const data = treatments.map((treatment) => ({
      procedureName: treatment.procedureName,
      cost: Number(treatment.cost),
      status: treatment.status,
      toothNumber: treatment.finding?.tooth?.toothNumber || 'N/A',
      completedBy: treatment.completedBy ? `${treatment.completedBy.firstName} ${treatment.completedBy.lastName}` : null,
      completedDate: treatment.completedDate?.toISOString().split('T')[0] || null
    }))

    // Convert aggregated status data to object format
    const statusStats = statusBreakdown.reduce((acc: Record<string, number>, item: { status: string; _count: { id: number } }) => {
      acc[item.status] = item._count.id
      return acc
    }, {} as Record<string, number>)

    const totalCount = totalStats._count.id || 0
    const completionRate = totalCount > 0
      ? ((statusStats["COMPLETED"] || 0) / totalCount * 100).toFixed(1)
      : "0.0"

    return {
      data: [
        ...data,
        { separator: true },
        { summary: "Status Breakdown", ...statusStats },
        { summary: "Completion Rate", percentage: `${completionRate}%` },
        { summary: "Total Treatment Value", totalCost: Number(totalStats._sum.cost || 0) }
      ],
      metadata: {
        totalRecords: totalCount,
        generatedAt: new Date(),
        dateRange
      }
    }
  }
}
