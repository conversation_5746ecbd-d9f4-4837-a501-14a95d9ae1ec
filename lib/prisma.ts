import { PrismaClient, Prisma } from '@prisma/client';
import { 
  applyAllExtensions, 
  ExtendedPrismaClient, 
  ExtensionConfig, 
  defaultExtensionConfig,
  validateExtensionDependencies 
} from './prisma-extensions';
import { PrismaLibSQL } from '@prisma/adapter-libsql';
import { createClient } from '@libsql/client';


// Environment-based configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Global Prisma client type for proper TypeScript support
const globalForPrisma = globalThis as unknown as {
  prisma: ExtendedPrismaClient | undefined;
};

/**
 * Creates a new Prisma client with extensions applied
 */
function createPrismaClient(extensionConfig: ExtensionConfig = defaultExtensionConfig): ExtendedPrismaClient {
  // Validate extension dependencies
  const dependencyErrors = validateExtensionDependencies(extensionConfig);
  if (dependencyErrors.length > 0) {
    throw new Error(`Extension dependency validation failed:\n${dependencyErrors.join('\n')}`);
  }
  
  let baseClient: PrismaClient;

  if (isProduction && process.env.TURSO_DATABASE_URL) {
    // Production: Use Turso with libSQL adapter
    const adapter = new PrismaLibSQL({
      url: process.env.TURSO_DATABASE_URL,
      authToken: process.env.TURSO_AUTH_TOKEN,
    });
    baseClient = new PrismaClient({ 
      adapter,
      log: [{ level: 'error', emit: 'stdout' }]
    });
  } else {
    // Development/fallback: Use local SQLite
    const rawUrl = process.env.DATABASE_URL ?? "file:./dev.db";
    const enhancedUrl = rawUrl.includes("?") ? `${rawUrl}&timeout=20000` : `${rawUrl}?timeout=20000`;
    
    baseClient = new PrismaClient({
      log: isDevelopment 
        ? [{ level: 'query', emit: 'event' }, { level: 'error', emit: 'stdout' }]
        : [{ level: 'error', emit: 'stdout' }],
      datasources: {
        db: {
          url: enhancedUrl,
        },
      },
      transactionOptions: {
        maxWait: 20000,
        timeout: 20000,
      },
    });

    // Set up query logging for development
    if (isDevelopment) {
      baseClient.$on('query' as never, (e: Prisma.QueryEvent) => {
        console.log('Query: ' + e.query);
        console.log('Params: ' + e.params);
        console.log('Duration: ' + e.duration + 'ms');
      });
    }
  }

  // Apply all extensions to the client
  const extendedClient = applyAllExtensions(baseClient, extensionConfig);
  
  return extendedClient;
}

// Create the main client instance
const client = createPrismaClient();

// Export the main Prisma client instance with full type support
export const prisma: ExtendedPrismaClient = globalForPrisma.prisma ?? client;

// Export types for external use
export type { ExtendedPrismaClient };

// Export extension-related utilities
export { 
  TenantContext, 
  applyAllExtensions,
  defaultExtensionConfig,
  validateExtensionDependencies 
} from './prisma-extensions';

// Export tenant context manager
export { TenantContextManager } from './tenant-context';

// Graceful shutdown handler
export async function closeDatabaseConnection(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('Database connection closed gracefully');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
}

// Set up global client for development hot reloading
if (!isProduction) {
  globalForPrisma.prisma = client;
}

// Handle process termination gracefully
if (isProduction) {
  process.on('SIGINT', async () => {
    await closeDatabaseConnection();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    await closeDatabaseConnection();
    process.exit(0);
  });
}

// Default export
export default prisma;