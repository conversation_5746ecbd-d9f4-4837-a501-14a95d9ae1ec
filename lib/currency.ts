export const DEFAULT_CURRENCY = "IQD" as const

export type FormatCurrencyOptions = {
  currency?: string
  minimumFractionDigits?: number
  maximumFractionDigits?: number
  locale?: string | string[]
}

/**
 * Formats a numeric amount into a localized currency string.
 * Defaults to IQD with 0 fraction digits.
 */
export function formatCurrency(amount: number, options: FormatCurrencyOptions = {}): string {
  const {
    currency = DEFAULT_CURRENCY,
    minimumFractionDigits = 0,
    maximumFractionDigits = 0,
    locale,
  } = options

  try {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency,
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(amount)
  } catch {
    const rounded = Math.round(Number(amount) || 0)
    return `${rounded.toLocaleString(typeof locale === 'string' ? locale : undefined)} ${currency}`
  }
}


