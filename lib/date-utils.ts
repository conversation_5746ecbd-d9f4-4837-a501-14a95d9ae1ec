/**
 * Date utility functions for consistent date formatting across the application
 */

export type DateInput = string | Date | null | undefined;

/**
 * Format date to ISO date string (yyyy-MM-dd)
 * This is the primary format for consistent date display
 */
export function formatDate(value: DateInput): string {
  if (!value) return "—";
  
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    if (isNaN(date.getTime())) return "—";
    
    // Format as yyyy-MM-dd
    return date.toISOString().split('T')[0];
  } catch {
    return "—";
  }
}

/**
 * Format date for display with locale-aware formatting (e.g., "Jan 15, 2024")
 * Use this for user-friendly date displays
 */
export function formatDateLocale(value: DateInput): string {
  if (!value) return "—";
  
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    if (isNaN(date.getTime())) return "—";
    
    return new Intl.DateTimeFormat(undefined, {
      year: "numeric",
      month: "short",
      day: "2-digit",
    }).format(date);
  } catch {
    return "—";
  }
}

/**
 * Format date and time for display (e.g., "Jan 15, 2024, 2:30 PM")
 */
export function formatDateTime(value: DateInput): string {
  if (!value) return "—";
  
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    if (isNaN(date.getTime())) return "—";
    
    return new Intl.DateTimeFormat(undefined, {
      year: "numeric",
      month: "short",
      day: "2-digit",
      hour: "numeric",
      minute: "2-digit",
    }).format(date);
  } catch {
    return "—";
  }
}

/**
 * Format date for input fields (yyyy-MM-dd)
 * Same as formatDate but more explicit for form inputs
 */
export function formatDateForInput(value: DateInput): string {
  return formatDate(value);
}

/**
 * Format date for API/JSON serialization
 */
export function formatDateForAPI(value: DateInput): string | null {
  if (!value) return null;
  
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    if (isNaN(date.getTime())) return null;
    
    return date.toISOString();
  } catch {
    return null;
  }
}

/**
 * Format time only (e.g., "2:30 PM")
 */
export function formatTime(value: DateInput): string {
  if (!value) return "—";
  
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    if (isNaN(date.getTime())) return "—";
    
    return new Intl.DateTimeFormat(undefined, {
      hour: "numeric",
      minute: "2-digit",
    }).format(date);
  } catch {
    return "—";
  }
}

/**
 * Get relative time (e.g., "2 days ago", "in 3 hours")
 */
export function formatRelativeTime(value: DateInput): string {
  if (!value) return "—";
  
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    if (isNaN(date.getTime())) return "—";
    
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffDays = Math.round(diffMs / (1000 * 60 * 60 * 24));
    
    if (Math.abs(diffDays) < 1) {
      const diffHours = Math.round(diffMs / (1000 * 60 * 60));
      if (Math.abs(diffHours) < 1) {
        const diffMinutes = Math.round(diffMs / (1000 * 60));
        if (diffMinutes === 0) return "now";
        return diffMinutes > 0 ? `in ${diffMinutes}m` : `${Math.abs(diffMinutes)}m ago`;
      }
      return diffHours > 0 ? `in ${diffHours}h` : `${Math.abs(diffHours)}h ago`;
    }
    
    return diffDays > 0 ? `in ${diffDays} days` : `${Math.abs(diffDays)} days ago`;
  } catch {
    return "—";
  }
}

/**
 * Check if a date is today
 */
export function isToday(value: DateInput): boolean {
  if (!value) return false;
  
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    if (isNaN(date.getTime())) return false;
    
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  } catch {
    return false;
  }
}

/**
 * Parse and validate date input
 */
export function parseDate(value: DateInput): Date | null {
  if (!value) return null;
  
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    return isNaN(date.getTime()) ? null : date;
  } catch {
    return null;
  }
}

/**
 * Get age from date of birth
 */
export function calculateAge(dateOfBirth: DateInput): number | null {
  if (!dateOfBirth) return null;
  
  try {
    const birthDate = typeof dateOfBirth === "string" ? new Date(dateOfBirth) : dateOfBirth;
    if (isNaN(birthDate.getTime())) return null;
    
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  } catch {
    return null;
  }
}

/**
 * Get current timestamp in ISO format
 */
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Get current date in yyyy-MM-dd format
 */
export function getCurrentDate(): string {
  return formatDate(new Date());
}

/**
 * Legacy formatDate function for backward compatibility
 * @deprecated Use formatDateLocale instead for user-friendly display
 */
export function formatDateLegacy(value: DateInput): string {
  return formatDateLocale(value);
}
