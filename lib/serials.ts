import { prisma } from "./prisma"


export const generateInvoiceSerial = async (tenantId: number) => {
    const invoices = await prisma.invoiceSerial.create({
        data: {
            tenantId: tenantId,
        }
    })
    const serial = `${invoices.id}`
    return serial
}

export const generatePaymentSerial = async (tenantId: number) => {
    const payments = await prisma.paymentSerial.create({
        data: {
            tenantId: tenantId,
        }
    })
    const serial = `${payments.id}`
    return serial
}