import { UserType } from "@prisma/client";

/**
 * TypeScript interfaces for authentication data types
 */

// Re-export UserType from Prisma to maintain consistency
export { UserType } from "@prisma/client";

// Authentication result types
export interface AuthResult {
  success: boolean;
  message: string;
  user?: AuthUser;
  errors?: string[];
}

export interface AuthUser {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  userType: UserType;
  tenantId: number;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  // Working hours fields for dentists
  sat?: boolean;
  satstart?: string | null;
  satend?: string | null;
  sun?: boolean;
  sunstart?: string | null;
  sunend?: string | null;
  mon?: boolean;
  monstart?: string | null;
  monend?: string | null;
  tue?: boolean;
  tuestart?: string | null;
  tueend?: string | null;
  wed?: boolean;
  wedstart?: string | null;
  wedend?: string | null;
  thu?: boolean;
  thustart?: string | null;
  thuend?: string | null;
  fri?: boolean;
  fristart?: string | null;
  friend?: string | null;
}

// Session management types
export interface SessionInfo {
  userId: string;
  tenantId: number;
  username: string;
  userType: UserType;
  isActive: boolean;
  createdAt: Date;
  expiresAt: Date;
}

export interface SessionConfig {
  maxAge: number; // in seconds
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'strict' | 'lax' | 'none';
}

// Tenant creation types
export interface TenantCreationData {
  name: string;
  address: string;
  phone: string;
  email: string;
  logoImage?: string | null;
}

export interface TenantCreationResult {
  success: boolean;
  message: string;
  tenantId?: number;
  errors?: string[];
}

// User creation types
export interface AdminUserCreationData {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  tenantId: number;
}

export interface UserCreationResult {
  success: boolean;
  message: string;
  userId?: string;
  errors?: string[];
}

// Rate limiting types
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxAttempts: number; // Maximum attempts per window
  blockDurationMs: number; // How long to block after exceeding limit
}

export interface RateLimitInfo {
  attempts: number;
  windowStart: Date;
  isBlocked: boolean;
  blockUntil?: Date;
}

// Error types
export class AuthenticationError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public errors: string[]) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class TenantError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'TenantError';
  }
}

export class RateLimitError extends Error {
  constructor(message: string, public retryAfter: number) {
    super(message);
    this.name = 'RateLimitError';
  }
}

// API request/response types
export interface ApiRequest<T = unknown> {
  body: T;
  headers: Record<string, string>;
  ip?: string;
  userAgent?: string;
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message: string;
  errors?: string[];
  statusCode: number;
}

// Middleware types
export interface AuthMiddlewareOptions {
  requireAuth?: boolean;
  allowedUserTypes?: UserType[];
  requireTenant?: boolean;
}

export interface AuthContext {
  user?: AuthUser;
  session?: SessionInfo;
  tenant?: {
    id: number;
    name: string;
  };
}