import { getCurrentTimestamp } from "../date-utils";

/**
 * Password hashing utilities for secure authentication
 */
export class PasswordUtils {
  private static readonly SALT_ROUNDS = 12;

  /**
   * Hash a password using bcrypt
   */
  static async hashPassword(password: string): Promise<string> {
    const bcrypt = await import("bcryptjs");
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  /**
   * Verify a password against its hash
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    const bcrypt = await import("bcryptjs");
    return bcrypt.compare(password, hash);
  }

  /**
   * Validate password strength requirements
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    }

    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }

    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }

    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number");
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push("Password must contain at least one special character");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

/**
 * Input sanitization utilities
 */
export class SanitizationUtils {
  /**
   * Sanitize string input to prevent injection attacks
   */
  static sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes that could break SQL
      .replace(/[&]/g, '&amp;') // Escape ampersands
      .replace(/[/\\]/g, '') // Remove path traversal characters
      .substring(0, 255); // Limit length
  }

  /**
   * Sanitize email input
   */
  static sanitizeEmail(email: string): string {
    return email.toLowerCase().trim().substring(0, 255);
  }

  /**
   * Sanitize phone number input
   */
  static sanitizePhone(phone: string): string {
    return phone.replace(/[^\d+\-\s()]/g, '').trim().substring(0, 20);
  }

  /**
   * Sanitize and validate URL input
   */
  static sanitizeUrl(url: string): string {
    const sanitized = url.trim().substring(0, 2048);
    // Only allow http/https protocols
    if (!/^https?:\/\//.test(sanitized)) {
      return '';
    }
    return sanitized;
  }

  /**
   * Remove potentially dangerous characters that could be used for injection
   */
  static removeDangerousChars(input: string): string {
    return input
      .replace(/[<>'"&]/g, '') // HTML/SQL injection chars
      .replace(/[{}[\]]/g, '') // JSON injection chars
      .replace(/[;|&$`]/g, '') // Command injection chars
      .trim();
  }

  /**
   * Sanitize input for database operations
   */
  static sanitizeForDatabase(input: string): string {
    return input
      .trim()
      .replace(/[\0\x08\x09\x1a\n\r"'\\\%]/g, '') // SQL injection chars
      .substring(0, 1000); // Limit length
  }
}

/**
 * Additional sanitizers that preserve safe path segments
 */
export class PathSanitizationUtils {
  /**
   * Sanitize a public path (e.g., '/uploads/logos/abc.png') while preserving
   * forward slashes and common filename characters. Collapses duplicate slashes
   * and ensures a leading slash.
   */
  static sanitizePublicPath(path: string): string {
    const trimmed = path.trim().substring(0, 1024);
    const whitelisted = trimmed.replace(/[^A-Za-z0-9._\/-]/g, '');
    // collapse multiple slashes to a single slash
    let normalized = whitelisted.replace(/\/+\/+/g, '/');
    // ensure leading slash
    if (!normalized.startsWith('/')) {
      normalized = `/${normalized}`;
    }
    return normalized;
  }
}

/**
 * Validation utilities for common formats
 */
export class ValidationUtils {
  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 255;
  }

  /**
   * Validate phone number format (basic international format)
   */
  static isValidPhone(phone: string): boolean {
    // Remove all non-digit characters except the leading +
    const cleaned = phone.replace(/[\s\-\(\)]/g, '');
    
    // Iraqi mobile phone patterns:
    // - +964 followed by 7XX XXXX XXX (international format)
    // - 07XX XXXX XXX (national format)
    // - 7XX XXXX XXX (local format without leading 0)
    
    const iraqiMobilePatterns = [
      /^\+9647[0-9]{9}$/, // +964 7XX XXX XXXX (10 digits after 7)
      /^07[0-9]{9}$/,     // 07XX XXX XXXX (11 digits total)
      /^7[0-9]{9}$/       // 7XX XXX XXXX (10 digits total)
    ];
    
    return iraqiMobilePatterns.some(pattern => pattern.test(cleaned));
  }

  /**
   * Validate username format
   */
  static isValidUsername(username: string): boolean {
    const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
    return usernameRegex.test(username);
  }

  /**
   * Check for potential SQL injection patterns
   */
  static hasSqlInjectionPatterns(input: string): boolean {
    const sqlPatterns = [
      /(\bunion\b.*\bselect\b)/i,
      /(\bselect\b.*\bfrom\b)/i,
      /(\bdrop\b.*\btable\b)/i,
      /(\binsert\b.*\binto\b)/i,
      /(\bupdate\b.*\bset\b)/i,
      /(\bdelete\b.*\bfrom\b)/i,
      /(;.*--)|(\/\*.*\*\/)/i,
      /(\bor\b.*=.*)/i,
      /(\band\b.*=.*)/i,
    ];
    
    return sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Check for potential XSS patterns
   */
  static hasXssPatterns(input: string): boolean {
    // Decode URL encoded input for pattern matching
    const decodedInput = this.decodeUrlEncoding(input);
    
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
    ];
    
    return xssPatterns.some(pattern => pattern.test(input) || pattern.test(decodedInput));
  }

  /**
   * Decode URL encoded strings for security analysis
   */
  static decodeUrlEncoding(input: string): string {
    try {
      return decodeURIComponent(input);
    } catch {
      // If decoding fails, return original input
      return input;
    }
  }

  /**
   * Validate input against common security threats
   */
  static validateSecurityThreats(input: string): {
    isValid: boolean;
    threats: string[];
  } {
    const threats: string[] = [];

    if (this.hasSqlInjectionPatterns(input)) {
      threats.push('Potential SQL injection detected');
    }

    if (this.hasXssPatterns(input)) {
      threats.push('Potential XSS attack detected');
    }

    if (input.length > 10000) {
      threats.push('Input too long - potential DoS attack');
    }

    return {
      isValid: threats.length === 0,
      threats,
    };
  }
}

/**
 * Enhanced error handling utilities
 */
export class SecurityErrorHandler {
  /**
   * Create standardized error response for validation failures
   */
  static createValidationErrorResponse(
    message: string,
    errors: string[] = [],
    statusCode: number = 400
  ) {
    return {
      success: false,
      message,
      errors,
      statusCode,
      timestamp: getCurrentTimestamp(),
    };
  }

  /**
   * Create standardized error response for security violations
   */
  static createSecurityErrorResponse(
    message: string = 'Security violation detected',
    statusCode: number = 400
  ) {
    return {
      success: false,
      message,
      statusCode,
      timestamp: getCurrentTimestamp(),
      // Don't expose internal security details
    };
  }

  /**
   * Create standardized error response for rate limiting
   */
  static createRateLimitErrorResponse(retryAfter?: number) {
    return {
      success: false,
      message: 'Too many requests. Please try again later.',
      statusCode: 429,
      retryAfter,
      timestamp: getCurrentTimestamp(),
    };
  }

  /**
   * Log security incidents for monitoring
   */
  static logSecurityIncident(
    type: string,
    details: string,
    userAgent?: string,
    ip?: string
  ) {
    const incident = {
      type,
      details,
      userAgent,
      ip,
      timestamp: getCurrentTimestamp(),
    };

    // In production, this should go to a proper logging service
    console.warn('[SECURITY INCIDENT]', JSON.stringify(incident));
  }
}