import { z } from "zod";
import { ValidationUtils } from "./utils";

/**
 * Zod validation schemas for authentication data
 */

// Base schemas for reusable validation
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters long")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/\d/, "Password must contain at least one number")
  .regex(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character");

const emailSchema = z
  .string()
  .email("Invalid email format")
  .max(255, "Email must be less than 255 characters")
  .refine((email) => ValidationUtils.isValidEmail(email), {
    message: "Invalid email format",
  });

const phoneSchema = z
  .string()
  .min(10, "Phone number must be at least 10 digits")
  .max(20, "Phone number must be less than 20 characters")
  .refine((phone) => ValidationUtils.isValidPhone(phone), {
    message: "Invalid phone number format",
  });

const usernameSchema = z
  .string()
  .min(3, "Username must be at least 3 characters long")
  .max(30, "Username must be less than 30 characters")
  .refine((username) => ValidationUtils.isValidUsername(username), {
    message: "Username can only contain letters, numbers, and underscores",
  });

// Clinic information schema
export const clinicInfoSchema = z.object({
  name: z
    .string()
    .min(2, "Clinic name must be at least 2 characters long")
    .max(100, "Clinic name must be less than 100 characters")
    .trim(),
  address: z
    .string()
    .min(5, "Address must be at least 5 characters long")
    .max(255, "Address must be less than 255 characters")
    .trim(),
  phone: phoneSchema,
  email: emailSchema,
});

// Admin user schema for registration
export const adminUserSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  firstName: z
    .string()
    .min(1, "First name is required")
    .max(50, "First name must be less than 50 characters")
    .trim(),
  lastName: z
    .string()
    .min(1, "Last name is required")
    .max(50, "Last name must be less than 50 characters")
    .trim(),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Registration schema combining clinic and admin user
export const registrationSchema = z.object({
  clinic: clinicInfoSchema,
  adminUser: adminUserSchema,
});

// Login schema
export const loginSchema = z.object({
  username: z
    .string()
    .min(1, "Username is required")
    .max(30, "Username must be less than 30 characters")
    .trim(),
  password: z
    .string()
    .min(1, "Password is required"),
});

// Session data schema
export const sessionSchema = z.object({
  userId: z.string(),
  tenantId: z.number(),
  username: z.string(),
  userType: z.enum(["ADMIN", "DENTIST", "RECEPTIONIST", "PATIENT"]),
  isActive: z.boolean(),
  expiresAt: z.date(),
});

// Type exports for TypeScript
export type ClinicInfo = z.infer<typeof clinicInfoSchema>;
export type AdminUser = z.infer<typeof adminUserSchema>;
export type RegistrationData = z.infer<typeof registrationSchema>;
export type LoginData = z.infer<typeof loginSchema>;
export type SessionData = z.infer<typeof sessionSchema>;

// API response schemas
export const registrationResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  tenantId: z.number().optional(),
  userId: z.string().optional(),
});

export const loginResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  user: z.object({
    id: z.string(),
    username: z.string(),
    email: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    userType: z.string(),
    tenantId: z.number(),
  }).optional(),
  redirectUrl: z.string().optional(),
});

export const errorResponseSchema = z.object({
  success: z.literal(false),
  message: z.string(),
  errors: z.array(z.string()).optional(),
});

export type RegistrationResponse = z.infer<typeof registrationResponseSchema>;
export type LoginResponse = z.infer<typeof loginResponseSchema>;
export type ErrorResponse = z.infer<typeof errorResponseSchema>;