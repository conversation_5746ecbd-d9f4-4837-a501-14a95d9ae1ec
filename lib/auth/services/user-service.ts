import { prisma } from "../../prisma";
import { TenantContextManager } from "../../tenant-context";
import { 
  AdminUserCreationData, 
  UserCreationR<PERSON>ult, 
  AuthR<PERSON>ult, 
  AuthUser,
  UserType,
  AuthenticationError 
} from "../types";
import { PasswordUtils, SanitizationUtils, ValidationUtils } from "../utils";

/**
 * UserService - Handles user management and authentication operations
 * Requirements: 2.1, 2.2, 2.4, 3.2, 4.1
 */
export class UserService {
  /**
   * Create admin user with superuser privileges
   * Requirements: 2.1, 2.2
   */
  static async createAdminUser(data: AdminUserCreationData): Promise<UserCreationResult> {
    try {
      // Sanitize input data
      const sanitizedData = {
        username: SanitizationUtils.sanitizeString(data.username),
        email: SanitizationUtils.sanitizeEmail(data.email),
        firstName: SanitizationUtils.sanitizeString(data.firstName),
        lastName: SanitizationUtils.sanitizeString(data.lastName),
        password: data.password, // Don't sanitize password
        tenantId: data.tenantId,
      };

      // Validate input data
      const errors: string[] = [];
      
      if (!ValidationUtils.isValidUsername(sanitizedData.username)) {
        errors.push("Username can only contain letters, numbers, and underscores (3-30 characters)");
      }
      
      if (!ValidationUtils.isValidEmail(sanitizedData.email)) {
        errors.push("Valid email address is required");
      }
      
      if (!sanitizedData.firstName || sanitizedData.firstName.length < 1) {
        errors.push("First name is required");
      }
      
      if (!sanitizedData.lastName || sanitizedData.lastName.length < 1) {
        errors.push("Last name is required");
      }

      // Validate password strength
      const passwordValidation = PasswordUtils.validatePasswordStrength(sanitizedData.password);
      if (!passwordValidation.isValid) {
        errors.push(...passwordValidation.errors);
      }

      if (errors.length > 0) {
        return {
          success: false,
          message: "Validation failed",
          errors,
        };
      }

      // Check for existing user with same username or email in tenant
      const existingUser = await this.checkUserExists(
        sanitizedData.username,
        sanitizedData.email,
        sanitizedData.tenantId
      );

      if (existingUser.exists) {
        return {
          success: false,
          message: "User already exists",
          errors: existingUser.errors,
        };
      }

      // Hash password
      const hashedPassword = await PasswordUtils.hashPassword(sanitizedData.password);

      // Create user with tenant context
      const user = await TenantContextManager.runWithContext(
        { tenantId: sanitizedData.tenantId },
        async () => {
          return prisma.user.create({
            data: {
              username: sanitizedData.username,
              email: sanitizedData.email,
              firstName: sanitizedData.firstName,
              lastName: sanitizedData.lastName,
              password: hashedPassword,
              userType: "ADMIN", // Admin users get ADMIN type (closest to superuser)
              isActive: true,
              tenantId: sanitizedData.tenantId,
            },
          });
        }
      );

      return {
        success: true,
        message: "Admin user created successfully",
        userId: user.id.toString(),
      };
    } catch (error) {
      console.error("Error creating admin user:", error);
      return {
        success: false,
        message: "Failed to create admin user",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Authenticate user with credentials
   * Requirements: 3.2, 2.4
   */
  static async authenticateUser(
    username: string,
    password: string,
    tenantId?: string
  ): Promise<AuthResult> {
    try {
      // Sanitize input
      const sanitizedUsername = SanitizationUtils.sanitizeString(username);

      if (!sanitizedUsername || !password) {
        return {
          success: false,
          message: "Username and password are required",
        };
      }

      // Find user by username or email across all tenants if no tenantId provided
      let user;
      
      if (tenantId) {
        // Search within specific tenant
        user = await TenantContextManager.runWithContext(
          { tenantId },
          async () => {
            return prisma.user.findFirst({
              where: {
                OR: [
                  { username: sanitizedUsername },
                  { email: sanitizedUsername },
                ],
                isActive: true,
                tenantId,
              },
            });
          }
        );
      } else {
        // Search across all tenants (for login without tenant context)
        user = await prisma.bypassTenant(async () => {
          return prisma.user.findFirst({
            where: {
              OR: [
                { username: sanitizedUsername },
                { email: sanitizedUsername },
              ],
              isActive: true,
            },
          });
        });
      }

      if (!user) {
        return {
          success: false,
          message: "Invalid credentials",
        };
      }

      // Verify password
      const isPasswordValid = await PasswordUtils.verifyPassword(password, user.password);
      if (!isPasswordValid) {
        return {
          success: false,
          message: "Invalid credentials",
        };
      }

      // Check if user account is active
      if (!user.isActive) {
        return {
          success: false,
          message: "Account is inactive",
        };
      }

      // Create auth user object
      const authUser: AuthUser = {
        id: user.id.toString(),
        username: user.username || "",
        email: user.email || "",
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        userType: user.userType as UserType,
        tenantId: user.tenantId,
        isActive: user.isActive,
      };

      return {
        success: true,
        message: "Authentication successful",
        user: authUser,
      };
    } catch (error) {
      console.error("Error authenticating user:", error);
      return {
        success: false,
        message: "Authentication failed",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Check if user exists with given username or email in tenant
   * Requirements: 2.5
   */
  static async checkUserExists(
    username: string,
    email: string,
    tenantId: number
  ): Promise<{ exists: boolean; errors: string[] }> {
    try {
      const errors: string[] = [];

      // Check username uniqueness within tenant
      const existingByUsername = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.findFirst({
            where: {
              username,
              tenantId,
            },
          });
        }
      );

      if (existingByUsername) {
        errors.push("Username already exists in this clinic");
      }

      // Check email uniqueness within tenant
      const existingByEmail = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.findFirst({
            where: {
              email,
              tenantId,
            },
          });
        }
      );

      if (existingByEmail) {
        errors.push("Email already exists in this clinic");
      }

      return {
        exists: errors.length > 0,
        errors,
      };
    } catch (error) {
      console.error("Error checking user existence:", error);
      return {
        exists: true, // Fail safe - assume exists to prevent duplicates
        errors: ["Unable to verify user uniqueness"],
      };
    }
  }

  /**
   * Update user password
   * Requirements: 2.4, 4.1
   */
  static async updatePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    tenantId: number
  ): Promise<UserCreationResult> {
    try {
      // Validate new password strength
      const passwordValidation = PasswordUtils.validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        return {
          success: false,
          message: "Password does not meet requirements",
          errors: passwordValidation.errors,
        };
      }

      // Get current user
      const user = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.findUnique({
            where: { 
              id: parseInt(userId),
              tenantId,
            },
          });
        }
      );

      if (!user) {
        throw new AuthenticationError("User not found", "USER_NOT_FOUND");
      }

      // Verify current password
      const isCurrentPasswordValid = await PasswordUtils.verifyPassword(
        currentPassword,
        user.password
      );

      if (!isCurrentPasswordValid) {
        return {
          success: false,
          message: "Current password is incorrect",
          errors: ["Invalid current password"],
        };
      }

      // Hash new password
      const hashedNewPassword = await PasswordUtils.hashPassword(newPassword);

      // Update password
      await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.update({
            where: { 
              id: parseInt(userId),
              tenantId,
            },
            data: { password: hashedNewPassword },
          });
        }
      );

      return {
        success: true,
        message: "Password updated successfully",
        userId,
      };
    } catch (error) {
      console.error("Error updating password:", error);
      
      if (error instanceof AuthenticationError) {
        throw error;
      }
      
      return {
        success: false,
        message: "Failed to update password",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Deactivate user account
   * Requirements: 3.6
   */
  static async deactivateUser(userId: string, tenantId: number): Promise<UserCreationResult> {
    try {
      await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.update({
            where: { 
              id: parseInt(userId),
              tenantId,
            },
            data: { isActive: false },
          });
        }
      );

      return {
        success: true,
        message: "User account deactivated successfully",
        userId,
      };
    } catch (error) {
      console.error("Error deactivating user:", error);
      return {
        success: false,
        message: "Failed to deactivate user account",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Get users by type for a specific tenant
   * Requirements: User management
   */
  static async getUsersByType(
    tenantId: number, 
    userType: UserType, 
    options: { search?: string; includeInactive?: boolean } = {}
  ): Promise<AuthUser[]> {
    try {
      const whereClause: {
        tenantId: number;
        userType: UserType;
        isActive?: boolean;
        OR?: Array<{
          firstName?: { contains: string };
          lastName?: { contains: string };
          username?: { contains: string };
          email?: { contains: string };
        }>;
      } = {
        tenantId,
        userType,
      };

      if (!options.includeInactive) {
        whereClause.isActive = true;
      }

      if (options.search) {
        whereClause.OR = [
          {
            firstName: {
              contains: options.search
            }
          },
          {
            lastName: {
              contains: options.search
            }
          },
          {
            username: {
              contains: options.search
            }
          },
          {
            email: {
              contains: options.search
            }
          }
        ];
      }

      const users = await prisma.user.findMany({
        where: whereClause,
        select: {
          id: true,
          tenantId: true,
          username: true,
          email: true,
          firstName: true,
          lastName: true,
          phoneNumber: true,
          userType: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          // Include working hours fields for dentists
          ...(userType === UserType.DENTIST ? {
            sat: true,
            satstart: true,
            satend: true,
            sun: true,
            sunstart: true,
            sunend: true,
            mon: true,
            monstart: true,
            monend: true,
            tue: true,
            tuestart: true,
            tueend: true,
            wed: true,
            wedstart: true,
            wedend: true,
            thu: true,
            thustart: true,
            thuend: true,
            fri: true,
            fristart: true,
            friend: true,
          } : {}),
        },
        orderBy: {
          firstName: 'asc'
        }
      });

      return users.map((user) => ({
        id: user.id.toString(),
        tenantId: user.tenantId,
        username: user.username || '',
        email: user.email || '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phoneNumber: user.phoneNumber || '',
        userType: user.userType as UserType,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        // Include working hours fields for dentists
        ...(userType === UserType.DENTIST ? {
          sat: user.sat,
          satstart: user.satstart,
          satend: user.satend,
          sun: user.sun,
          sunstart: user.sunstart,
          sunend: user.sunend,
          mon: user.mon,
          monstart: user.monstart,
          monend: user.monend,
          tue: user.tue,
          tuestart: user.tuestart,
          tueend: user.tueend,
          wed: user.wed,
          wedstart: user.wedstart,
          wedend: user.wedend,
          thu: user.thu,
          thustart: user.thustart,
          thuend: user.thuend,
          fri: user.fri,
          fristart: user.fristart,
          friend: user.friend,
        } : {}),
      }));
    } catch (error) {
      console.error("Error fetching users by type:", error);
      throw new Error("Failed to fetch users");
    }
  }

  /**
   * Create a new user
   * Requirements: User management
   */
  static async createUser(data: {
    tenantId: number;
    firstName: string;
    lastName: string;
    email?: string | null;
    phoneNumber?: string | null;
    username: string;
    password: string;
    userType: UserType;
    isActive: boolean;
    createdById?: string;
  }): Promise<UserCreationResult> {
    try {
      // Sanitize input data
      const sanitizedData = {
        tenantId: data.tenantId,
        firstName: SanitizationUtils.sanitizeString(data.firstName),
        lastName: SanitizationUtils.sanitizeString(data.lastName),
        email: data.email ? SanitizationUtils.sanitizeEmail(data.email) : null,
        phoneNumber: data.phoneNumber ? SanitizationUtils.sanitizeString(data.phoneNumber) : null,
        username: SanitizationUtils.sanitizeString(data.username),
        password: data.password,
        userType: data.userType,
        isActive: data.isActive,
        createdById: data.createdById ? parseInt(data.createdById) : null,
      };

      // Validate input data
      const errors: string[] = [];
      
      if (!ValidationUtils.isValidUsername(sanitizedData.username)) {
        errors.push("Username can only contain letters, numbers, and underscores (3-30 characters)");
      }
      
      if (sanitizedData.email && !ValidationUtils.isValidEmail(sanitizedData.email)) {
        errors.push("Valid email address is required");
      }
      
      if (!sanitizedData.firstName || sanitizedData.firstName.length < 1) {
        errors.push("First name is required");
      }
      
      if (!sanitizedData.lastName || sanitizedData.lastName.length < 1) {
        errors.push("Last name is required");
      }
      
      if (sanitizedData.password.length < 6) {
        errors.push("Password must be at least 6 characters long");
      }

      if (errors.length > 0) {
        return {
          success: false,
          message: "Validation failed",
          errors,
        };
      }

      // Check for existing user with same username, email, or phone in tenant
      const existingUser = await prisma.user.findFirst({
        where: {
          tenantId: sanitizedData.tenantId,
          OR: [
            { username: sanitizedData.username },
            ...(sanitizedData.email ? [{ email: sanitizedData.email }] : []),
            ...(sanitizedData.phoneNumber ? [{ phoneNumber: sanitizedData.phoneNumber }] : []),
          ],
        },
      });

      if (existingUser) {
        return {
          success: false,
          message: "User with this username, email, or phone number already exists",
          errors: ["User already exists"],
        };
      }

      // Hash password
      const hashedPassword = await PasswordUtils.hashPassword(sanitizedData.password);

      // Create user
      const user = await prisma.user.create({
        data: {
          tenantId: sanitizedData.tenantId,
          firstName: sanitizedData.firstName,
          lastName: sanitizedData.lastName,
          email: sanitizedData.email,
          phoneNumber: sanitizedData.phoneNumber,
          username: sanitizedData.username,
          password: hashedPassword,
          userType: sanitizedData.userType,
          isActive: sanitizedData.isActive,
          createdById: sanitizedData.createdById,
        },
      });

      return {
        success: true,
        message: "User created successfully",
        userId: user.id.toString(),
      };
    } catch (error) {
      console.error("Error creating user:", error);
      return {
        success: false,
        message: "Failed to create user",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Update user information
   * Requirements: User management
   */
  static async updateUser(userId: number, data: {
    firstName?: string;
    lastName?: string;
    email?: string | null;
    phoneNumber?: string | null;
    username?: string;
    password?: string;
    isActive?: boolean;
    updatedById?: string;
    // Working hours fields for dentists
    sat?: boolean;
    satstart?: string | null;
    satend?: string | null;
    sun?: boolean;
    sunstart?: string | null;
    sunend?: string | null;
    mon?: boolean;
    monstart?: string | null;
    monend?: string | null;
    tue?: boolean;
    tuestart?: string | null;
    tueend?: string | null;
    wed?: boolean;
    wedstart?: string | null;
    wedend?: string | null;
    thu?: boolean;
    thustart?: string | null;
    thuend?: string | null;
    fri?: boolean;
    fristart?: string | null;
    friend?: string | null;
  }): Promise<UserCreationResult> {
    try {
      const updateData: Record<string, string | boolean | null | number> = {};

      if (data.firstName) {
        updateData.firstName = SanitizationUtils.sanitizeString(data.firstName);
      }
      
      if (data.lastName) {
        updateData.lastName = SanitizationUtils.sanitizeString(data.lastName);
      }
      
      if (data.email !== undefined) {
        updateData.email = data.email ? SanitizationUtils.sanitizeEmail(data.email) : null;
      }
      
      if (data.phoneNumber !== undefined) {
        updateData.phoneNumber = data.phoneNumber ? SanitizationUtils.sanitizeString(data.phoneNumber) : null;
      }
      
      if (data.username) {
        updateData.username = SanitizationUtils.sanitizeString(data.username);
      }
      
      if (data.password) {
        updateData.password = await PasswordUtils.hashPassword(data.password);
      }
      
      if (data.isActive !== undefined) {
        updateData.isActive = data.isActive;
      }
      
      if (data.updatedById) {
        updateData.updatedById = parseInt(data.updatedById);
      }

      // Working hours fields for dentists
      if (data.sat !== undefined) {
        updateData.sat = data.sat;
      }
      if (data.satstart !== undefined) {
        updateData.satstart = data.satstart;
      }
      if (data.satend !== undefined) {
        updateData.satend = data.satend;
      }
      if (data.sun !== undefined) {
        updateData.sun = data.sun;
      }
      if (data.sunstart !== undefined) {
        updateData.sunstart = data.sunstart;
      }
      if (data.sunend !== undefined) {
        updateData.sunend = data.sunend;
      }
      if (data.mon !== undefined) {
        updateData.mon = data.mon;
      }
      if (data.monstart !== undefined) {
        updateData.monstart = data.monstart;
      }
      if (data.monend !== undefined) {
        updateData.monend = data.monend;
      }
      if (data.tue !== undefined) {
        updateData.tue = data.tue;
      }
      if (data.tuestart !== undefined) {
        updateData.tuestart = data.tuestart;
      }
      if (data.tueend !== undefined) {
        updateData.tueend = data.tueend;
      }
      if (data.wed !== undefined) {
        updateData.wed = data.wed;
      }
      if (data.wedstart !== undefined) {
        updateData.wedstart = data.wedstart;
      }
      if (data.wedend !== undefined) {
        updateData.wedend = data.wedend;
      }
      if (data.thu !== undefined) {
        updateData.thu = data.thu;
      }
      if (data.thustart !== undefined) {
        updateData.thustart = data.thustart;
      }
      if (data.thuend !== undefined) {
        updateData.thuend = data.thuend;
      }
      if (data.fri !== undefined) {
        updateData.fri = data.fri;
      }
      if (data.fristart !== undefined) {
        updateData.fristart = data.fristart;
      }
      if (data.friend !== undefined) {
        updateData.friend = data.friend;
      }

      // Validate username if provided
      if (updateData.username && !ValidationUtils.isValidUsername(updateData.username as number)) {
        return {
          success: false,
          message: "Username can only contain letters, numbers, and underscores (3-30 characters)",
          errors: ["Invalid username format"],
        };
      }

      // Validate email if provided
      if (updateData.email && !ValidationUtils.isValidEmail(updateData.email as number)) {
        return {
          success: false,
          message: "Valid email address is required",
          errors: ["Invalid email format"],
        };
      }

      await prisma.user.update({
        where: { id: userId },
        data: updateData,
      });

      return {
        success: true,
        message: "User updated successfully",
        userId: userId.toString(),
      };
    } catch (error) {
      console.error("Error updating user:", error);
      
      if (error instanceof Error && error.message.includes("Unique constraint failed")) {
        return {
          success: false,
          message: "Username, email, or phone number already exists",
          errors: ["Duplicate data"],
        };
      }
      
      return {
        success: false,
        message: "Failed to update user",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Get user by ID (overload for number parameter)
   */
  static async getUserById(userId: number): Promise<AuthUser | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          tenantId: true,
          username: true,
          email: true,
          firstName: true,
          lastName: true,
          phoneNumber: true,
          userType: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          // Working hours fields
          sat: true,
          satstart: true,
          satend: true,
          sun: true,
          sunstart: true,
          sunend: true,
          mon: true,
          monstart: true,
          monend: true,
          tue: true,
          tuestart: true,
          tueend: true,
          wed: true,
          wedstart: true,
          wedend: true,
          thu: true,
          thustart: true,
          thuend: true,
          fri: true,
          fristart: true,
          friend: true,
        },
      });

      if (!user) {
        return null;
      }

      return {
        id: user.id.toString(),
        tenantId: user.tenantId,
        username: user.username || '',
        email: user.email || '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phoneNumber: user.phoneNumber || '',
        userType: user.userType as UserType,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        // Working hours fields
        sat: user.sat,
        satstart: user.satstart,
        satend: user.satend,
        sun: user.sun,
        sunstart: user.sunstart,
        sunend: user.sunend,
        mon: user.mon,
        monstart: user.monstart,
        monend: user.monend,
        tue: user.tue,
        tuestart: user.tuestart,
        tueend: user.tueend,
        wed: user.wed,
        wedstart: user.wedstart,
        wedend: user.wedend,
        thu: user.thu,
        thustart: user.thustart,
        thuend: user.thuend,
        fri: user.fri,
        fristart: user.fristart,
        friend: user.friend,
      };
    } catch (error) {
      console.error("Error fetching user by ID:", error);
      return null;
    }
  }
}