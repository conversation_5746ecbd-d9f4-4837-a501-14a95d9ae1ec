import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "./services/auth-service";
import { TenantContextManager } from "../tenant-context";
import { UserType, AuthContext, AuthMiddlewareOptions } from "./types";

/**
 * Authentication and Tenant Context Middleware
 * Requirements: 3.4, 3.6, 4.3, 8.1, 8.2
 */
export class AuthMiddleware {
  /**
   * Create authentication middleware for Next.js API routes
   * This middleware validates sessions and sets tenant context automatically
   */
  static createApiMiddleware(options: AuthMiddlewareOptions = {}) {
    const {
      requireAuth = true,
      allowedUserTypes = [],
      requireTenant = true,
    } = options;

    return async (
      request: NextRequest,
      context: { params?: Record<string, string> } = {}
    ): Promise<{
      isAuthorized: boolean;
      authContext?: AuthContext;
      response?: NextResponse;
    }> => {
      try {
        
        // Validate session
        const sessionValidation = await AuthService.validateSession();
        
        if (!sessionValidation.isValid || !sessionValidation.session) {
          
          if (requireAuth) {
            return {
              isAuthorized: false,
              response: NextResponse.json(
                {
                  success: false,
                  message: "Authentication required",
                  errors: ["Please log in to access this resource"],
                },
                { status: 401 }
              ),
            };
          }
          
          // Not requiring auth, continue without context
          return { isAuthorized: true };
        }

        const session = sessionValidation.session;

        // Check user type authorization
        if (allowedUserTypes.length > 0 && !allowedUserTypes.includes(session.userType)) {
          
          return {
            isAuthorized: false,
            response: NextResponse.json(
              {
                success: false,
                message: "Insufficient permissions",
                errors: ["You don't have permission to access this resource"],
              },
              { status: 403 }
            ),
          };
        }

        // Create tenant context
        const tenantContext = TenantContextManager.createContextFromAuthSession(
          session,
          undefined, // sessionId is already in the session
          undefined  // We'll get tenant name later if needed
        );

        // Set tenant context for this request
        TenantContextManager.setGlobalContext(tenantContext);

        // Validate tenant access if required
        if (requireTenant && !session.tenantId) {
          
          return {
            isAuthorized: false,
            response: NextResponse.json(
              {
                success: false,
                message: "Tenant context required",
                errors: ["Valid tenant context is required for this operation"],
              },
              { status: 400 }
            ),
          };
        }

        // Create auth context
        const authContext: AuthContext = {
          user: {
            id: session.userId,
            username: session.username,
            email: "", // Will be populated from user service if needed
            firstName: "", // Will be populated from user service if needed
            lastName: "", // Will be populated from user service if needed
            userType: session.userType,
            tenantId: session.tenantId,
            isActive: session.isActive,
          },
          session: session,
          tenant: session.tenantId ? {
            id: session.tenantId,
            name: tenantContext.tenantName || `Tenant ${session.tenantId}`,
          } : undefined,
        };

        
        return {
          isAuthorized: true,
          authContext,
        };

      } catch (error) {
        
        return {
          isAuthorized: false,
          response: NextResponse.json(
            {
              success: false,
              message: "Authentication error",
              errors: ["An error occurred during authentication"],
            },
            { status: 500 }
          ),
        };
      }
    };
  }

  /**
   * Create a wrapper for API route handlers that includes authentication
   */
  static withAuth(
    handler: (
      request: NextRequest,
      context: { params?: Record<string, string>; authContext: AuthContext }
    ) => Promise<NextResponse>,
    options: AuthMiddlewareOptions = {}
  ) {
    return async (
      request: NextRequest,
      context: { params?: Record<string, string> } = {}
    ): Promise<NextResponse> => {
      const middleware = this.createApiMiddleware(options);
      const result = await middleware(request, context);

      if (!result.isAuthorized) {
        return result.response || NextResponse.json(
          { success: false, message: "Unauthorized" },
          { status: 401 }
        );
      }

      if (!result.authContext) {
        return NextResponse.json(
          { success: false, message: "Authentication context missing" },
          { status: 500 }
        );
      }

      // Call the actual handler with auth context
      return handler(request, { ...context, authContext: result.authContext });
    };
  }

  /**
   * Route guard for client-side navigation
   * This should be used in page components or layout components
   */
  static async checkRouteAccess(options: AuthMiddlewareOptions = {}): Promise<{
    isAuthorized: boolean;
    authContext?: AuthContext;
    redirectUrl?: string;
  }> {
    try {
      const sessionValidation = await AuthService.validateSession();
      
      if (!sessionValidation.isValid || !sessionValidation.session) {
        return {
          isAuthorized: false,
          redirectUrl: "/auth/login",
        };
      }

      const session = sessionValidation.session;

      // Check user type authorization
      if (options.allowedUserTypes && options.allowedUserTypes.length > 0) {
        if (!options.allowedUserTypes.includes(session.userType)) {
          return {
            isAuthorized: false,
            redirectUrl: "/unauthorized",
          };
        }
      }

      // Create auth context
      const authContext: AuthContext = {
        user: {
          id: session.userId,
          username: session.username,
          email: "",
          firstName: "",
          lastName: "",
          userType: session.userType,
          tenantId: session.tenantId,
          isActive: session.isActive,
        },
        session: session,
        tenant: session.tenantId ? {
          id: session.tenantId,
          name: `Tenant ${session.tenantId}`,
        } : undefined,
      };

      return {
        isAuthorized: true,
        authContext,
      };

    } catch (error) {
      return {
        isAuthorized: false,
        redirectUrl: "/auth/login",
      };
    }
  }
}

/**
 * Utility functions for role-based access control
 */
export class RoleGuard {
  /**
   * Check if user has sufficient privileges for an operation
   */
  static hasPermission(userType: UserType, requiredLevel: UserType): boolean {
    const hierarchy: Record<UserType, number> = {
      ADMIN: 4,
      DENTIST: 3,
      RECEPTIONIST: 2,
      PATIENT: 1,
    };

    return hierarchy[userType] >= hierarchy[requiredLevel];
  }

  /**
   * Check if user can access admin features
   */
  static isAdmin(userType: UserType): boolean {
    return userType === "ADMIN";
  }

  /**
   * Check if user can access clinical features
   */
  static isClinicalStaff(userType: UserType): boolean {
    return ["ADMIN", "DENTIST"].includes(userType);
  }

  /**
   * Check if user can manage other users
   */
  static canManageUsers(userType: UserType): boolean {
    return userType === "ADMIN";
  }

  /**
   * Create middleware options for different access levels
   */
  static adminOnly(): AuthMiddlewareOptions {
    return {
      requireAuth: true,
      allowedUserTypes: ["ADMIN"],
      requireTenant: true,
    };
  }

  static clinicalStaffOnly(): AuthMiddlewareOptions {
    return {
      requireAuth: true,
      allowedUserTypes: ["ADMIN", "DENTIST"],
      requireTenant: true,
    };
  }

  static authenticatedOnly(): AuthMiddlewareOptions {
    return {
      requireAuth: true,
      allowedUserTypes: ["ADMIN", "DENTIST", "RECEPTIONIST"],
      requireTenant: true,
    };
  }
}
