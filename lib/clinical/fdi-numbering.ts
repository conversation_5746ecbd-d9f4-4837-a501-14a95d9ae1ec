// FDI Tooth Numbering utilities

export type Quadrant = 1 | 2 | 3 | 4

/**
 * Generate all valid permanent dentition FDI numbers: 11-18, 21-28, 31-38, 41-48
 */
export function generateFDINumbers(): number[] {
  const numbers: number[] = []
  for (let quadrant = 1 as Quadrant; quadrant <= 4; quadrant = (quadrant + 1) as Quadrant) {
    for (let position = 1; position <= 8; position++) {
      numbers.push(quadrant * 10 + position)
    }
  }
  return numbers
}

/**
 * Calculate quadrant from an FDI tooth number
 * Returns 1-4 for valid FDI numbers; throws on invalid inputs
 */
export function calculateQuadrant(toothNumber: number): Quadrant {
  validateFDINumber(toothNumber)
  return Math.floor(toothNumber / 10) as Quadrant
}

/**
 * Calculate position within quadrant from an FDI tooth number (1-8)
 * Throws on invalid inputs
 */
export function calculatePosition(toothNumber: number): number {
  validateFDINumber(toothNumber)
  return toothNumber % 10
}

/**
 * Validate that a number is a valid permanent dentition FDI number
 */
export function isValidFDINumber(toothNumber: number): boolean {
  const quadrant = Math.floor(toothNumber / 10)
  const position = toothNumber % 10
  return quadrant >= 1 && quadrant <= 4 && position >= 1 && position <= 8
}

function validateFDINumber(toothNumber: number): void {
  if (!Number.isInteger(toothNumber) || !isValidFDINumber(toothNumber)) {
    throw new Error(`Invalid FDI tooth number: ${toothNumber}`)
  }
}

// Names and types
export type ToothType = 'Incisor' | 'Canine' | 'Premolar' | 'Molar'

const TOOTH_NAME_MAP: Record<number, string> = (() => {
  const map: Record<number, string> = {}
  const quadrantName: Record<Quadrant, string> = {
    1: 'Upper Right',
    2: 'Upper Left',
    3: 'Lower Left',
    4: 'Lower Right',
  }
  const positionName: Record<number, string> = {
    1: 'Central Incisor',
    2: 'Lateral Incisor',
    3: 'Canine',
    4: 'First Premolar',
    5: 'Second Premolar',
    6: 'First Molar',
    7: 'Second Molar',
    8: 'Third Molar',
  }
  for (const n of generateFDINumbers()) {
    const q = calculateQuadrant(n)
    const p = calculatePosition(n)
    map[n] = `${quadrantName[q]} ${positionName[p]}`
  }
  return map
})()

const TOOTH_TYPE_MAP: Record<number, ToothType> = (() => {
  const map: Record<number, ToothType> = {}
  for (const n of generateFDINumbers()) {
    const p = calculatePosition(n)
    if (p === 1 || p === 2) map[n] = 'Incisor'
    else if (p === 3) map[n] = 'Canine'
    else if (p === 4 || p === 5) map[n] = 'Premolar'
    else map[n] = 'Molar'
  }
  return map
})()

export function getToothName(toothNumber: number): string {
  validateFDINumber(toothNumber)
  return TOOTH_NAME_MAP[toothNumber]
}

export function getToothType(toothNumber: number): ToothType {
  validateFDINumber(toothNumber)
  return TOOTH_TYPE_MAP[toothNumber]
}


