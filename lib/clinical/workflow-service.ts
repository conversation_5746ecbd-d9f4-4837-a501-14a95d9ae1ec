import { Invoice, Payment, Treatment, Finding, CaseSheet, Patient, PatientStatus, InvoiceStatus, PaymentStatus } from "@prisma/client";
import prisma from "../prisma";
import type { ExtendedPrismaClient } from "../prisma-extensions";
import { WorkflowError, WorkflowErrorHandler } from "./errors";
import { logger } from "./logger";

// Simplified workflow result interface - removed over-engineering
// For basic patient creation, we don't need complex state tracking

export interface WorkflowResult {
  patient?: Patient;
  caseSheet?: CaseSheet;
  teethCount?: number;
  finding?: Finding;
  treatment?: Treatment;
  invoice?: Invoice;
  payment?: Payment;
}

export interface PatientCreationData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  tenantId: number;
  email?: string;
  address?: string;
  dateOfBirth?: Date;
  uid?: string;
}

export interface TreatmentData {
  findingId: number;
  procedureName: string;
  cost: number | string;
}

export interface FindingData {
  toothId: number;
  description: string;
  // Optional audit field for who recorded the finding
  recordedById?: number;
  severity?: "low" | "medium" | "high";
  // Optional inline treatment creation
  procedureName?: string;
  cost?: number | string;
  // Optional audit field for who created the treatment
  treatmentCreatedById?: number;
}

export interface PaymentData {
  patientId: number;
  invoiceId?: number | null;
  serial: string;
  amount: number | string;
  paymentMethod: "CASH" | "CARD" | "CHECK" | "BANK_TRANSFER" | "OTHER";
}

// Re-export for convenience
export { WorkflowError };

export class ClinicalWorkflowService {
  // Create patient, user (patient), case sheet, and all teeth in a single transaction
  async createPatientWithCompleteWorkflow(data: PatientCreationData): Promise<WorkflowResult> {
    // Note: Validation is already handled in the route layer
    
    try {
      const result = await prisma.$transaction(async (tx) => {
        // Check for duplicate patient by tenant + name
        const existing = await tx.patient.findFirst({
          where: {
            tenantId: data.tenantId,
            firstName: data.firstName.trim(),
            lastName: data.lastName.trim(),
          },
        });
        if (existing) {
          throw new Error("Patient already exists");
        }

        // Create patient record
        const patient = await tx.patient.create({
          data: {
            tenantId: data.tenantId,
            firstName: data.firstName.trim(),
            lastName: data.lastName.trim(),
            phoneNumber: data.phoneNumber.trim(),
            email: data.email?.toLowerCase().trim(),
            address: data.address?.trim(),
            dateOfBirth: data.dateOfBirth,
            uid: data.uid?.trim(),
            status: PatientStatus.ACTIVE,
          },
        });

        // Create linked PATIENT user using phoneNumber as username
        const user = await tx.user.create({
          data: {
            tenantId: data.tenantId,
            username: data.phoneNumber.trim(),
            phoneNumber: data.phoneNumber.trim(),
            password: "$2a$12$WdWvGmVwL3u3X2U3cvw1peTq3t7k7xv0b8oF9lR0m9qQw1r9a0d6C", // dummy bcrypt
            userType: "PATIENT",
            isActive: true,
            firstName: data.firstName.trim(),
            lastName: data.lastName.trim(),
          },
        });

        const serialCount = await tx.caseSheetSerial.count({ where: { tenantId: data.tenantId } });
        const serial = serialCount + 1;
        await tx.caseSheetSerial.create({ data: { tenantId: data.tenantId } });

        // Update patient with user reference
        await tx.patient.update({
          where: { id: patient.id },
          data: { userId: user.id },
        });

        // Create case sheet with all teeth (inline implementation since tx doesn't have extensions)
        const { generateFDINumbers, calculateQuadrant, calculatePosition, getToothName } = await import('../clinical/fdi-numbering');
        const { CaseSheetStatus } = await import('@prisma/client');
        
        // Prepare teeth creation data using FDI numbering
        const teethCreateData = generateFDINumbers().map((toothNumber) => ({
          tenantId: data.tenantId,
          toothNumber,
          quadrant: calculateQuadrant(toothNumber),
          positionInQuadrant: calculatePosition(toothNumber),
          toothName: getToothName(toothNumber),
          createdById: user.id,
        }));

        // Create case sheet with nested teeth
        const caseSheet = await tx.caseSheet.create({
          data: {
            tenantId: data.tenantId,
            patientId: patient.id,
            serial: serial.toString(),
            status: CaseSheetStatus.ACTIVE,
            createdById: user.id,
            teeth: {
              create: teethCreateData,
            },
          },
          include: {
            patient: true,
            teeth: {
              orderBy: { toothNumber: 'asc' },
            },
          },
        });
        const teethCount = caseSheet.teeth?.length ?? 32;

        return { patient, caseSheet, teethCount };
      }, { timeout: 20000 });

      return result;
    } catch (error) {
      // Simple error handling - let the route handle user messages
      throw error instanceof Error ? error : new Error("Failed to create patient");
    }
  }

  async searchPatientAndInitializeWorkflow(): Promise<WorkflowResult> {
    throw new Error("Not implemented");
  }

  // Individual workflow steps (signatures only for now)
  async createPatientWithUser(): Promise<{ patient: Patient }> {
    throw new Error("Not implemented");
  }

  async createCaseSheetWithTeeth(patientId: number): Promise<{ caseSheet: CaseSheet; teethCount: number }> {
    if (!Number.isInteger(patientId) || patientId <= 0) {
      throw new Error("Invalid patient ID");
    }

    const result = await prisma.$transaction(async (tx) => {
      // Ensure patient exists
      const patient = await tx.patient.findUnique({ where: { id: patientId } });
      if (!patient) {
        throw new Error("Patient not found");
      }

      // Ensure patient doesn't already have a case sheet
      const existingCaseSheet = await tx.caseSheet.findFirst({ 
        where: { tenantId: patient.tenantId, patientId } 
      });
      if (existingCaseSheet) {
        throw new Error("Patient already has a case sheet");
      }

      // Create case sheet with teeth (inline implementation since tx doesn't have extensions)
      const { generateFDINumbers, calculateQuadrant, calculatePosition, getToothName } = await import('../clinical/fdi-numbering');
      const { CaseSheetStatus } = await import('@prisma/client');
      
      // Prepare teeth creation data using FDI numbering
      const teethCreateData = generateFDINumbers().map((toothNumber) => ({
        tenantId: patient.tenantId,
        toothNumber,
        quadrant: calculateQuadrant(toothNumber),
        positionInQuadrant: calculatePosition(toothNumber),
        toothName: getToothName(toothNumber),
        createdById: undefined, // No specific user for this operation
      }));

      // Create case sheet with nested teeth
      const caseSheet = await tx.caseSheet.create({
        data: {
          tenantId: patient.tenantId,
          patientId: patientId,
          status: CaseSheetStatus.ACTIVE,
          teeth: {
            create: teethCreateData,
          },
        },
        include: {
          patient: true,
          teeth: {
            orderBy: { toothNumber: 'asc' },
          },
        },
      });
      const teethCount = caseSheet.teeth?.length ?? 32;

      return { caseSheet, teethCount };
    });

    return result;
  }

  async createFindingWithTreatment(_findingData: FindingData): Promise<{ finding: Finding; treatment?: Treatment }> {
    // Basic validation
    if (!Number.isInteger(_findingData.toothId) || _findingData.toothId <= 0) {
      throw new Error("Valid toothId is required");
    }
    if (!_findingData.description || !_findingData.description.trim()) {
      throw new Error("Description is required");
    }

    const hasTreatmentData =
      typeof _findingData.procedureName === "string" &&
      _findingData.procedureName.trim().length > 0 &&
      _findingData.cost !== undefined;

    try {
      const result = await prisma.$transaction(async (tx) => {
        // Validate tooth existence (tenant inferred downstream by extensions)
        const tooth = await tx.tooth.findUnique({ where: { id: _findingData.toothId } });
        if (!tooth) {
          throw new Error("Tooth not found");
        }

        // Create finding directly to avoid extension context issues; infer tenantId from tooth
        const finding = await tx.finding.create({
          data: {
            tenantId: tooth.tenantId,
            toothId: _findingData.toothId,
            description: _findingData.description.trim(),
            recordedById: _findingData.recordedById,
            severity: _findingData.severity ? String(_findingData.severity) : undefined,
            // createdById can mirror recordedById if provided
            ...(_findingData.recordedById ? { createdById: _findingData.recordedById } : {}),
          },
        });

        // Optionally create a treatment if provided inline
        let treatment: Treatment | undefined;
        if (hasTreatmentData) {
          const numericCost = typeof _findingData.cost === 'string' || typeof _findingData.cost === 'number'
            ? Number(_findingData.cost)
            : undefined;
          if (numericCost === undefined) {
            throw new Error("Invalid cost");
          }
          treatment = await tx.treatment.create({
            data: {
              tenantId: tooth.tenantId,
              findingId: finding.id,
              procedureName: _findingData.procedureName!.trim(),
              cost: numericCost,
              ...(_findingData.treatmentCreatedById ? { createdById: _findingData.treatmentCreatedById } : {}),
            },
          });
        }

        return { finding, treatment };
      });

      return result;
    } catch (error) {
      // Simple error handling - let database/validation errors bubble up
      throw error instanceof Error ? error : new Error("Failed to create finding");
    }
  }

  async createTreatmentWithInvoice(_treatmentData: TreatmentData): Promise<{ treatment: Treatment; invoice: Invoice }> {
    const errors: string[] = [];
    if (!Number.isInteger(_treatmentData.findingId) || _treatmentData.findingId <= 0) {
      errors.push("Valid findingId is required");
    }
    if (!_treatmentData.procedureName || !_treatmentData.procedureName.trim()) {
      errors.push("procedureName is required");
    }
    if (_treatmentData.cost === undefined || _treatmentData.cost === null || `${_treatmentData.cost}`.trim() === "") {
      errors.push("cost is required");
    }

    if (errors.length > 0) {
      throw new WorkflowError("Validation failed", {
        step: "TREATMENTS_PLANNED",
        rollbackRequired: false,
        userMessage: errors.join(", "),
      });
    }

    logger.info("createTreatmentWithInvoice:start", {
      findingId: _treatmentData.findingId,
      procedureName: _treatmentData.procedureName,
    });

    try {
      const result = await prisma.$transaction(async (tx) => {
        // Ensure finding exists and resolve patientId via tooth -> caseSheet
        const finding = await tx.finding.findUnique({
          where: { id: _treatmentData.findingId },
          include: {
            tooth: {
              include: {
                caseSheet: true,
              },
            },
          },
        });

        if (!finding) {
          throw new WorkflowError("Finding not found", {
            step: "TREATMENTS_PLANNED",
            rollbackRequired: false,
            userMessage: "Cannot create treatment: finding does not exist",
          });
        }

        const patientId = finding.tooth?.caseSheet?.patientId;
        if (!patientId) {
          throw new WorkflowError("Related patient not found", {
            step: "TREATMENTS_PLANNED",
            rollbackRequired: false,
            userMessage: "Unable to determine patient for invoice",
          });
        }

        const numericCost = typeof _treatmentData.cost === 'string' || typeof _treatmentData.cost === 'number'
          ? Number(_treatmentData.cost)
          : undefined;
        if (numericCost === undefined) {
          throw new WorkflowError("cost must be a number or numeric string", {
            step: "TREATMENTS_PLANNED",
            rollbackRequired: false,
            userMessage: "Invalid cost",
          });
        }
        const treatment = await tx.treatment.create({
          data: {
            tenantId: finding.tenantId,
            findingId: _treatmentData.findingId,
            procedureName: _treatmentData.procedureName.trim(),
            cost: numericCost,
          },
        });

        // Find an existing invoice that already contains treatments for THIS finding (preferred), still open (DRAFT/SENT)
        let invoice = await tx.invoice.findFirst({
          where: {
            patientId,
            treatments: { some: { findingId: _treatmentData.findingId } },
            OR: [{ status: InvoiceStatus.DRAFT }, { status: InvoiceStatus.SENT }],
          },
        });

        // If not found, create a fresh invoice dedicated to this finding
        if (!invoice) {
          const serialCount = await tx.invoiceSerial.count({ where: { tenantId: finding.tenantId } });
          const serial = serialCount + 1;
          await tx.invoiceSerial.create({ data: { tenantId: finding.tenantId } });

          const created = await tx.invoice.create({
            data: {
              tenantId: finding.tenantId,
              patientId,
              serial: serial.toString(),
              invoiceDate: new Date(),
              status: InvoiceStatus.DRAFT,
              totalAmount: 0,
              amountPaid: 0,
              balanceDue: 0,
            },
          });
          invoice = await tx.invoice.update({ where: { id: created.id }, data: { serial: `INV-${created.id}` } });
        }

        // Link ALL uninvoiced treatments under this finding to the chosen invoice, including the newly created one
        const uninvoicedTreatmentsForFinding = await tx.treatment.findMany({
          where: { findingId: _treatmentData.findingId, invoiceId: null },
          select: { id: true, cost: true },
        });

        const idsToLink = [treatment.id, ...uninvoicedTreatmentsForFinding.map((t: { id: number }) => t.id)].filter(
          (v, i, arr) => arr.indexOf(v) === i
        );

        if (idsToLink.length > 0) {
          await tx.treatment.updateMany({ where: { id: { in: idsToLink } }, data: { invoiceId: invoice.id } });
        }

        // Recalculate totals from all treatments currently linked to the invoice
        const allLinked = await tx.treatment.findMany({ where: { invoiceId: invoice.id }, select: { cost: true } });
        let newTotal = 0;
        for (const item of allLinked) {
          newTotal += Number(item.cost);
        }
        const newBalance = newTotal - Number(invoice.amountPaid);
        const updatedInvoice = await tx.invoice.update({
          where: { id: invoice.id },
          data: { totalAmount: newTotal, balanceDue: newBalance < 0 ? 0 : newBalance },
        });

        // Return the newly created treatment linked to the updated invoice
        const treatmentLinked = await tx.treatment.update({
          where: { id: treatment.id },
          data: { invoiceId: updatedInvoice.id },
        });

        return { treatment: treatmentLinked, invoice: updatedInvoice } as const;
      });

      logger.info("createTreatmentWithInvoice:success", {
        treatmentId: result.treatment.id,
        invoiceId: result.invoice.id,
      });

      return result;
    } catch (error) {
      await WorkflowErrorHandler.handleWorkflowError(error, {
        operation: "createTreatmentWithInvoice",
        findingId: _treatmentData.findingId,
      });
      if (error instanceof WorkflowError) {
        throw error;
      }
      throw new WorkflowError("Failed to create treatment and update invoice", {
        step: "TREATMENTS_PLANNED",
        rollbackRequired: true,
        userMessage: "Unable to save treatment and update invoice",
      });
    }
  }

  async processPaymentWithUpdate(_paymentData: PaymentData): Promise<{ payment: Payment; invoice?: Invoice }> {
    const errors: string[] = [];
    if (!Number.isInteger(_paymentData.patientId) || _paymentData.patientId <= 0) {
      errors.push("Valid patientId is required");
    }
    if (_paymentData.amount === undefined || _paymentData.amount === null || `${_paymentData.amount}`.trim() === "") {
      errors.push("amount is required");
    }
    if (!_paymentData.paymentMethod) {
      errors.push("paymentMethod is required");
    }

    if (errors.length > 0) {
      throw new WorkflowError("Validation failed", {
        step: "PAYMENTS_PROCESSED",
        rollbackRequired: false,
        userMessage: errors.join(", "),
      });
    }

    logger.info("processPaymentWithUpdate:start", {
      patientId: _paymentData.patientId,
      invoiceId: _paymentData.invoiceId ?? null,
      method: _paymentData.paymentMethod,
    });

    try {
      const result = await prisma.$transaction(async (tx) => {
        // Resolve target invoice if not provided: pick most recent with balanceDue > 0
        let targetInvoiceId: number | null | undefined = _paymentData.invoiceId ?? undefined;

        if (!targetInvoiceId) {
          const candidate = await tx.invoice.findFirst({
            where: {
              patientId: _paymentData.patientId,
              balanceDue: { gt: 0 },
            },
            orderBy: [{ invoiceDate: "desc" }],
          });
          targetInvoiceId = candidate?.id;
        }

        // If invoice provided or found, ensure it exists
        let preInvoice: Invoice | null = null;
        if (targetInvoiceId) {
          preInvoice = await tx.invoice.findUnique({ where: { id: targetInvoiceId } });
          if (!preInvoice) {
            throw new WorkflowError("Invoice not found", {
              step: "PAYMENTS_PROCESSED",
              rollbackRequired: false,
              userMessage: "Specified invoice does not exist",
            });
          }
        }

        // Create payment directly
        // Infer tenant from patient
        const patient = await tx.patient.findUnique({ where: { id: _paymentData.patientId } });
        if (!patient) {
          throw new WorkflowError("Patient not found", {
            step: "PAYMENTS_PROCESSED",
            rollbackRequired: false,
            userMessage: "Patient does not exist",
          });
        }
        const numericAmount = typeof _paymentData.amount === 'string' ? Number(_paymentData.amount) : (_paymentData.amount as number);
        if (!Number.isFinite(numericAmount)) {
          throw new WorkflowError("amount must be a finite number", {
            step: "PAYMENTS_PROCESSED",
            rollbackRequired: false,
            userMessage: "Invalid amount",
          });
        }
        const payment = await tx.payment.create({
          data: {
            tenantId: patient.tenantId,
            patientId: _paymentData.patientId,
            invoiceId: preInvoice ? preInvoice.id : null,
            serial: _paymentData.serial,
            amount: numericAmount,
            paymentDate: new Date(),
            paymentMethod: _paymentData.paymentMethod,
            status: PaymentStatus.COMPLETED,
          },
        });

        // Fetch updated invoice if linked
        let updatedInvoice: Invoice | undefined = undefined;
        if (payment.invoiceId) {
          const inv = await tx.invoice.findUnique({ where: { id: payment.invoiceId } });
          if (inv) {
            // Recalculate aggregates from completed payments
            const payments = await tx.payment.findMany({ where: { invoiceId: inv.id, status: PaymentStatus.COMPLETED } });
            const totalPaidNumber = payments.reduce((sum: number, p) => sum + Number(p.amount), 0);
            const newBalanceNumber = Number(inv.totalAmount) - totalPaidNumber;
            let newStatus = inv.status;
            if (totalPaidNumber >= Number(inv.totalAmount)) {
              newStatus = InvoiceStatus.PAID;
            } else if (totalPaidNumber > 0) {
              newStatus = InvoiceStatus.PARTIALLY_PAID;
            } else {
              // No payments and balance remaining
              newStatus = InvoiceStatus.SENT;
            }
            if (newBalanceNumber < 0) {
              // Clamp overpayment: set balance to 0 and amountPaid to totalAmount
              updatedInvoice = await tx.invoice.update({
                where: { id: inv.id },
                data: {
                  amountPaid: inv.totalAmount,
                  balanceDue: 0,
                  status: InvoiceStatus.PAID,
                },
              });
            } else {
              updatedInvoice = await tx.invoice.update({
                where: { id: inv.id },
                data: {
                  amountPaid: totalPaidNumber,
                  balanceDue: newBalanceNumber,
                  status: newStatus,
                },
              });
            }
          }
        }

        return { payment, invoice: updatedInvoice } as const;
      });

      logger.info("processPaymentWithUpdate:success", {
        paymentId: result.payment.id,
        invoiceId: result.invoice?.id ?? null,
      });

      return result;
    } catch (error) {
      await WorkflowErrorHandler.handleWorkflowError(error, {
        operation: "processPaymentWithUpdate",
        patientId: _paymentData.patientId,
        invoiceId: _paymentData.invoiceId ?? null,
      });
      if (error instanceof WorkflowError) {
        throw error;
      }
      throw new WorkflowError("Failed to process payment and update invoice", {
        step: "PAYMENTS_PROCESSED",
        rollbackRequired: true,
        userMessage: "Unable to record payment",
      });
    }
  }
}
