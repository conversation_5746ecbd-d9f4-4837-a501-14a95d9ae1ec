import { getCurrentTimestamp } from "../date-utils";

type LogLevel = 'debug' | 'info' | 'warn' | 'error'

function log(level: LogLevel, message: string, meta?: Record<string, unknown>) {
  const ts = getCurrentTimestamp()
  const base = { level, ts, message }
  const payload = meta ? { ...base, ...meta } : base
   
  console[level === 'debug' ? 'log' : level](JSON.stringify(payload))
}

export const logger = {
  debug: (message: string, meta?: Record<string, unknown>) => log('debug', message, meta),
  info: (message: string, meta?: Record<string, unknown>) => log('info', message, meta),
  warn: (message: string, meta?: Record<string, unknown>) => log('warn', message, meta),
  error: (message: string, meta?: Record<string, unknown>) => log('error', message, meta),
}
