import { z } from "zod"

export const paymentFormSchema = z.object({
  amount: z
    .string()
    .min(1, "Amount is required")
    .refine((v) => !Number.isNaN(Number(v)), "Enter a valid number")
    .refine((v) => Number(v) > 0, "Amount must be greater than 0"),
  paymentMethod: z.enum(["cash", "card", "bank_transfer", "insurance", "other"]),
  paymentDate: z.string().min(1, "Payment date is required"),
  notes: z.string().optional().or(z.literal("")),
})

export type PaymentFormData = z.infer<typeof paymentFormSchema>
