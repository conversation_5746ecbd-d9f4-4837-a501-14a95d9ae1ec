import { z } from "zod"

export const treatmentFormSchema = z.object({
  procedureName: z.string().min(1, "Procedure name is required").max(100, "Keep under 100 characters").trim(),
  cost: z
    .string()
    .min(1, "Cost is required")
    .refine((v) => !Number.isNaN(Number(v)), "Enter a valid number")
    .refine((v) => Number(v) >= 0, "Cost must be positive"),
  notes: z.string().max(1000, "Keep notes concise").optional().or(z.literal("")),
})

export type TreatmentFormData = z.infer<typeof treatmentFormSchema>
