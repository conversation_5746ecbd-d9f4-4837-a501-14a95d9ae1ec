/**
 * Common field types and utilities for Prisma models
 * 
 * This file provides TypeScript utility types and interfaces that work with
 * the common fields pattern used across all Prisma models in the application.
 */

import type { User, Tenant } from '@prisma/client'

/**
 * Base fields that every model (except Tenant) should have
 */
export interface BaseModelFields {
  id: number
  tenantId: number
  createdAt: Date
  updatedAt: Date
}

/**
 * Audit fields for tracking who created/updated records
 */
export interface AuditFields {
  createdById: number | null
  updatedById: number | null
}

/**
 * Complete common fields interface combining base and audit fields
 */
export interface CommonFields extends BaseModelFields, AuditFields {}

/**
 * Common fields with relations populated
 */
export interface CommonFieldsWithRelations extends BaseModelFields, AuditFields {
  tenant: Tenant
  createdBy: User | null
  updatedBy: User | null
}

/**
 * Utility type to extract common fields from any model type
 */
export type ExtractCommonFields<T> = Pick<T, keyof CommonFields & keyof T>

/**
 * Utility type to omit common fields from a model type
 * Useful when creating input types for forms or APIs
 */
export type OmitCommonFields<T> = Omit<T, keyof CommonFields>

/**
 * Utility type for creating new records (omits id, timestamps, and audit fields)
 */
export type CreateInput<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt' | 'createdById' | 'updatedById'>

/**
 * Utility type for updating records (omits id, createdAt, and makes other fields optional)
 */
export type UpdateInput<T> = Partial<Omit<T, 'id' | 'createdAt'>>

/**
 * Utility type for models with tenant relation included
 */
export type WithTenant<T> = T & { tenant: Tenant }

/**
 * Utility type for models with audit relations included
 */
export type WithAuditRelations<T> = T & {
  createdBy: User | null
  updatedBy: User | null
}

/**
 * Utility type for models with all common relations included
 */
export type WithCommonRelations<T> = WithTenant<WithAuditRelations<T>>

/**
 * Base query options that can be used across all models
 */
export interface BaseQueryOptions {
  tenantId: number
  include?: {
    tenant?: boolean
    createdBy?: boolean
    updatedBy?: boolean
  }
  orderBy?: {
    createdAt?: 'asc' | 'desc'
    updatedAt?: 'asc' | 'desc'
  }
}

/**
 * Pagination options
 */
export interface PaginationOptions {
  skip?: number
  take?: number
}

/**
 * Complete query options combining base options with pagination
 */
export interface QueryOptions extends BaseQueryOptions, PaginationOptions {}

/**
 * Date range filter for common timestamp fields
 */
export interface DateRangeFilter {
  from?: Date
  to?: Date
}

/**
 * Common filters that can be applied to any model
 */
export interface CommonFilters {
  tenantId: number
  createdAt?: DateRangeFilter
  updatedAt?: DateRangeFilter
  createdById?: number | null
  updatedById?: number | null
}

/**
 * Utility function to create base where clause for tenant isolation
 */
export const createTenantWhere = (tenantId: number) => ({ tenantId })

/**
 * Utility function to create date range where clause
 */
export const createDateRangeWhere = (field: 'createdAt' | 'updatedAt', range: DateRangeFilter) => {
  const where: Record<string, { gte?: Date; lte?: Date }> = {}
  if (range.from || range.to) {
    where[field] = {}
    if (range.from) where[field].gte = range.from
    if (range.to) where[field].lte = range.to
  }
  return where
}

/**
 * Utility function to create common include options
 */
export const createCommonInclude = (options: BaseQueryOptions['include'] = {}) => ({
  tenant: options.tenant || false,
  createdBy: options.createdBy || false,
  updatedBy: options.updatedBy || false,
})

/**
 * Utility function to create common order by options
 */
export const createCommonOrderBy = (options: BaseQueryOptions['orderBy'] = {}) => ({
  createdAt: options.createdAt || 'desc',
  updatedAt: options.updatedAt || 'desc',
})


/**
 * Utility to extract only the common fields from a model instance
 */
export const extractCommonFields = <T extends CommonFields>(model: T): CommonFields => ({
  id: model.id,
  tenantId: model.tenantId,
  createdAt: model.createdAt,
  updatedAt: model.updatedAt,
  createdById: model.createdById,
  updatedById: model.updatedById,
})

/**
 * Utility to create audit data for new records
 */
export const createAuditData = (userId?: number | null) => ({
  createdById: userId || null,
  updatedById: userId || null,
})

/**
 * Utility to create update audit data
 */
export const createUpdateAuditData = (userId?: number | null) => ({
  updatedById: userId || null,
})
