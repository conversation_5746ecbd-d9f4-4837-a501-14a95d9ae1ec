import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware, RoleGuard } from "@/lib/auth/services";
import { TenantService } from "@/lib/auth/services/tenant-service";
import { BlobService } from "@/lib/blob-utils";

const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
const ALLOWED_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

/**
 * POST - Upload clinic logo
 */
const _POST = AuthMiddleware.withAuth(
  async (req: NextRequest, { authContext }) => {
    try {
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      // Check admin privileges
      if (authContext.user?.userType !== 'ADMIN') {
        return NextResponse.json(
          { success: false, message: "Admin privileges required" },
          { status: 403 }
        );
      }

      // Parse form data
      const formData = await req.formData();
      const file = formData.get("logo") as File;

      if (!file) {
        return NextResponse.json(
          { success: false, message: "No file provided" },
          { status: 400 }
        );
      }

      // Validate file
      if (!ALLOWED_TYPES.includes(file.type)) {
        return NextResponse.json(
          { success: false, message: "Invalid file type. Please upload PNG, JPG, or WebP images." },
          { status: 400 }
        );
      }

      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { success: false, message: "File too large. Maximum size is 2MB." },
          { status: 400 }
        );
      }

      // Get current tenant to check for existing logo
      const currentTenant = await TenantService.getTenantById(tenantId);
      
      // Generate unique filename path
      const filename = BlobService.createFilename(file, tenantId, 'logos');
      
      // Upload to Vercel Blob
      const blob = await BlobService.uploadFile(file, filename, tenantId, {
        allowOverwrite: true,
        onUploadProgress: (event: any) => {
        }
      });

      // Update tenant with new logo URL
      const updateResult = await TenantService.updateTenantInfo(tenantId, {
        logoImage: blob.url,
      });

      if (!updateResult.success) {
        // If update failed, remove the uploaded blob
        try {
          await BlobService.deleteFile(blob.url);
        } catch (deleteError) {
          console.warn("Failed to clean up blob after database update failure:", deleteError);
        }
        
        return NextResponse.json(
          { success: false, message: "Failed to update tenant with logo information" },
          { status: 500 }
        );
      }

      // Clean up old blob if it exists and is a blob URL
      if (currentTenant?.logoImage && BlobService.isBlobUrl(currentTenant.logoImage)) {
        try {
          await BlobService.deleteFile(currentTenant.logoImage);
        } catch (deleteError) {
          console.warn("Failed to remove old blob:", deleteError);
          // Don't fail the request if old file removal fails
        }
      }

      return NextResponse.json({
        success: true,
        message: "Logo uploaded successfully",
        data: {
          logoUrl: blob.url,
          fileName: blob.pathname,
        }
      });

    } catch (error) {
      console.error("Error uploading logo:", error);
      return NextResponse.json(
        { success: false, message: "Failed to upload logo" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function POST(request: NextRequest) {
  return _POST(request);
}

/**
 * DELETE - Remove clinic logo
 */
const _DELETE = AuthMiddleware.withAuth(
  async (_req: NextRequest, { authContext }) => {
    try {
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      // Check admin privileges
      if (authContext.user?.userType !== 'ADMIN') {
        return NextResponse.json(
          { success: false, message: "Admin privileges required" },
          { status: 403 }
        );
      }

      // Get current tenant to find logo file
      const currentTenant = await TenantService.getTenantById(tenantId);
      
      if (!currentTenant?.logoImage) {
        return NextResponse.json(
          { success: false, message: "No logo to delete" },
          { status: 400 }
        );
      }

      // Remove logo from database
      const updateResult = await TenantService.updateTenantInfo(tenantId, {
        logoImage: null,
      });

      if (!updateResult.success) {
        return NextResponse.json(
          { success: false, message: "Failed to remove logo from database" },
          { status: 500 }
        );
      }

      // Remove logo file if it's a blob URL
      if (BlobService.isBlobUrl(currentTenant.logoImage)) {
        try {
          await BlobService.deleteFile(currentTenant.logoImage);
        } catch (deleteError) {
          console.warn("Failed to remove logo blob:", deleteError);
          // Don't fail the request if file removal fails
        }
      }

      return NextResponse.json({
        success: true,
        message: "Logo removed successfully"
      });

    } catch (error) {
      console.error("Error removing logo:", error);
      return NextResponse.json(
        { success: false, message: "Failed to remove logo" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function DELETE(request: NextRequest) {
  return _DELETE(request);
}
