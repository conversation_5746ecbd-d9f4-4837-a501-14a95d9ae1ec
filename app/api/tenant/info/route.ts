import { NextRequest, NextResponse } from "next/server";
import { TenantService } from "@/lib/auth/services/tenant-service";
import { AuthMiddleware, RoleGuard } from "@/lib/auth/services";

/**
 * GET - Get tenant/clinic information
 */
const _GET = AuthMiddleware.withAuth(
  async (_req: NextRequest, { authContext }) => {
    try {
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      const tenant = await TenantService.getTenantById(tenantId);
      
      if (!tenant) {
        return NextResponse.json(
          { success: false, message: "Clinic not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          id: tenant.id,
          name: tenant.name,
          address: tenant.address,
          phoneNumber: tenant.phoneNumber,
          logoImage: tenant.logoImage,
          createdAt: tenant.createdAt,
        }
      });
    } catch (error) {
      console.error("Error fetching tenant info:", error);
      return NextResponse.json(
        { success: false, message: "Failed to fetch clinic information" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function GET(request: NextRequest) {
  return _GET(request);
}

/**
 * PUT - Update tenant/clinic information
 */
const _PUT = AuthMiddleware.withAuth(
  async (req: NextRequest, { authContext }) => {
    try {
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      // Check if user has admin privileges
      if (authContext.user?.userType !== 'ADMIN') {
        return NextResponse.json(
          { success: false, message: "Admin privileges required" },
          { status: 403 }
        );
      }

      const body = await req.json();
      const { name, address, phone } = body;

      // Validate required fields
      if (!name || name.trim().length < 2) {
        return NextResponse.json(
          { success: false, message: "Clinic name must be at least 2 characters long" },
          { status: 400 }
        );
      }

      const result = await TenantService.updateTenantInfo(tenantId, {
        name: name.trim(),
        address: address?.trim(),
        phone: phone?.trim(),
      });

      if (!result.success) {
        return NextResponse.json(result, { status: 400 });
      }

      // Fetch updated tenant info
      const updatedTenant = await TenantService.getTenantById(tenantId);
      
      return NextResponse.json({
        success: true,
        message: "Clinic information updated successfully",
        data: {
          id: updatedTenant?.id,
          name: updatedTenant?.name,
          address: updatedTenant?.address,
          phoneNumber: updatedTenant?.phoneNumber,
          logoImage: updatedTenant?.logoImage,
          createdAt: updatedTenant?.createdAt,
        }
      });
    } catch (error) {
      console.error("Error updating tenant info:", error);
      return NextResponse.json(
        { success: false, message: "Failed to update clinic information" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function PUT(request: NextRequest) {
  return _PUT(request);
}
