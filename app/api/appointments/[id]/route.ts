import { NextRequest, NextResponse } from "next/server"
// RouteContext is not exported in Next 15; type inline instead
import prisma from "@/lib/prisma"
import { AuthContext, AuthMiddleware } from "@/lib/auth/services"

// GET /api/appointments/[id]
const _GET = AuthMiddleware.withAuth(
async (_request: NextRequest, context: { params?: Promise<Record<string, string>> | Record<string, string>; authContext: AuthContext }) => {
  try {
    const paramsObj: Record<string, string> = typeof (context).params?.then === 'function'
      ? await (context).params
      : (((context).params as Record<string, string> | undefined) || ({} as Record<string, string>))
    const id = Number(paramsObj?.id)
    if (!Number.isInteger(id) || id <= 0) {
      return NextResponse.json({ success: false, message: "Invalid appointment id" }, { status: 400 })
    }

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const appointment = await prisma.appointment.findFirst({
      where: { id, tenantId },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
            email: true
          }
        },
        primaryProvider: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    if (!appointment) {
      return NextResponse.json({ success: false, message: "Appointment not found" }, { status: 404 })
    }

    return NextResponse.json(
      {
        success: true,
        appointment: {
          id: appointment.id,
          title: `${appointment.patient.firstName} ${appointment.patient.lastName}`,
          start: appointment.appointmentDate,
          end: new Date(appointment.appointmentDate.getTime() + appointment.durationMinutes * 60000),
          appointmentType: appointment.appointmentType,
          status: appointment.status,
          notes: appointment.notes,
          patient: appointment.patient,
          primaryProvider: appointment.primaryProvider,
          backgroundColor: appointment.status === 'SCHEDULED' ? '#3b82f6' : 
                          appointment.status === 'COMPLETED' ? '#10b981' : 
                          appointment.status === 'CANCELLED' ? '#ef4444' : '#6b7280',
          color: 'white'
        }
      },
      { status: 200 }
    )
  } catch (e) {
    console.error("Failed to fetch appointment:", e)
    return NextResponse.json({ success: false, message: "Failed to fetch appointment" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _GET(request, { params } as any)
}

// PATCH /api/appointments/[id]
const _PATCH = AuthMiddleware.withAuth(
async (request: NextRequest, context: { params?: Promise<Record<string, string>> | Record<string, string>; authContext: AuthContext }) => {
  try {
    const paramsObj: Record<string, string> = typeof (context).params?.then === 'function'
      ? await (context).params
      : (((context).params as Record<string, string> | undefined) || ({} as Record<string, string>))
    const id = Number(paramsObj?.id)
    if (!Number.isInteger(id) || id <= 0) {
      return NextResponse.json({ success: false, message: "Invalid appointment id" }, { status: 400 })
    }

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    // Ensure the appointment belongs to the tenant before updating
    const existingAppointment = await prisma.appointment.findFirst({
      where: { id, tenantId }
    });
    if (!existingAppointment) {
      return NextResponse.json({ success: false, message: "Appointment not found" }, { status: 404 });
    }

    const body = await request.json().catch(() => ({}))
    const {
      appointmentDate,
      durationMinutes,
      appointmentType,
      status,
      notes,
      primaryProviderId
    } = body ?? {}

    // Build update data
    const updateData: Record<string, unknown> = {
      updatedById: context.authContext?.user?.id ? Number(context.authContext.user.id) : null
    }

    if (appointmentDate) updateData.appointmentDate = new Date(appointmentDate)
    if (durationMinutes) updateData.durationMinutes = Number(durationMinutes)
    if (appointmentType) updateData.appointmentType = appointmentType
    if (status) updateData.status = status
    if (notes !== undefined) updateData.notes = notes ? String(notes).trim() : null
    if (primaryProviderId !== undefined) updateData.primaryProviderId = primaryProviderId ? Number(primaryProviderId) : null

    const updated = await prisma.appointment.update({
      where: { id },
      data: updateData,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
            email: true
          }
        },
        primaryProvider: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    return NextResponse.json(
      {
        success: true,
        appointment: {
          id: updated.id,
          title: `${updated.patient.firstName} ${updated.patient.lastName}`,
          start: updated.appointmentDate,
          end: new Date(updated.appointmentDate.getTime() + updated.durationMinutes * 60000),
          appointmentType: updated.appointmentType,
          status: updated.status,
          notes: updated.notes,
          patient: updated.patient,
          primaryProvider: updated.primaryProvider,
          backgroundColor: updated.status === 'SCHEDULED' ? '#3b82f6' : 
                          updated.status === 'COMPLETED' ? '#10b981' : 
                          updated.status === 'CANCELLED' ? '#ef4444' : '#6b7280',
          color: 'white'
        }
      },
      { status: 200 }
    )
  } catch (e) {
    console.error("Failed to update appointment:", e)
    return NextResponse.json({ success: false, message: "Failed to update appointment" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function PATCH(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _PATCH(request, { params } as any)
}

// DELETE /api/appointments/[id]
const _DELETE = AuthMiddleware.withAuth(
async (_request: NextRequest, context: { params?: Promise<Record<string, string>> | Record<string, string>; authContext: AuthContext }) => {
  try {
    const paramsObj: Record<string, string> = typeof (context).params?.then === 'function'
      ? await (context).params
      : (((context).params as Record<string, string> | undefined) || ({} as Record<string, string>))
    const id = Number(paramsObj?.id)
    if (!Number.isInteger(id) || id <= 0) {
      return NextResponse.json({ success: false, message: "Invalid appointment id" }, { status: 400 })
    }

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    // Ensure the appointment belongs to the tenant before deleting
    const existingAppointment = await prisma.appointment.findFirst({
      where: { id, tenantId }
    });
    if (!existingAppointment) {
      return NextResponse.json({ success: false, message: "Appointment not found" }, { status: 404 });
    }

    await prisma.appointment.delete({
      where: { id }
    })

    return NextResponse.json({ success: true }, { status: 200 })
  } catch (e) {
    console.error("Failed to delete appointment:", e)
    return NextResponse.json({ success: false, message: "Failed to delete appointment" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _DELETE(request, { params } as any)
}
