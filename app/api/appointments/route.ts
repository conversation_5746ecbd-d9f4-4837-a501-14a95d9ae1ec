import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { AuthContext, AuthMiddleware } from "@/lib/auth/services"

// GET /api/appointments
const _GET = AuthMiddleware.withAuth(
async (request: NextRequest, context: { authContext: AuthContext }) => {
  try {
    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const url = new URL(request.url)
    const searchParams = url.searchParams
    
    // Parse date range from query params
    const startDate = searchParams.get('start')
    const endDate = searchParams.get('end')

    const whereClause: { tenantId: number; appointmentDate?: { gte: Date; lte: Date } } = { tenantId }

    // If date range is provided, filter by date
    if (startDate && endDate) {
      whereClause.appointmentDate = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    const appointments = await prisma.appointment.findMany({
      where: whereClause,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
            email: true
          }
        },
        primaryProvider: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        appointmentDate: 'asc'
      }
    })

    return NextResponse.json(
      {
        success: true,
        appointments: appointments.map((appointment: any) => ({
          id: appointment.id,
          title: `${appointment.patient.firstName} ${appointment.patient.lastName}`,
          start: appointment.appointmentDate,
          end: new Date(appointment.appointmentDate.getTime() + appointment.durationMinutes * 60000),
          appointmentType: appointment.appointmentType,
          status: appointment.status,
          notes: appointment.notes,
          patient: appointment.patient,
          primaryProvider: appointment.primaryProvider,
          backgroundColor: appointment.status === 'SCHEDULED' ? '#3b82f6' : 
                          appointment.status === 'COMPLETED' ? '#10b981' : 
                          appointment.status === 'CANCELLED' ? '#ef4444' : '#6b7280',
          color: 'white'
        }))
      },
      { status: 200 }
    )
  } catch (e) {
    console.error("Failed to fetch appointments:", e)
    return NextResponse.json({ success: false, message: "Failed to fetch appointments" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest) {
  return _GET(request)
}

// POST /api/appointments
const _POST = AuthMiddleware.withAuth(
async (request: NextRequest, context: { authContext: AuthContext }) => {
  try {
    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const body = await request.json().catch(() => ({}))
    const {
      patientId,
      appointmentDate,
      durationMinutes = 60,
      appointmentType = 'CONSULTATION',
      notes,
      primaryProviderId
    } = body ?? {}

    // Basic validation
    const errors: string[] = []
    if (!patientId || !Number.isInteger(Number(patientId))) errors.push("patientId is required and must be a valid number")
    if (!appointmentDate) errors.push("appointmentDate is required")
    
    if (errors.length > 0) {
      return NextResponse.json({ success: false, message: "Validation failed", errors }, { status: 400 })
    }

    // Verify patient exists and belongs to the tenant
    const patient = await prisma.patient.findFirst({
      where: { id: Number(patientId), tenantId }
    });
    if (!patient) {
      return NextResponse.json({ success: false, message: "Patient not found" }, { status: 404 });
    }

    // Verify provider exists and belongs to the tenant (if provided)
    if (primaryProviderId) {
      const provider = await prisma.user.findFirst({
        where: { id: Number(primaryProviderId), tenantId }
      });
      if (!provider) {
        return NextResponse.json({ success: false, message: "Provider not found" }, { status: 404 });
      }
    }

    const appointment = await prisma.appointment.create({
      data: {
        tenantId,
        patientId: Number(patientId),
        appointmentDate: new Date(appointmentDate),
        durationMinutes: Number(durationMinutes),
        appointmentType,
        notes: notes ? String(notes).trim() : null,
        primaryProviderId: primaryProviderId ? Number(primaryProviderId) : null,
        createdById: context.authContext?.user?.id ? Number(context.authContext.user.id) : null,
        updatedById: context.authContext?.user?.id ? Number(context.authContext.user.id) : null
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
            email: true
          }
        },
        primaryProvider: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    return NextResponse.json(
      {
        success: true,
        appointment: {
          id: appointment.id,
          title: `${appointment.patient.firstName} ${appointment.patient.lastName}`,
          start: appointment.appointmentDate,
          end: new Date(appointment.appointmentDate.getTime() + appointment.durationMinutes * 60000),
          appointmentType: appointment.appointmentType,
          status: appointment.status,
          notes: appointment.notes,
          patient: appointment.patient,
          primaryProvider: appointment.primaryProvider,
          backgroundColor: '#3b82f6',
          color: 'white'
        }
      },
      { status: 201 }
    )
  } catch (e) {
    console.error("Failed to create appointment:", e)
    return NextResponse.json({ success: false, message: "Failed to create appointment" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function POST(request: NextRequest) {
  return _POST(request)
}
