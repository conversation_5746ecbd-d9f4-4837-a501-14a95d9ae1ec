import { NextRequest, NextResponse } from "next/server";
// RouteContext removed in Next 15; inline context types instead
import { ClinicalWorkflowService, WorkflowError } from "@/lib/clinical/workflow-service";
import { AuthMiddleware, AuthContext } from "@/lib/auth/services";

// POST /api/clinical/teeth/[id]/findings
const _POST = AuthMiddleware.withAuth(async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>)
    const { user } = context.authContext
    const { id } = params
    const body = await request.json();

    const errors: string[] = [];

    const toothId = Number(id);
    if (!Number.isInteger(toothId) || toothId <= 0) {
      errors.push("Tooth ID in path must be a positive integer");
    }

    const description = typeof body?.description === "string" ? body.description.trim() : "";
    const severityRaw = typeof body?.severity === "string" ? body.severity.trim().toLowerCase() : undefined
    const severity = severityRaw && ["low", "medium", "high"].includes(severityRaw) ? (severityRaw as "low" | "medium" | "high") : undefined
    if (!description) {
      errors.push("description is required");
    }

    if (errors.length > 0) {
      return NextResponse.json(
        { success: false, message: "Validation failed", errors },
        { status: 400 }
      );
    }

    if (!user) {
      return NextResponse.json(
        { success: false, message: "Authentication required" },
        { status: 401 }
      );
    }

    const service = new ClinicalWorkflowService();
    const authUserId = Number.isFinite(Number(user.id)) ? Number(user.id) : undefined
    const { finding, treatment } = await service.createFindingWithTreatment({
      toothId,
      description,
      procedureName: typeof body?.procedureName === "string" ? body.procedureName : undefined,
      cost: body?.cost !== undefined ? body.cost : undefined,
      recordedById: Number.isFinite(Number(body?.recordedById)) ? Number(body.recordedById) : authUserId,
      severity,
      treatmentCreatedById: Number.isFinite(Number(body?.treatmentCreatedById)) ? Number(body.treatmentCreatedById) : authUserId,
    });

    return NextResponse.json(
      {
        success: true,
        message: treatment ? "Finding and treatment created" : "Finding created",
        finding,
        treatment: treatment ?? null,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof WorkflowError) {
      return NextResponse.json(
        { success: false, message: error.userMessage, step: error.step },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { success: false, message: "Failed to create finding" },
      { status: 500 }
    );
  } finally {}
}, { requireAuth: true, requireTenant: true })

export async function POST(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _POST(request, { params } as any);
}

export async function GET() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}
