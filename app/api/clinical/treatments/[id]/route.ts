import { NextRequest, NextResponse } from "next/server";
// RouteContext is not exported by next/server in Next 15; inline context types instead
import prisma from "@/lib/prisma";
import { AuthMiddleware, AuthContext } from "@/lib/auth/services";

// PATCH /api/clinical/treatments/[id]
const _PATCH = AuthMiddleware.withAuth(
  async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
    const params = context.params || ({} as Record<string, string>)
    const { user } = context.authContext
    try {
      const idRaw = params?.id
      const treatmentId = Number(idRaw)
      if (!Number.isInteger(treatmentId) || treatmentId <= 0) {
        return NextResponse.json({ success: false, message: "treatmentId must be a positive integer" }, { status: 400 })
      }

      const body = await request.json()
      const updateData: any = {}
      if (typeof body?.procedureName === "string") updateData.procedureName = body.procedureName.trim()
      if (body?.status) {
        const s = String(body.status).toLowerCase()
        updateData.status = s === "completed" ? "COMPLETED" : "PENDING"
        if (updateData.status === "COMPLETED") {
          updateData.completedDate = new Date()
          const userIdNum = Number(user?.id ?? NaN)
          if (Number.isFinite(userIdNum)) updateData.completedBy = { connect: { id: userIdNum } }
        } else {
          // If moving back to pending, clear completion info
          updateData.completedDate = null
          updateData.completedBy = { disconnect: true }
        }
      }
      const hasCost = body?.cost !== undefined && body?.cost !== null && String(body.cost).trim() !== ""
      const costNumber = hasCost ? Number(body.cost) : undefined
      if (hasCost && !Number.isFinite(costNumber)) {
        return NextResponse.json({ success: false, message: "cost must be a valid number" }, { status: 400 })
      }

      // Fetch existing treatment and context
      const existing = await prisma.treatment.findUnique({
        where: { id: treatmentId },
        include: {
          finding: {
            include: {
              tooth: { include: { caseSheet: true } },
            },
          },
        },
      })
      if (!existing) {
        return NextResponse.json({ success: false, message: "Treatment not found" }, { status: 404 })
      }

      const oldCost = Number(existing.cost)
      if (hasCost) updateData.cost = costNumber

      const updated = await prisma.treatment.update({ where: { id: treatmentId }, data: updateData })

      // Update open invoice totals if cost changed and patient context available
      if (hasCost && Number.isFinite(oldCost)) {
        const patientId = existing.finding?.tooth?.caseSheet?.patientId
        const tenantId = (existing).tenantId as number | undefined
        if (patientId && tenantId) {
          const invoice = await prisma.invoice.findFirst({
            where: { patientId, tenantId, OR: [{ status: "DRAFT" }, { status: "SENT" }] },
          })
          if (invoice) {
            const delta = Number(costNumber) - oldCost
            const newTotal = Number(invoice.totalAmount) + delta
            const newBalance = newTotal - Number(invoice.amountPaid)
            await prisma.invoice.update({
              where: { id: invoice.id },
              data: {
                totalAmount: (newTotal < 0 ? 0 : newTotal),
                balanceDue: (newBalance < 0 ? 0 : newBalance),
              },
            })
          }
        }
      }

      return NextResponse.json({ success: true, treatment: updated }, { status: 200 })
    } catch {
      return NextResponse.json({ success: false, message: "Failed to update treatment" }, { status: 500 })
    }
  },
  { requireAuth: true, requireTenant: true }
)

export async function PATCH(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _PATCH(request, { params } as any);
}

// DELETE /api/clinical/treatments/[id]
const _DELETE = AuthMiddleware.withAuth(
  async (_request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
    const params = context.params || ({} as Record<string, string>)
    try {
      const idRaw = params?.id
      const treatmentId = Number(idRaw)
      if (!Number.isInteger(treatmentId) || treatmentId <= 0) {
        return NextResponse.json({ success: false, message: "treatmentId must be a positive integer" }, { status: 400 })
      }

      // Fetch existing with context for invoice adjustment
      const existing = await prisma.treatment.findUnique({
        where: { id: treatmentId },
        include: {
          finding: { include: { tooth: { include: { caseSheet: true } } } },
        },
      })
      if (!existing) {
        return NextResponse.json({ success: false, message: "Treatment not found" }, { status: 404 })
      }

      const oldCost = Number(existing.cost)
      const patientId = existing.finding?.tooth?.caseSheet?.patientId
      const tenantId = (existing).tenantId as number | undefined

      await prisma.treatment.delete({ where: { id: treatmentId } })

      // Adjust invoice totals if open invoice exists
      if (patientId && tenantId && Number.isFinite(oldCost)) {
        const invoice = await prisma.invoice.findFirst({
          where: { patientId, tenantId, OR: [{ status: "DRAFT" }, { status: "SENT" }] },
        })
        if (invoice) {
          const newTotal = Number(invoice.totalAmount) - oldCost
          const newBalance = newTotal - Number(invoice.amountPaid)
          await prisma.invoice.update({
            where: { id: invoice.id },
            data: {
              totalAmount: (newTotal < 0 ? 0 : newTotal),
              balanceDue: (newBalance < 0 ? 0 : newBalance),
              status: newTotal <= 0 ? ("DRAFT") : invoice.status,
            },
          })
        }
      }

      return NextResponse.json({ success: true }, { status: 200 })
    } catch {
      return NextResponse.json({ success: false, message: "Failed to delete treatment" }, { status: 500 })
    }
  },
  { requireAuth: true, requireTenant: true }
)

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _DELETE(request, { params } as any);
}


