import { NextRequest, NextResponse } from "next/server";
// RouteContext removed in Next 15; inline context types instead
import prisma from "@/lib/prisma";
import { AuthContext, AuthMiddleware } from "@/lib/auth/services";

// GET /api/clinical/case-sheets/[id]
const _GET = AuthMiddleware.withAuth(
  async (_request: NextRequest, context: { params?: Promise<Record<string, string>> | Record<string, string>; authContext: AuthContext }) => {
    try {
      const paramsObj: Record<string, string> = typeof (context).params?.then === 'function'
        ? await (context).params
        : (((context).params as Record<string, string> | undefined) || ({} as Record<string, string>))
      const authContext = (context).authContext
      const idRaw = paramsObj?.id
      const caseSheetId = Number(idRaw)
      if (!Number.isInteger(caseSheetId) || caseSheetId <= 0) {
        return NextResponse.json(
          { success: false, message: "caseSheetId must be a positive integer" },
          { status: 400 }
        )
      }

      const resolvedTenantId = String(authContext?.user?.tenantId ?? authContext?.tenant?.id ?? "").trim()
      if (!resolvedTenantId) {
        return NextResponse.json(
          { success: false, message: "Missing tenant context" },
          { status: 400 }
        )
      }

      // Ensure tenant isolation
      const caseSheet = await prisma.caseSheet.findFirst({
        where: { id: caseSheetId, tenantId: resolvedTenantId },
        include: {
          patient: true,
          teeth: {
            include: {
              findings: {
                include: { treatments: true, recordedBy: true },
                orderBy: { recordedDate: "desc" },
              },
            },
            orderBy: { toothNumber: "asc" },
          },
        },
      })

      if (!caseSheet) {
        return NextResponse.json(
          { success: false, message: "Case sheet not found" },
          { status: 404 }
        )
      }

      return NextResponse.json({ success: true, caseSheet }, { status: 200 })
    } catch {
      return NextResponse.json(
        { success: false, message: "Failed to load case sheet" },
        { status: 500 }
      )
    }
  },
  { requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _GET(request, { params } as any);
}


