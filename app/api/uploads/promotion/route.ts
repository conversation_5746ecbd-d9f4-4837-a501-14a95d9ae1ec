import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/auth/services";
import { BlobService } from "@/lib/blob-utils";

const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const ALLOWED_TYPES = [
  // Images
  "image/jpeg", "image/jpg", "image/png", "image/webp", "image/gif",
  // Videos
  "video/mp4", "video/webm", "video/ogg", "video/quicktime",
];

const _POST = AuthMiddleware.withAuth(
  async (req: NextRequest, { authContext }) => {
    try {
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      } 

      const formData = await req.formData();
      const file = formData.get("file") as File | null;
      if (!file) {
        return NextResponse.json(
          { success: false, message: "No file provided" },
          { status: 400 }
        );
      }

      if (!ALLOWED_TYPES.includes(file.type)) {
        return NextResponse.json(
          { success: false, message: "Invalid file type. Upload image or video files." },
          { status: 400 }
        );
      }

      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { success: false, message: "File too large. Maximum size is 20MB." },
          { status: 400 }
        );
      }

      // Generate unique filename path
      const filename = BlobService.createFilename(file, tenantId, 'promotions/general');

      // Upload to Vercel Blob with multipart for large files
      const blob = await BlobService.uploadFile(file, filename, tenantId, {
        multipart: file.size > 5 * 1024 * 1024, // Use multipart for files > 5MB
        onUploadProgress: (event: any) => {
          console.log(`Upload progress: ${event.percentage}%`);
        }
      });

      return NextResponse.json({
        success: true,
        message: "File uploaded successfully",
        data: {
          fileUrl: blob.url,
          fileType: file.type,
          fileName: blob.pathname,
          size: file.size,
        },
      });
    } catch (error) {
      console.error("Error uploading promotion file:", error);
      return NextResponse.json(
        { success: false, message: "Failed to upload file" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function POST(request: NextRequest) {
  return _POST(request);
}


