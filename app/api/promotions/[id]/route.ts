import { NextRequest, NextResponse } from "next/server"
// RouteContext removed in Next 15; inline context types instead
import prisma from "@/lib/prisma"
import { AuthContext, AuthMiddleware } from "@/lib/auth/services"

// GET /api/promotions/[id]
const _GET = AuthMiddleware.withAuth(
async (_request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>)
    const idStr = params?.id; const id = parseInt(idStr || "0")
    if (!idStr || isNaN(id)) {
      return NextResponse.json({ success: false, message: "Invalid id" }, { status: 400 })
    }

    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const promotion = await prisma.promotion.findFirst({
      where: { id, tenantId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    })

    if (!promotion) return NextResponse.json({ success: false, message: "Promotion not found" }, { status: 404 })

    return NextResponse.json({ success: true, promotion }, { status: 200 })
  } catch (e) {
    console.error("Failed to fetch promotion:", e)
    return NextResponse.json({ success: false, message: "Failed to fetch promotion" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _GET(request, { params } as any);
}

// PATCH /api/promotions/[id]
const _PATCH = AuthMiddleware.withAuth(
async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>)
    const idStr = params?.id; const id = parseInt(idStr || "0")
    if (!idStr || isNaN(id)) {
      return NextResponse.json({ success: false, message: "Invalid id" }, { status: 400 })
    }

    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const existing = await prisma.promotion.findFirst({ where: { id, tenantId } })
    if (!existing) return NextResponse.json({ success: false, message: "Promotion not found" }, { status: 404 })

    const body = await request.json().catch(() => ({}))
    const { type, title, fileUrl, fileType, userId } = body ?? {}

    const updateData: Record<string, unknown> = {}
    if (type !== undefined) {
      if (["BANNER", "PREVIOUS_CASES"].includes(type)) updateData.type = type
      else return NextResponse.json({ success: false, message: "Invalid type" }, { status: 400 })
    }
    if (title !== undefined) updateData.title = title ?? null
    if (fileUrl !== undefined) updateData.fileUrl = fileUrl ?? null
    if (fileType !== undefined) updateData.fileType = fileType ?? null
    
    if (userId !== undefined) {
      if (userId === null || userId === "") {
        updateData.userId = null
      } else {
        const dentist = await prisma.user.findFirst({
          where: {
            id: parseInt(userId),
            tenantId,
            userType: "DENTIST",
            isActive: true,
          },
        })
        if (!dentist) {
          return NextResponse.json({ success: false, message: "Invalid dentist selected" }, { status: 400 })
        }
        updateData.userId = parseInt(userId)
      }
    }

    const updated = await prisma.promotion.update({
      where: { id },
      data: updateData,
    })

    return NextResponse.json({ success: true, promotion: updated }, { status: 200 })
  } catch (e) {
    console.error("Failed to update promotion:", e)
    return NextResponse.json({ success: false, message: "Failed to update promotion" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function PATCH(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _PATCH(request, { params } as any);
}

// DELETE /api/promotions/[id]
const _DELETE = AuthMiddleware.withAuth(
async (_request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>)
    const idStr = params?.id; const id = parseInt(idStr || "0")
    if (!idStr || isNaN(id)) {
      return NextResponse.json({ success: false, message: "Invalid id" }, { status: 400 })
    }

    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const existing = await prisma.promotion.findFirst({ where: { id, tenantId } })
    if (!existing) return NextResponse.json({ success: false, message: "Promotion not found" }, { status: 404 })

    await prisma.promotion.delete({ where: { id } })
    return NextResponse.json({ success: true }, { status: 200 })
  } catch (e) {
    console.error("Failed to delete promotion:", e)
    return NextResponse.json({ success: false, message: "Failed to delete promotion" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _DELETE(request, { params } as any);
}


