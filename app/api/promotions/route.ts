import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { AuthContext, AuthMiddleware } from "@/lib/auth/services"

// GET /api/promotions
const _GET = AuthMiddleware.withAuth(
async (request: NextRequest, context: { authContext: AuthContext }) => {
  try {
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const url = new URL(request.url)
    const type = url.searchParams.get("type") as "BANNER" | "PREVIOUS_CASES" | null

    const promotions = await prisma.promotion.findMany({
      where: {
        tenantId,
        ...(type ? { type } : {}),
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    })

    return NextResponse.json({ success: true, promotions }, { status: 200 })
  } catch (e) {
    console.error("Failed to fetch promotions:", e)
    return NextResponse.json({ success: false, message: "Failed to fetch promotions" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest) {
  return _GET(request)
}

// POST /api/promotions
const _POST = AuthMiddleware.withAuth(
async (request: NextRequest, context: { authContext: AuthContext }) => {
  try {
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const body = await request.json().catch(() => ({}))
    const { type, title, fileUrl, fileType, userId } = body ?? {}

    const errors: string[] = []
    if (!type || !["BANNER", "PREVIOUS_CASES"].includes(type)) errors.push("type must be BANNER or PREVIOUS_CASES")
    // title is optional by schema; fileUrl/fileType optional too
    // userId is optional - if provided, validate it exists and is a dentist

    if (userId !== undefined && userId !== null) {
      const dentist = await prisma.user.findFirst({
        where: {
          id: parseInt(userId),
          tenantId,
          userType: "DENTIST",
          isActive: true,
        },
      })
      if (!dentist) {
        errors.push("Invalid dentist selected")
      }
    }

    if (errors.length > 0) {
      return NextResponse.json({ success: false, message: "Validation failed", errors }, { status: 400 })
    }

    const created = await prisma.promotion.create({
      data: {
        tenantId,
        type,
        title: title ?? null,
        fileUrl: fileUrl ?? null,
        fileType: fileType ?? null,
        userId: userId ? parseInt(userId) : null,
      },
    })

    return NextResponse.json({ success: true, promotion: created }, { status: 201 })
  } catch (e) {
    console.error("Failed to create promotion:", e)
    return NextResponse.json({ success: false, message: "Failed to create promotion" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function POST(request: NextRequest) {
  return _POST(request)
}


