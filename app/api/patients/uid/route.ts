import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/auth/services";

// GET /api/patients/uid?uid=<uid_value>
const _GET = AuthMiddleware.withAuth(
  async (request: NextRequest, { authContext }) => {
    try {
      const url = new URL(request.url);
      const uid = url.searchParams.get("uid");

      // Security: Only use tenant ID from authenticated context to prevent cross-tenant data access
      const tenantId = authContext?.user?.tenantId ?? authContext?.tenant?.id;

      const errors: string[] = [];
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Missing tenant context" },
          { status: 400 }
        );
      }
      if (!uid || String(uid).trim().length === 0) {
        errors.push("uid parameter is required");
      }
      if (errors.length > 0) {
        return NextResponse.json(
          { success: false, message: "Validation failed", errors },
          { status: 400 }
        );
      }

      const trimmedUid = String(uid).trim();

      // Search for patient by UID within the tenant
      const patient = await prisma.patient.findFirst({
        where: { 
          uid: trimmedUid,
          tenantId
        },
        include: { caseSheet: true }
      });

      if (!patient) {
        return NextResponse.json(
          { success: false, message: "Patient not found", found: false },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        found: true,
        patient: {
          id: patient.id,
          firstName: patient.firstName,
          lastName: patient.lastName,
          phoneNumber: patient.phoneNumber,
          email: patient.email,
          address: patient.address,
          dateOfBirth: patient.dateOfBirth,
          uid: patient.uid,
          caseSheetId: patient.caseSheet?.id ?? null,
          caseSheetStatus: patient.caseSheet?.status ?? null,
        }
      }, { status: 200 });
    } catch (error) {
      console.error("Failed to search patient by UID:", error);
      return NextResponse.json(
        { success: false, message: "Failed to search patient by UID" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function GET(request: NextRequest) {
  return _GET(request);
}
