import { NextRequest, NextResponse } from "next/server"
// RouteContext removed in Next 15; inline context types instead
import prisma from "@/lib/prisma"
import { AuthContext, AuthMiddleware } from "@/lib/auth/services"

// GET /api/patients/[id]
const _GET = AuthMiddleware.withAuth(
async (_request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>)
    const id = Number(params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid patient id" }, { status: 400 })

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const p = await prisma.patient.findFirst({ 
      where: { id, tenantId }, 
      include: { caseSheet: true } 
    })
    if (!p) return NextResponse.json({ success: false, message: "Patient not found" }, { status: 404 })

    return NextResponse.json(
      {
        success: true,
        patient: {
          id: p.id,
          firstName: p.firstName,
          lastName: p.lastName,
          phoneNumber: p.phoneNumber,
          email: p.email,
          address: p.address,
          dateOfBirth: p.dateOfBirth,
          uid: p.uid,
          status: p.status,
          caseSheetId: p.caseSheet?.id ?? null,
        },
      },
      { status: 200 }
    )
  } catch {
    return NextResponse.json({ success: false, message: "Failed to fetch patient" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _GET(request, { params } as any);
}

// DELETE /api/patients/[id]
const _DELETE = AuthMiddleware.withAuth(
async (_request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>)
    const id = Number(params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid patient id" }, { status: 400 })

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    // Ensure the patient belongs to the tenant
    const existingPatient = await prisma.patient.findFirst({
      where: { id, tenantId }
    });
    if (!existingPatient) {
      return NextResponse.json({ success: false, message: "Patient not found" }, { status: 404 });
    }

    // Delete all related data in the correct order to respect foreign key constraints
    await prisma.$transaction(async (tx: any) => {
      // First, get the case sheet ID to find related data
      const caseSheet = await tx.caseSheet.findUnique({ 
        where: { patientId: id },
        select: { id: true }
      })

      if (caseSheet) {
        // Find all teeth for this case sheet
        const teeth = await tx.tooth.findMany({
          where: { caseSheetId: caseSheet.id },
          select: { id: true }
        })

        if (teeth.length > 0) {
          const toothIds = teeth.map((t: any) => t.id)
          
          // Find all findings for these teeth
          const findings = await tx.finding.findMany({
            where: { toothId: { in: toothIds } },
            select: { id: true }
          })

          if (findings.length > 0) {
            const findingIds = findings.map((f: any) => f.id)
            
            // Delete treatments first (deepest level)
            await tx.treatment.deleteMany({
              where: { findingId: { in: findingIds } }
            })
            
            // Delete findings
            await tx.finding.deleteMany({
              where: { id: { in: findingIds } }
            })
          }
          
          // Delete teeth
          await tx.tooth.deleteMany({
            where: { id: { in: toothIds } }
          })
        }
        
        // Delete case sheet
        await tx.caseSheet.delete({
          where: { id: caseSheet.id }
        })
      }

      // Delete appointments
      await tx.appointment.deleteMany({ where: { patientId: id } })
      
      // Delete payments (keep for audit trail as mentioned in comment, but user wants everything deleted)
      await tx.payment.deleteMany({ where: { patientId: id } })
      
      // Delete invoices (keep for audit trail as mentioned in comment, but user wants everything deleted)
      await tx.invoice.deleteMany({ where: { patientId: id } })

      // Finally, delete the patient
      await tx.patient.delete({ where: { id } })
    })

    return NextResponse.json({ success: true }, { status: 200 })
  } catch (e) {
    console.error("Failed to delete patient:", e)
    return NextResponse.json({ success: false, message: "Failed to delete patient" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _DELETE(request, { params } as any);
}

// PATCH /api/patients/[id]
const _PATCH = AuthMiddleware.withAuth(
async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>)
    const id = Number(params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid patient id" }, { status: 400 })

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const body = await request.json().catch(() => ({}))
    const {
      firstName,
      lastName,
      phoneNumber,
      email,
      address,
      dateOfBirth,
      uid,
    } = body ?? {}

    // Basic validation similar to create
    const errors: string[] = []
    if (!firstName || !String(firstName).trim()) errors.push("firstName is required")
    if (!lastName || !String(lastName).trim()) errors.push("lastName is required")
    if (!phoneNumber || !String(phoneNumber).trim()) errors.push("phoneNumber is required")
    if (email && !String(email).includes("@")) errors.push("email is invalid")
    if (errors.length > 0) {
      return NextResponse.json({ success: false, message: "Validation failed", errors }, { status: 400 })
    }

    // Ensure the patient belongs to the tenant before updating
    const existingPatient = await prisma.patient.findFirst({
      where: { id, tenantId: tenantId as number }
    });
    if (!existingPatient) {
      return NextResponse.json({ success: false, message: "Patient not found" }, { status: 404 });
    }

    const updated = await prisma.patient.update({
      where: { id },
      data: {
        firstName: String(firstName).trim(),
        lastName: String(lastName).trim(),
        phoneNumber: String(phoneNumber).trim(),
        email: email ? String(email).trim().toLowerCase() : null,
        address: address ? String(address).trim() : null,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
        uid: uid ? String(uid).trim() : null,
      },
      include: { caseSheet: true },
    })

    return NextResponse.json(
      {
        success: true,
        patient: {
          id: updated.id,
          firstName: updated.firstName,
          lastName: updated.lastName,
          phoneNumber: updated.phoneNumber,
          email: updated.email,
          address: updated.address,
          dateOfBirth: updated.dateOfBirth,
          uid: updated.uid,
          status: updated.status,
          caseSheetId: updated.caseSheet?.id ?? null,
        },
      },
      { status: 200 }
    )
  } catch {
    return NextResponse.json({ success: false, message: "Failed to update patient" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function PATCH(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _PATCH(request, { params } as any);
}


