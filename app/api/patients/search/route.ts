import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import type { Patient, CaseSheet } from "@prisma/client";
import { AuthMiddleware } from "@/lib/auth/services";

// POST /api/patients/search
const _POST = AuthMiddleware.withAuth(
  async (request: NextRequest, { authContext }) => {
    try {
      const body = await request.json();
      const { searchTerm } = body ?? {};

      // Security: Only use tenant ID from authenticated context to prevent cross-tenant data access
      const tenantId = authContext?.user?.tenantId ?? authContext?.tenant?.id;

      const errors: string[] = [];
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Missing tenant context" },
          { status: 400 }
        );
      }
      if (!searchTerm || String(searchTerm).trim().length === 0) {
        errors.push("searchTerm is required");
      }
      if (errors.length > 0) {
        return NextResponse.json(
          { success: false, message: "Validation failed", errors },
          { status: 400 }
        );
      }
      const term = String(searchTerm).trim();

      // Use the comprehensive searchPatients method that searches across all fields
      const detailed: Array<Patient & { caseSheet: CaseSheet | null }> = await prisma.patient.searchPatients({
        searchTerm: term,
        tenantId,
        limit: 50,
      });

      const patients = detailed.map((p: any) => ({
        id: p.id,
        firstName: p.firstName,
        lastName: p.lastName,
        phoneNumber: p.phoneNumber,
        email: p.email,
        caseSheetId: p.caseSheet?.id ?? null,
        caseSheetStatus: p.caseSheet?.status ?? null,
      }));

      return NextResponse.json({ success: true, patients }, { status: 200 });
    } catch {
      return NextResponse.json(
        { success: false, message: "Failed to search patients" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function POST(request: NextRequest) {
  return _POST(request);
}

const _GET = AuthMiddleware.withAuth(
  async (request: NextRequest, { authContext }) => {
    try {
      const url = new URL(request.url);
      const limitParam = url.searchParams.get("limit");
      const limitRaw = Number(limitParam);
      const limit = Number.isFinite(limitRaw) ? Math.min(Math.max(limitRaw, 1), 100) : 50;

      // Security: Only use tenant ID from authenticated context to prevent cross-tenant data access
      const tenantId = authContext?.user?.tenantId ?? authContext?.tenant?.id;

      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Missing tenant context" },
          { status: 400 }
        );
      }

      const detailed: Array<any> = await prisma.patient.findMany({
        where: { tenantId },
        include: { caseSheet: true },
        orderBy: [{ createdAt: "desc" }],
        take: limit,
      });

      const patients = detailed.map((p: any) => ({
        id: p.id,
        firstName: p.firstName,
        lastName: p.lastName,
        phoneNumber: p.phoneNumber,
        email: p.email,
        caseSheetId: p.caseSheet?.id ?? null,
        caseSheetStatus: p.caseSheet?.status ?? null,
        lastVisitDate: (p.caseSheet)?.lastVisitDate ?? null,
      }));

      return NextResponse.json({ success: true, patients }, { status: 200 });
    } catch {
      return NextResponse.json(
        { success: false, message: "Failed to list patients" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function GET(request: NextRequest) {
  return _GET(request);
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}
