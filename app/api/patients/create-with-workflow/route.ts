import { NextRequest, NextResponse } from "next/server";
import { ClinicalWorkflowService, type PatientCreationData } from "@/lib/clinical/workflow-service";
import { AuthMiddleware } from "@/lib/auth/services";

// POST /api/patients/create-with-workflow
const _POST = AuthMiddleware.withAuth(
  async (request: NextRequest, { authContext }) => {
    try {
      const body = await request.json();
      const {
        firstName,
        lastName,
        phoneNumber,
        email,
        address,
        dateOfBirth,
        uid,
      } = body ?? {};

      // Get tenantId from authenticated context (user is always part of a tenant)
      const tenantId = authContext.tenant?.id as number;

      // Basic validation
      const errors: string[] = [];
      if (!firstName || !String(firstName).trim()) errors.push("firstName is required");
      if (!lastName || !String(lastName).trim()) errors.push("lastName is required");
      if (!phoneNumber || !String(phoneNumber).trim()) errors.push("phoneNumber is required");
      if (errors.length > 0) {
        return NextResponse.json(
          { success: false, message: "Validation failed", errors },
          { status: 400 }
        );
      }

      const service = new ClinicalWorkflowService();
      const input: PatientCreationData = {
        firstName: String(firstName).trim(),
        lastName: String(lastName).trim(),
        phoneNumber: String(phoneNumber).trim(),
        tenantId,
        email: email ? String(email).trim().toLowerCase() : undefined,
        address: address ? String(address).trim() : undefined,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
        uid: uid ? String(uid).trim() : undefined,
      };

      const result = await service.createPatientWithCompleteWorkflow(input);

      return NextResponse.json(
        {
          success: true,
          message: "Patient and initial clinical records created",
          patient: result.patient,
          caseSheet: result.caseSheet,
          teethCount: result.teethCount,
        },
        { status: 201 }
      );
    } catch (error) {
      // Simplified error handling - all errors from service are user-facing
      const message = error instanceof Error ? error.message : "Failed to create patient with workflow";
      const status = message.includes("already exists") || message.includes("Invalid") ? 400 : 500;
      
      return NextResponse.json(
        { success: false, message },
        { status }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function POST(request: NextRequest) {
  return _POST(request);
}

export async function GET() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}


