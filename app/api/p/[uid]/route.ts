import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// Types are now inferred from Prisma query results to avoid type conflicts

// GET /api/p/[uid] - Tenant-bypassed public patient information
// This endpoint bypasses tenant isolation for public patient information display
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ uid: string }> }
) {
  try {
    const { uid } = await params;

    if (!uid || !uid.trim()) {
      return NextResponse.json(
        { success: false, message: "UID parameter is required" },
        { status: 400 }
      );
    }

    // Find patient by UID (bypass tenant isolation for public display)
    const patient = await prisma.patient.findFirst({
      where: { 
        uid: uid.trim(),
        status: 'ACTIVE' // Only show active patients
      },
      include: {
        tenant: true,
        caseSheet: {
          include: {
            teeth: {
              include: {
                findings: {
                  include: {
                    treatments: true,
                    recordedBy: true
                  },
                  orderBy: {
                    recordedDate: 'desc'
                  }
                }
              },
              orderBy: {
                toothNumber: 'asc'
              }
            }
          }
        }
      }
    });

    if (!patient) {
      return NextResponse.json(
        { success: false, message: "Patient not found" },
        { status: 404 }
      );
    }

    // Get dentists for this tenant with working hours
    const dentists = await prisma.user.findMany({
      where: {
        tenantId: patient.tenantId,
        userType: 'DENTIST',
        isActive: true
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phoneNumber: true,
        // Working hours
        sat: true, satstart: true, satend: true,
        sun: true, sunstart: true, sunend: true,
        mon: true, monstart: true, monend: true,
        tue: true, tuestart: true, tueend: true,
        wed: true, wedstart: true, wedend: true,
        thu: true, thustart: true, thuend: true,
        fri: true, fristart: true, friend: true
      },
      orderBy: {
        firstName: 'asc'
      }
    });

    // Process clinical data for summary
    const clinicalSummary = {
      totalFindings: 0,
      totalTreatments: 0,
      completedTreatments: 0,
      pendingTreatments: 0,
      lastVisitDate: null as Date | null,
      findingsBySeverity: { low: 0, medium: 0, high: 0, unspecified: 0 },
      treatmentsByMonth: [] as Array<{ month: string; count: number }>
    };

    if (patient.caseSheet?.teeth) {
      const allFindings = patient.caseSheet.teeth.flatMap((tooth: any) => tooth.findings);
      const allTreatments = allFindings.flatMap((finding: any) => finding.treatments);
      
      clinicalSummary.totalFindings = allFindings.length;
      clinicalSummary.totalTreatments = allTreatments.length;
      clinicalSummary.completedTreatments = allTreatments.filter((t: any) => t.status === 'COMPLETED').length;
      clinicalSummary.pendingTreatments = allTreatments.filter((t: any) => t.status === 'PENDING').length;
      
      // Get last visit date
      const dates = [...allFindings.map((f: any) => f.recordedDate), ...allTreatments.filter((t: any) => t.completedDate).map((t: any) => t.completedDate!)];
      if (dates.length > 0) {
        clinicalSummary.lastVisitDate = new Date(Math.max(...dates.map((d: any) => d.getTime())));
      }
      
      // Count findings by severity
      allFindings.forEach((finding: any) => {
        const severity = finding.severity?.toLowerCase();
        if (severity === 'low') clinicalSummary.findingsBySeverity.low++;
        else if (severity === 'medium') clinicalSummary.findingsBySeverity.medium++;
        else if (severity === 'high') clinicalSummary.findingsBySeverity.high++;
        else clinicalSummary.findingsBySeverity.unspecified++;
      });
      
      // Group treatments by month for chart data
      const treatmentsByMonth = new Map<string, number>();
      allTreatments.forEach((treatment: any) => {
        const date = treatment.completedDate || treatment.createdAt;
        const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
        treatmentsByMonth.set(monthKey, (treatmentsByMonth.get(monthKey) || 0) + 1);
      });
      
      clinicalSummary.treatmentsByMonth = Array.from(treatmentsByMonth.entries())
        .map(([month, count]) => ({ month, count }))
        .sort((a: any, b: any) => a.month.localeCompare(b.month))
        .slice(-12); // Last 12 months
    }

    // Fetch tenant promotions (publicly visible) for this tenant
    const promotions = await prisma.promotion.findMany({
      where: { tenantId: patient.tenantId },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({
      success: true,
      data: {
        patient: {
          id: patient.id,
          firstName: patient.firstName,
          lastName: patient.lastName,
          uid: patient.uid,
          dateOfBirth: patient.dateOfBirth,
          phoneNumber: patient.phoneNumber,
          email: patient.email
        },
        clinic: {
          id: patient.tenant.id,
          name: patient.tenant.name,
          logoImage: patient.tenant.logoImage,
          phoneNumber: patient.tenant.phoneNumber,
          address: patient.tenant.address
        },
        dentists,
        clinicalSummary,
        caseSheet: patient.caseSheet,
        promotions,
      }
    });

  } catch (error) {
    console.error("Error fetching patient data:", error);
    return NextResponse.json(
      { success: false, message: "Failed to fetch patient information" },
      { status: 500 }
    );
  }
}