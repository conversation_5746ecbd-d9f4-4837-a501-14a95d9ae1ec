import { NextResponse } from "next/server";
import { AuthService } from "@/lib/auth/services";
import { TenantContextManager } from "@/lib/tenant-context";

/**
 * Logout API endpoint
 * GET/POST /api/auth/logout
 * 
 * Requirements: 4.3
 * Destroys user session and clears authentication cookies
 */
async function handleLogout() {
  try {

    // Validate current session (optional - logout should work even with invalid session)
    const sessionValidation = await AuthService.validateSession();
    
    if (sessionValidation.isValid && sessionValidation.session) {
    } else {
    }

    // Destroy session and clear cookies
    const destroyResult = await AuthService.destroySession();
    
    if (!destroyResult.success) {
      
      return NextResponse.json(
        {
          success: false,
          message: "Failed to logout",
          errors: ["Unable to clear session"],
        },
        { status: 500 }
      );
    }


    // Clear tenant context
    TenantContextManager.clearGlobalContext();

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Logout successful",
        redirectUrl: "/auth/login",
      },
      { status: 200 }
    );

  } catch (error) {
    
    // Even if there's an error, we should try to clear the session
    try {
      await AuthService.destroySession();
    } catch (cleanupError) {
    }
    
    // Don't expose internal errors to client, but still return success
    // since logout should always appear to work from user perspective
    return NextResponse.json(
      {
        success: true,
        message: "Logout completed",
        redirectUrl: "/auth/login",
      },
      { status: 200 }
    );
  }
}

export async function GET() {
  return handleLogout();
}

export async function POST() {
  return handleLogout();
}
