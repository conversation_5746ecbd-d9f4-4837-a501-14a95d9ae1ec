import { NextRequest, NextResponse } from "next/server";
import { UserService, AuthService } from "@/lib/auth/services";
import { getCurrentTimestamp } from "@/lib/date-utils";
import { loginSchema } from "@/lib/auth/schemas";
import { SanitizationUtils, ValidationUtils, SecurityErrorHandler } from "@/lib/auth/utils";
import { TenantContextManager } from "@/lib/tenant-context";

/**
 * Login API endpoint
 * POST /api/auth/login
 * 
 * Requirements: 3.1, 3.2, 3.4, 3.5, 3.6
 * Authenticates users and establishes secure sessions
 */
export async function POST(request: NextRequest) {
  try {
    // Get client information for security logging and rate limiting
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Parse request body with size limit
    let body: unknown;
    try {
      body = await request.json();
    } catch {
      const errorResponse = SecurityErrorHandler.createValidationErrorResponse(
        "Login data validation failed",
        ["body: Invalid JSON"]
      );
      return NextResponse.json(errorResponse, { status: 400 });
    }
    
    // Check for basic security threats in raw input
    const rawInput = JSON.stringify(body);
    if (rawInput.length > 10000) {
      SecurityErrorHandler.logSecurityIncident(
        'OVERSIZED_LOGIN_REQUEST',
        `Login request size: ${rawInput.length} bytes`,
        userAgent,
        clientIP
      );
      const errorResponse = SecurityErrorHandler.createSecurityErrorResponse(
        'Request too large',
        413
      );
      return NextResponse.json(errorResponse, { status: 413 });
    }

    const securityCheck = ValidationUtils.validateSecurityThreats(rawInput);
    if (!securityCheck.isValid) {
      SecurityErrorHandler.logSecurityIncident(
        'LOGIN_SECURITY_THREAT',
        `Threats: ${securityCheck.threats.join(', ')}`,
        userAgent,
        clientIP
      );
      const errorResponse = SecurityErrorHandler.createSecurityErrorResponse();
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // Validate input using Zod schema
    const validationResult = loginSchema.safeParse(body);
    
    if (!validationResult.success) {
      const errors = validationResult.error.issues.map((err) => 
        `${err.path.join('.')}: ${err.message}`
      );
      
      SecurityErrorHandler.logSecurityIncident(
        'LOGIN_VALIDATION_FAILURE',
        `Validation errors: ${errors.join(', ')}`,
        userAgent,
        clientIP
      );
      
      const errorResponse = SecurityErrorHandler.createValidationErrorResponse(
        "Login data validation failed",
        errors
      );
      
      return NextResponse.json(errorResponse, { status: 400 });
    }

    const { username, password } = validationResult.data;

    // Check advanced rate limiting with brute force protection
    const advancedRateLimitCheck = AuthService.checkAdvancedRateLimit(clientIP);
    if (!advancedRateLimitCheck.allowed) {
      SecurityErrorHandler.logSecurityIncident(
        'ADVANCED_RATE_LIMIT_EXCEEDED',
        `IP blocked: ${advancedRateLimitCheck.blockedReason}, Risk Level: ${advancedRateLimitCheck.riskLevel}`,
        userAgent,
        clientIP
      );
      
      // Check for suspicious activity patterns
      const suspiciousActivity = AuthService.detectSuspiciousActivity();
      if (suspiciousActivity.coordinatedAttack) {
        SecurityErrorHandler.logSecurityIncident(
          'COORDINATED_ATTACK_DETECTED',
          `Coordinated attack detected: ${suspiciousActivity.suspiciousIPs.length} suspicious IPs, ${suspiciousActivity.totalAttempts} total attempts`,
          userAgent,
          clientIP
        );
      }
      
      const errorResponse = SecurityErrorHandler.createRateLimitErrorResponse(advancedRateLimitCheck.retryAfter);
      return NextResponse.json(errorResponse, { 
        status: 429,
        headers: {
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': getCurrentTimestamp(),
          'X-RateLimit-Limit': '5',
        }
      });
    }

    // Additional security validation for credentials
    const usernameSecurityCheck = ValidationUtils.validateSecurityThreats(username);
    if (!usernameSecurityCheck.isValid) {
      SecurityErrorHandler.logSecurityIncident(
        'MALICIOUS_USERNAME',
        `Username security threats: ${usernameSecurityCheck.threats.join(', ')}`,
        userAgent,
        clientIP
      );
      const errorResponse = SecurityErrorHandler.createSecurityErrorResponse(
        'Invalid login credentials'
      );
      return NextResponse.json(errorResponse, { status: 400 });
    }



    // Sanitize input data (additional layer of protection)
    const sanitizedUsername = SanitizationUtils.sanitizeString(username);

    // Step 1: Authenticate user
    
    const authResult = await UserService.authenticateUser(sanitizedUsername, password);
    
    if (!authResult.success || !authResult.user) {
      
      return NextResponse.json(
        {
          success: false,
          message: "Invalid credentials",
          errors: ["Username or password is incorrect"],
        },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Step 2: Create session
    
    const sessionResult = await AuthService.createSession(user);
    
    if (!sessionResult.success) {
      
      return NextResponse.json(
        {
          success: false,
          message: "Failed to create session",
          errors: ["Unable to establish secure session"],
        },
        { status: 500 }
      );
    }

    // Step 3: Set tenant context for the authenticated user
    
    const tenantContext = TenantContextManager.createContextFromAuthSession(
      {
        userId: user.id,
        tenantId: user.tenantId,
        username: user.username,
        userType: user.userType,
        isActive: user.isActive,
      },
      sessionResult.sessionToken,
      undefined // We'll get tenant name later if needed
    );
    
    // Set the tenant context globally for this request
    TenantContextManager.setGlobalContext(tenantContext);
    

    // Step 4: Determine redirect URL based on user role
    let redirectUrl = "/dashboard";
    
    switch (user.userType) {
      case "ADMIN":
        redirectUrl = "/dashboard";
        break;
      case "DENTIST":
        redirectUrl = "/dashboard";
        break;
      case "RECEPTIONIST":
        redirectUrl = "/dashboard";
        break;
      case "PATIENT":
        redirectUrl = "/dashboard"; // Or a patient-specific area if needed
        break;
      default:
        redirectUrl = "/dashboard";
    }

    // Step 5: Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Login successful",
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          userType: user.userType,
          tenantId: user.tenantId,
        },
        redirectUrl,
      },
      { status: 200 }
    );

  } catch (error) {
    // Don't expose internal errors to client
    return NextResponse.json(
      {
        success: false,
        message: "An unexpected error occurred during login",
        errors: ["Please try again later"],
      },
      { status: 500 }
    );
  }
}

/**
 * Handle unsupported HTTP methods
 */
export async function GET() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}
