import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/auth/services";

/**
 * Authentication Validation API endpoint
 * GET /api/auth/validate
 * 
 * Requirements: 3.4, 8.1
 * Validates current user session and returns user information
 */
const _GET = AuthMiddleware.withAuth(
  async (_request: NextRequest, { authContext }) => {
    try {
      // Get tenantId from authenticated context
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      
      // Return user information (excluding sensitive data)
      const userInfo = {
        id: authContext.user?.id,
        username: authContext.user?.username,
        userType: authContext.user?.userType,
        tenantId: tenantId,
        isActive: authContext.user?.isActive,
        // Note: We don't have email, firstName, lastName in session
        // These would need to be fetched from database if needed
        email: authContext.user?.email || "", // Placeholder
        firstName: authContext.user?.firstName || "", // Placeholder
        lastName: authContext.user?.lastName || "", // Placeholder
      };

      return NextResponse.json(
        {
          success: true,
          message: "Session is valid",
          user: userInfo,
          session: authContext.session ? {
            createdAt: authContext.session.createdAt,
            expiresAt: authContext.session.expiresAt,
          } : null,
        },
        { status: 200 }
      );

    } catch (error) {
      console.error("[AUTH-VALIDATE] Error validating session:", error);
      
      return NextResponse.json(
        {
          success: false,
          message: "Authentication validation failed",
          user: null,
        },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function GET(request: NextRequest) {
  return _GET(request);
}

/**
 * Handle unsupported HTTP methods
 */
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}