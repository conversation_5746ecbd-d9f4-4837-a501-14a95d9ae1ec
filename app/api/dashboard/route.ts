import { NextRequest, NextResponse } from "next/server";
import { getCurrentTimestamp } from "@/lib/date-utils";
import { AuthMiddleware, RoleGuard, AuthContext } from "@/lib/auth/services";

import prisma from "@/lib/prisma";

/**
 * Dashboard API endpoint - Protected route example
 * GET /api/dashboard
 * 
 * This endpoint demonstrates:
 * - Authentication middleware usage
 * - Tenant context integration
 * - Role-based access control
 */

// Using the middleware wrapper for automatic auth handling
const _GET = AuthMiddleware.withAuth(
  async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {

    try {
      const { authContext } = context
      
      // Simple dashboard data structure
      type DashboardStats = {
        todayPatients: number
        totalPatients: number
        pendingTreatments: number
        outstandingInvoices: number
        completedTreatments: number
        generatedAt: string
      }
      type Activity = {
        id: number
        type: string
        description: string
        timestamp: string
        user: string
        relatedEntity?: { type: "patient" | "invoice" | "treatment"; id: number | string; name: string }
      }
      type DashboardData = {
        user: {
          id: number | string | null | undefined
          username: string | null | undefined
          userType: string | undefined
          tenantId: number | null | undefined
        }
        tenant: {
          id: number
          name: string
        } | undefined
        stats?: DashboardStats
        recentActivity?: Activity[]
      }

      const dashboardData: DashboardData = {
        user: {
          id: authContext.user?.id,
          username: authContext.user?.username,
          userType: authContext.user?.userType,
          tenantId: authContext.user?.tenantId,
        },
        tenant: authContext.tenant,
      };

      // Get tenantId from authenticated context
      const tenantId = authContext.user?.tenantId;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Missing tenant context" },
          { status: 400 }
        )
      }

      // Establish today's start/end (UTC)
      const now = new Date()
      const startOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0))
      const endOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999))

      // Get all required stats in parallel
      const [todaysPatientIds, totalPatients, pendingTreatments, outstandingInvoices, completedTreatments] = await Promise.all([
        // 1) Today's patients: distinct patients with an appointment today (excluding cancelled)
        prisma.appointment.findMany({
          where: {
            tenantId,
            appointmentDate: {
              gte: startOfDay,
              lte: endOfDay,
            },
            status: { not: "CANCELLED" },
          },
          distinct: ["patientId"],
          select: { patientId: true },
        }),
        
        // 2) Total number of patients
        prisma.patient.count({
          where: { tenantId },
        }),
        
        // 3) Pending treatments
        prisma.treatment.count({
          where: {
            tenantId,
            status: "PENDING",
          },
        }),
        
        // 4) Outstanding invoices (balanceDue > 0)
        prisma.invoice.count({
          where: {
            tenantId,
            balanceDue: { gt: 0 },
          },
        }),
        
        // 5) Completed treatments
        prisma.treatment.count({
          where: {
            tenantId,
            status: "COMPLETED",
          },
        }),
      ])

      const todayPatients = todaysPatientIds.length

      dashboardData.stats = {
        todayPatients,
        totalPatients,
        pendingTreatments,
        outstandingInvoices,
        completedTreatments,
        generatedAt: getCurrentTimestamp(),
      };

      // Simple recent activity - latest patients only
      const latestPatients = await prisma.patient.findMany({
        where: { tenantId },
        orderBy: { createdAt: "desc" },
        take: 10,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          createdAt: true,
        },
      })

      // dashboardData.recentActivity = latestPatients.map((patient: { id: number; firstName: string; lastName: string; createdAt: Date }) => ({
      //   id: `patient-${patient.id}`,
      //   type: "patient_created",
      //   description: `New patient: ${patient.firstName} ${patient.lastName}`,
      //   timestamp: getCurrentTimestamp(),
      //   user: "System",
      //   relatedEntity: {
      //     type: "patient" as const,
      //     id: patient.id,
      //     name: `${patient.firstName} ${patient.lastName}`,
      //   },
      // }))

      return NextResponse.json(
        {
          success: true,
          message: "Dashboard data retrieved successfully",
          data: dashboardData,
        },
        { status: 200 }
      );

    } catch (error) {
      console.error("[DASHBOARD-API] Error retrieving dashboard data:", error);
      
      return NextResponse.json(
        {
          success: false,
          message: "Failed to retrieve dashboard data",
          errors: ["An error occurred while loading dashboard"],
        },
        { status: 500 }
      );
    }
  },
  // Middleware options - require authentication for all user types
  RoleGuard.authenticatedOnly()
);

export async function GET(request: NextRequest) {
  return _GET(request);
}
