import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/auth/services"

// POST /api/billing/invoices
// Body: { patientId: number, treatmentIds?: number[] }
const _POST = AuthMiddleware.withAuth(
async (request: NextRequest) => {
  try {
    const body = await request.json()
    const patientId = Number(body?.patientId)
    const ids = Array.isArray(body?.treatmentIds) ? body.treatmentIds.map((x: unknown) => Number(x)).filter((n: number) => Number.isInteger(n) && n > 0) : []
    if (!Number.isInteger(patientId) || patientId <= 0) {
      return NextResponse.json({ success: false, message: "patientId must be a positive integer" }, { status: 400 })
    }

    const patient = await prisma.patient.findUnique({ where: { id: patientId } })
    if (!patient) return NextResponse.json({ success: false, message: "Patient not found" }, { status: 404 })

    // Fetch candidate treatments that belong to the patient's case sheet and are not linked to any invoice yet
    let treatments: { id: number; cost: number }[] = []
    if (ids.length > 0) {
      const rawTreatments = await prisma.treatment.findMany({
        where: {
          id: { in: ids },
          invoiceId: null,
          finding: { tooth: { caseSheet: { patientId } } },
        },
        select: { id: true, cost: true },
      })
      treatments = rawTreatments.map((t: any) => ({ id: t.id, cost: Number(t.cost) }))
    } else {
      const rawTreatments = await prisma.treatment.findMany({
        where: {
          invoiceId: null,
          finding: { tooth: { caseSheet: { patientId } } },
        },
        select: { id: true, cost: true },
      })
      treatments = rawTreatments.map((t: any) => ({ id: t.id, cost: Number(t.cost) }))
    }

    const totalAmount = treatments.reduce((sum: number, t: any) => sum + t.cost, 0)

    // If there are no eligible treatments to invoice:
    // 1) If specific treatmentIds were provided and they are already invoiced under a single invoice, return that invoice
    // 2) Otherwise, try to return an existing open invoice (DRAFT/SENT) for this patient
    // 3) If none found, do NOT create a new empty invoice; return 400 to avoid empty invoices
    if (treatments.length === 0) {
      // Case 1: All provided treatments already belong to a single invoice? Return it.
      if (ids.length > 0) {
        const selectedTreatments = await prisma.treatment.findMany({
          where: { id: { in: ids } },
          select: { invoiceId: true },
        })
        const invoiceIds = Array.from(
          new Set(
            selectedTreatments
              .map((t: { invoiceId: number | null }) => t.invoiceId)
              .filter((v: number | null): v is number => typeof v === 'number')
          )
        )
        if (invoiceIds.length === 1) {
          const existing = await prisma.invoice.findUnique({ where: { id: invoiceIds[0] } })
          if (existing) {
            return NextResponse.json({ success: true, invoice: existing }, { status: 200 })
          }
        }
      }

      // Case 2: Return an existing open invoice for the patient if any
      const openExisting = await prisma.invoice.findFirst({
        where: {
          patientId,
          OR: [{ status: "DRAFT" }, { status: "SENT" }, { status: "PARTIALLY_PAID" }],
        },
        orderBy: { invoiceDate: 'desc' },
      })
      if (openExisting) {
        return NextResponse.json({ success: true, invoice: openExisting }, { status: 200 })
      }

      // Case 3: Nothing to invoice and no open invoice to return
      return NextResponse.json({ success: false, message: "No eligible treatments to invoice" }, { status: 400 })
    }

    // Create invoice with a temporary unique number; then set to INV-{id}
    const serialCount = await prisma.invoiceSerial.count({ where: { tenantId: patient.tenantId } });
    const serial = serialCount + 1;
    await prisma.invoiceSerial.create({ data: { tenantId: patient.tenantId } });
    let invoice = await prisma.invoice.create({
      data: {
        tenantId: patient.tenantId,
        patientId,
        serial: serial.toString(),
        invoiceDate: new Date(),
        status: "DRAFT",
        totalAmount: totalAmount,
        amountPaid: 0,
        balanceDue: totalAmount,
      },
    })

    // Update to deterministic invoice number INV-{invoiceId}
    invoice = await prisma.invoice.update({
      where: { id: invoice.id },
      data: { serial: `INV-${invoice.id}` },
    })

    if (treatments.length > 0) {
      await prisma.treatment.updateMany({
        where: { id: { in: treatments.map((t: any) => t.id) } },
        data: { invoiceId: invoice.id },
      })
    }

    return NextResponse.json({ success: true, invoice }, { status: 201 })
  } catch {
    return NextResponse.json({ success: false, message: "Failed to create invoice" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function POST(request: NextRequest) {
  return _POST(request)
}

// GET /api/billing/invoices
// Returns a list of invoices with basic patient info
const _GET = AuthMiddleware.withAuth(
async (_request: NextRequest, { authContext }) => {
  try {
    // Get tenantId from authenticated context
    const tenantId = authContext?.user?.tenantId ?? authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const invoices = await prisma.invoice.findMany({
      where: { tenantId },
      include: { patient: true },
      orderBy: { invoiceDate: "desc" },
    })
    return NextResponse.json(invoices, { status: 200 })
  } catch {
    return NextResponse.json({ success: false, message: "Failed to load invoices" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest) {
  return _GET(request)
}

export async function PUT() {
  return NextResponse.json({ success: false, message: "Method not allowed" }, { status: 405 })
}

export async function DELETE() {
  return NextResponse.json({ success: false, message: "Method not allowed" }, { status: 405 })
}


