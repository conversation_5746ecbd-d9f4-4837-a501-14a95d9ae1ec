import { NextRequest, NextResponse } from "next/server"
// RouteContext was removed from next/server types in Next 15; inline context types instead
import prisma from "@/lib/prisma"
import { AuthMiddleware, AuthContext } from "@/lib/auth/services"

// GET /api/billing/invoices/[id]
const _GET = AuthMiddleware.withAuth(
async (_request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const id = Number(context.params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid invoice id" }, { status: 400 })

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext.user?.tenantId ?? context.authContext.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const invoice = await prisma.invoice.findFirst({
      where: { id, tenantId },
      include: { patient: true, payments: true, treatments: true }
    })
    if (!invoice) return NextResponse.json({ success: false, message: "Invoice not found" }, { status: 404 })
    return NextResponse.json(invoice, { status: 200 })
  } catch {
    return NextResponse.json({ success: false, message: "Failed to fetch invoice" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _GET(request, { params } as any);
}

// PATCH /api/billing/invoices/[id]
const _PATCH = AuthMiddleware.withAuth(
async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const id = Number(context.params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid invoice id" }, { status: 400 })
    const body = await request.json()

    const updates: Record<string, unknown> = {}
    if (typeof body?.status === "string") updates.status = body.status.toUpperCase()
    if (Array.isArray(body?.treatmentIds)) {
      const ids = body.treatmentIds.map((x: unknown) => Number(x)).filter((n: number) => Number.isInteger(n) && n > 0)
      await prisma.treatment.updateMany({ where: { id: { in: ids } }, data: { invoiceId: id } })
      // Recalc totals
      const treatments = await prisma.treatment.findMany({ where: { invoiceId: id } })
      const total = treatments.reduce((sum: number, t: any) => sum + Number((t as any).cost ?? 0), 0)
      const inv = await prisma.invoice.findUnique({ where: { id } })
      if (inv) {
        updates.totalAmount = total
        updates.balanceDue = (total - Number(inv.amountPaid))
      }
    }

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext.user?.tenantId ?? context.authContext.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    // Ensure the invoice belongs to the tenant
    const existingInvoice = await prisma.invoice.findFirst({
      where: { id, tenantId }
    });
    if (!existingInvoice) {
      return NextResponse.json({ success: false, message: "Invoice not found" }, { status: 404 });
    }

    const updated = await prisma.invoice.update({ where: { id }, data: updates })
    return NextResponse.json({ success: true, invoice: updated }, { status: 200 })
  } catch {
    return NextResponse.json({ success: false, message: "Failed to update invoice" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function PATCH(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _PATCH(request, { params } as any);
}

// DELETE /api/billing/invoices/[id]
const _DELETE = AuthMiddleware.withAuth(
async (_request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const id = Number(context.params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid invoice id" }, { status: 400 })

    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext.user?.tenantId ?? context.authContext.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    // Ensure the invoice belongs to the tenant
    const existingInvoice = await prisma.invoice.findFirst({
      where: { id, tenantId }
    });
    if (!existingInvoice) {
      return NextResponse.json({ success: false, message: "Invoice not found" }, { status: 404 });
    }

    // Unlink treatments then delete invoice
    await prisma.$transaction([
      prisma.treatment.updateMany({ where: { invoiceId: id }, data: { invoiceId: null } }),
      prisma.invoice.delete({ where: { id } }),
    ])
    return NextResponse.json({ success: true }, { status: 200 })
  } catch {
    return NextResponse.json({ success: false, message: "Failed to delete invoice" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _DELETE(request, { params } as any);
}


