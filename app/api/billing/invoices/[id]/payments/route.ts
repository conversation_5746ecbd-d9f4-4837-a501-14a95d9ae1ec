import { NextRequest, NextResponse } from "next/server";
// RouteContext removed in Next 15; inline context types instead
import { ClinicalWorkflowService, WorkflowError } from "@/lib/clinical/workflow-service";
import { AuthContext, AuthMiddleware } from "@/lib/auth/services";
import prisma from "@/lib/prisma";

// POST /api/billing/invoices/[id]/payments
const _POST = AuthMiddleware.withAuth(
async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>);
    const { id } = params;
    const body = await request.json();

    const errors: string[] = [];

    // Validate required fields per task
    const amountProvided = body?.amount !== undefined && body?.amount !== null && String(body.amount).trim() !== "";
    if (!amountProvided) {
      errors.push("amount is required");
    }

    const rawMethod = typeof body?.paymentMethod === "string" ? body.paymentMethod.trim().toUpperCase() : "";
    const validMethods = new Set(["CASH", "CARD", "CHECK", "BANK_TRANSFER", "OTHER"]);
    if (!rawMethod || !validMethods.has(rawMethod)) {
      errors.push("paymentMethod is required and must be one of CASH, CARD, CHECK, BANK_TRANSFER, OTHER");
    }

    if (errors.length > 0) {
      return NextResponse.json(
        { success: false, message: "Validation failed", errors },
        { status: 400 }
      );
    }

    // Get tenantId from authenticated context
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    // Optional invoice id from path; service will resolve if not provided
    const invoiceId = Number(id);
    const parsedInvoiceId = Number.isInteger(invoiceId) && invoiceId > 0 ? invoiceId : undefined;

    // patientId is required by the service; if missing, the service will respond with a domain error
    const patientId = Number(body?.patientId);

    const serial = await prisma.paymentSerial.count({ where: { tenantId } });
    const serialCount = serial + 1;
    await prisma.paymentSerial.create({ data: { tenantId } });

    const service = new ClinicalWorkflowService();
    const { payment, invoice } = await service.processPaymentWithUpdate({
      patientId,
      invoiceId: parsedInvoiceId,
      serial: serialCount.toString(),
      amount: body.amount,
      paymentMethod: rawMethod as "CASH" | "CARD" | "CHECK" | "BANK_TRANSFER" | "OTHER",
    });

    // Mark all treatments in this invoice as completed upon any recorded payment
    if (invoice?.id) {
      try {
        await prisma.treatment.updateMany({
          where: { invoiceId: invoice.id },
          data: { status: 'COMPLETED' },
        });
      } catch {}
    }

    return NextResponse.json(
      {
        success: true,
        message: "Payment recorded",
        payment,
        invoice: invoice ?? null,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof WorkflowError) {
      return NextResponse.json(
        { success: false, message: (error as WorkflowError).userMessage, step: (error as WorkflowError).step },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { success: false, message: "Failed to process payment" },
      { status: 500 }
    );
  }
});

export async function POST(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _POST(request, { params } as any);
}

export async function GET() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

// PUT /api/billing/invoices/[id]/payments
const _PUT = AuthMiddleware.withAuth(
async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    const params = context.params || ({} as Record<string, string>);
    const invoiceId = Number(params.id);
    if (!Number.isInteger(invoiceId) || invoiceId <= 0) {
      return NextResponse.json({ success: false, message: "Invalid invoice id" }, { status: 400 });
    }
    const body = await request.json();
    const paymentId = Number(body?.id);
    const amount = body?.amount;
    const methodRaw = typeof body?.paymentMethod === 'string' ? body.paymentMethod.trim().toUpperCase() : '';
    if (!Number.isInteger(paymentId) || paymentId <= 0) {
      return NextResponse.json({ success: false, message: "Invalid payment id" }, { status: 400 });
    }
    const validMethods = new Set(["CASH", "CARD", "CHECK", "BANK_TRANSFER", "OTHER"]);
    if (!amount || !validMethods.has(methodRaw)) {
      return NextResponse.json({ success: false, message: "Missing or invalid fields" }, { status: 400 });
    }

    const existing = await prisma.payment.findUnique({ where: { id: paymentId } });
    if (!existing || existing.invoiceId !== invoiceId) {
      return NextResponse.json({ success: false, message: "Payment not found for invoice" }, { status: 404 });
    }

    const updated = await prisma.$transaction(async (tx: any) => {
      await tx.payment.update({ where: { id: paymentId }, data: { amount: Number(amount), paymentMethod: methodRaw as "CASH" | "CARD" | "CHECK" | "BANK_TRANSFER" | "OTHER" } });
      const inv = await tx.invoice.findUnique({ where: { id: invoiceId } });
      if (!inv) return null;
      // Mark treatments completed for this invoice on payment update
      await tx.treatment.updateMany({ where: { invoiceId }, data: { status: 'COMPLETED' } });
      const payments = await tx.payment.findMany({ where: { invoiceId, status: 'COMPLETED' } });
      const totalPaid = payments.reduce((s: number, p: any) => s + Number(p.amount), 0);
      const newBalance = Number(inv.totalAmount) - totalPaid;
      let status: "PAID" | "PARTIALLY_PAID" | "SENT";
      if (newBalance <= 0) {
        status = 'PAID';
      } else if (totalPaid > 0) {
        status = 'PARTIALLY_PAID';
      } else {
        // No payments remaining and balance due
        status = 'SENT';
      }
      const updatedInvoice = await tx.invoice.update({ where: { id: invoiceId }, data: { amountPaid: totalPaid, balanceDue: Math.max(0, newBalance), status } });
      const payment = await tx.payment.findUnique({ where: { id: paymentId } });
      return { payment, invoice: updatedInvoice };
    });
    return NextResponse.json({ success: true, ...updated }, { status: 200 });
  } catch {
    return NextResponse.json({ success: false, message: "Failed to update payment" }, { status: 500 });
  }
});

export async function PUT(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _PUT(request, { params } as any);
}

// DELETE /api/billing/invoices/[id]/payments?id=PAYMENT_ID
const _DELETE = AuthMiddleware.withAuth(
async (request: NextRequest, context: { params?: Record<string, string>; authContext: unknown }) => {
  try {
    const params = context.params || ({} as Record<string, string>);
    const invoiceId = Number(params.id);
    if (!Number.isInteger(invoiceId) || invoiceId <= 0) {
      return NextResponse.json({ success: false, message: "Invalid invoice id" }, { status: 400 });
    }
    const { searchParams } = new URL(request.url);
    const pid = Number(searchParams.get('id'));
    if (!Number.isInteger(pid) || pid <= 0) {
      return NextResponse.json({ success: false, message: "Invalid payment id" }, { status: 400 });
    }
    const existing = await prisma.payment.findUnique({ where: { id: pid } });
    if (!existing || existing.invoiceId !== invoiceId) {
      return NextResponse.json({ success: false, message: "Payment not found for invoice" }, { status: 404 });
    }
    const result = await prisma.$transaction(async (tx: any) => {
      await tx.payment.delete({ where: { id: pid } });
      const inv = await tx.invoice.findUnique({ where: { id: invoiceId } });
      if (!inv) return null;
      // Still keep treatments completed once any payment was made previously; no reversal here
      const payments = await tx.payment.findMany({ where: { invoiceId, status: 'COMPLETED' } });
      const totalPaid = payments.reduce((s: number, p: any) => s + Number(p.amount), 0);
      const newBalance = Number(inv.totalAmount) - totalPaid;
      let status: "PAID" | "PARTIALLY_PAID" | "SENT";
      if (newBalance <= 0) {
        status = 'PAID';
      } else if (totalPaid > 0) {
        status = 'PARTIALLY_PAID';
      } else {
        // No payments remaining and balance due
        status = 'SENT';
      }
      const updatedInvoice = await tx.invoice.update({ where: { id: invoiceId }, data: { amountPaid: totalPaid, balanceDue: Math.max(0, newBalance), status } });
      return { invoice: updatedInvoice };
    });
    return NextResponse.json({ success: true, ...result }, { status: 200 });
  } catch {
    return NextResponse.json({ success: false, message: "Failed to delete payment" }, { status: 500 });
  }
});

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _DELETE(request, { params } as any);
}
