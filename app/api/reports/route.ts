import { NextRequest, NextResponse } from "next/server"
import { ReportService, ReportType } from "@/lib/reports"
import { AuthContext, AuthMiddleware } from "@/lib/auth/services"

// POST /api/reports
const _POST = AuthMiddleware.withAuth(
async (request: NextRequest, context: { authContext: AuthContext }) => {
  try {
    // Get tenantId from authenticated context
    const tenantId = context.authContext?.user?.tenantId ?? context.authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const body = await request.json().catch(() => ({}))
    const { reportType, dateFrom, dateTo, filters } = body

    if (!reportType || !dateFrom || !dateTo) {
      return NextResponse.json(
        { success: false, message: "Missing required fields: reportType, dateFrom, dateTo" },
        { status: 400 }
      );
    }

    // Validate report type
    const validReportTypes: ReportType[] = [
      "revenue-summary",
      "accounts-receivable",
      "appointment-utilization",
      "patient-demographics",
      "treatment-completion"
    ];

    if (!validReportTypes.includes(reportType)) {
      return NextResponse.json(
        { success: false, message: "Invalid report type" },
        { status: 400 }
      );
    }

    // Parse dates
    const startDate = new Date(dateFrom);
    const endDate = new Date(dateTo);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { success: false, message: "Invalid date format" },
        { status: 400 }
      );
    }

    if (startDate > endDate) {
      return NextResponse.json(
        { success: false, message: "Start date must be before end date" },
        { status: 400 }
      );
    }

    // Set end date to end of day
    endDate.setHours(23, 59, 59, 999);

    const query = {
      tenantId,
      dateRange: { start: startDate, end: endDate },
      filters: filters || {}
    };

    let result;
    switch (reportType) {
      case "revenue-summary":
        result = await ReportService.generateRevenueSummary(query);
        break;
      case "accounts-receivable":
        result = await ReportService.generateAccountsReceivable(query);
        break;
      case "appointment-utilization":
        result = await ReportService.generateAppointmentUtilization(query);
        break;
      case "patient-demographics":
        result = await ReportService.generatePatientDemographics(query);
        break;
      case "treatment-completion":
        result = await ReportService.generateTreatmentCompletion(query);
        break;
      default:
        return NextResponse.json(
          { success: false, message: "Unsupported report type" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      report: result
    }, { status: 200 });

  } catch (error) {
    console.error("Report generation error:", error);
    return NextResponse.json(
      { success: false, message: "Failed to generate report" },
      { status: 500 }
    );
  }
},
{ requireAuth: true, requireTenant: true }
);

export async function POST(request: NextRequest) {
  return _POST(request);
}
