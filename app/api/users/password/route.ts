import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/auth/services";
import { UserService } from "@/lib/auth/services/user-service";

// PUT /api/users/password - Change user password
const _PUT = AuthMiddleware.withAuth(
  async (request: NextRequest, context: { authContext: { user?: { id?: string; tenantId?: number } } }) => {
    try {
      const userId = context.authContext?.user?.id;
      const tenantId = context.authContext?.user?.tenantId;

      if (!userId || !tenantId) {
        return NextResponse.json(
          { success: false, message: "Missing authentication context" },
          { status: 401 }
        );
      }

      const body = await request.json();
      const { currentPassword, newPassword } = body;

      // Validate required fields
      if (!currentPassword || !newPassword) {
        return NextResponse.json(
          {
            success: false,
            message: "Current password and new password are required",
          },
          { status: 400 }
        );
      }

      // Use the existing UserService method to update password
      const result = await UserService.updatePassword(
        userId,
        currentPassword,
        newPassword,
        tenantId
      );

      if (result.success) {
        return NextResponse.json(
          {
            success: true,
            message: "Password updated successfully",
          },
          { status: 200 }
        );
      } else {
        return NextResponse.json(
          {
            success: false,
            message: result.message,
            errors: result.errors,
          },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error("Failed to update password:", error);
      return NextResponse.json(
        { success: false, message: "Failed to update password" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function PUT(request: NextRequest) {
  return _PUT(request);
}
