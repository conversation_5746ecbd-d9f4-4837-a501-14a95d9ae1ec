import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { UserService } from "@/lib/auth/services/user-service";
import { AuthMiddleware, RoleGuard } from "@/lib/auth/services";
import { UserType } from "@prisma/client";
import { passwordSchema } from "@/lib/auth/schemas";
import { AuthUser } from "@/lib/auth/types";

/**
 * GET - Get all receptionists for the current tenant
 */
const _GET = AuthMiddleware.withAuth(
  async (req: NextRequest, context: { authContext: { user?: { tenantId?: number } ; tenant?: { id: number } } }) => {
    try {
      const tenantId = context.authContext.user?.tenantId ?? context.authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      const { searchParams } = new URL(req.url);
      const search = searchParams.get("search") ?? undefined;

      const receptionists = await UserService.getUsersByType(tenantId, UserType.RECEPTIONIST, {
        search,
        includeInactive: false,
      });

      return NextResponse.json({
        success: true,
        data: receptionists.map((user: AuthUser) => ({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          username: user.username,
          isActive: user.isActive,
          createdAt: user.createdAt,
        })),
      });
    } catch (error) {
      console.error("Error fetching receptionists:", error);
      return NextResponse.json(
        { success: false, message: "Failed to fetch receptionists" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function GET(request: NextRequest) {
  return _GET(request);
}

/**
 * POST - Create a new receptionist
 */
const _POST = AuthMiddleware.withAuth(
  async (req: NextRequest, context: { authContext: { user?: { id?: string; tenantId?: number } ; tenant?: { id: number } } }) => {
    try {
      const tenantId = context.authContext.user?.tenantId ?? context.authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      const body = await req.json();
      const { firstName, lastName, email, phoneNumber, username, password } = body;

      // Basic validation
      if (!firstName || !lastName) {
        return NextResponse.json(
          { success: false, message: "First name and last name are required" },
          { status: 400 }
        );
      }

      if (!username || !password) {
        return NextResponse.json(
          { success: false, message: "Username and password are required" },
          { status: 400 }
        );
      }

      // Validate password using shared registration rules and optional confirm
      const passwordParse = z
        .object({
          password: passwordSchema,
          confirmPassword: z.string().optional(),
        })
        .refine(
          (data) => !data.confirmPassword || data.password === data.confirmPassword,
          { path: ["confirmPassword"], message: "Passwords don't match" }
        )
        .safeParse({ password, confirmPassword: body.confirmPassword });

      if (!passwordParse.success) {
        const firstError = passwordParse.error.issues[0]?.message ?? "Invalid password";
        return NextResponse.json(
          { success: false, message: firstError },
          { status: 400 }
        );
      }

      const result = await UserService.createUser({
        tenantId,
        firstName: String(firstName).trim(),
        lastName: String(lastName).trim(),
        email: email?.trim() || null,
        phoneNumber: phoneNumber?.trim() || null,
        username: String(username).trim(),
        password,
        userType: UserType.RECEPTIONIST,
        isActive: true,
        createdById: context.authContext.user?.id,
      });

      if (!result.success) {
        return NextResponse.json(result, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        message: "Receptionist created successfully",
        data: {
          id: result.userId,
        },
      });
    } catch (error) {
      console.error("Error creating receptionist:", error);
      return NextResponse.json(
        { success: false, message: "Failed to create receptionist" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function POST(request: NextRequest) {
  return _POST(request);
}
