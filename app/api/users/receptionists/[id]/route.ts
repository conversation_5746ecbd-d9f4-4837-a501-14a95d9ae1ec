import { NextRequest, NextResponse } from "next/server";
// RouteContext removed in Next 15; inline context types instead
import { z } from "zod";
import { UserService } from "@/lib/auth/services/user-service";
import { AuthMiddleware, RoleGuard, AuthContext } from "@/lib/auth/services";
import { UserType } from "@prisma/client";
import { passwordSchema } from "@/lib/auth/schemas";

/**
 * PUT - Update a receptionist
 */
const _PUT = AuthMiddleware.withAuth(
  async (req: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
    try {
      const params = context.params || ({} as Record<string, string>)
      const { authContext } = context
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      const userId = parseInt(params.id || "0");
      if (isNaN(userId)) {
        return NextResponse.json(
          { success: false, message: "Invalid user ID" },
          { status: 400 }
        );
      }

      const body = await req.json();
      const { firstName, lastName, email, phoneNumber, username, password } = body;

      // Basic validation
      if (!firstName || !lastName) {
        return NextResponse.json(
          { success: false, message: "First name and last name are required" },
          { status: 400 }
        );
      }

      if (!username) {
        return NextResponse.json(
          { success: false, message: "Username is required" },
          { status: 400 }
        );
      }

      if (password) {
        const passwordParse = z
          .object({
            password: passwordSchema,
            confirmPassword: z.string().optional(),
          })
          .refine(
            (data) => !data.confirmPassword || data.password === data.confirmPassword,
            { path: ["confirmPassword"], message: "Passwords don't match" }
          )
          .safeParse({ password, confirmPassword: body.confirmPassword });

        if (!passwordParse.success) {
          const firstError = passwordParse.error.issues[0]?.message ?? "Invalid password";
          return NextResponse.json(
            { success: false, message: firstError },
            { status: 400 }
          );
        }
      }

      // Verify the user exists and is a receptionist in this tenant
      const existingUser = await UserService.getUserById(userId);
      if (!existingUser || existingUser.tenantId !== tenantId || existingUser.userType !== UserType.RECEPTIONIST) {
        return NextResponse.json(
          { success: false, message: "Receptionist not found" },
          { status: 404 }
        );
      }

      const updateData: Record<string, string | null | undefined> = {
        firstName: String(firstName).trim(),
        lastName: String(lastName).trim(),
        email: email?.trim() || null,
        phoneNumber: phoneNumber?.trim() || null,
        username: String(username).trim(),
        updatedById: authContext.user?.id,
      };

      // Only update password if provided
      if (password) {
        updateData.password = password;
      }

      const result = await UserService.updateUser(userId, updateData);

      if (!result.success) {
        return NextResponse.json(result, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        message: "Receptionist updated successfully",
      });
    } catch (error) {
      console.error("Error updating receptionist:", error);
      return NextResponse.json(
        { success: false, message: "Failed to update receptionist" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function PUT(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _PUT(request, { params } as any);
}

/**
 * DELETE - Delete a receptionist (soft delete)
 */
const _DELETE = AuthMiddleware.withAuth(
  async (req: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
    try {
      const params = context.params || ({} as Record<string, string>)
      const { authContext } = context
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      const userId = parseInt(params.id || "0");
      if (isNaN(userId)) {
        return NextResponse.json(
          { success: false, message: "Invalid user ID" },
          { status: 400 }
        );
      }

      // Verify the user exists and is a receptionist in this tenant
      const existingUser = await UserService.getUserById(userId);
      if (!existingUser || existingUser.tenantId !== tenantId || existingUser.userType !== UserType.RECEPTIONIST) {
        return NextResponse.json(
          { success: false, message: "Receptionist not found" },
          { status: 404 }
        );
      }

      // Prevent deleting self
      if (authContext.user?.id && String(userId) === String(authContext.user.id)) {
        return NextResponse.json(
          { success: false, message: "Cannot delete your own account" },
          { status: 400 }
        );
      }

      // Soft delete - set isActive to false
      const result = await UserService.updateUser(userId, {
        isActive: false,
        updatedById: authContext.user?.id,
      });

      if (!result.success) {
        return NextResponse.json(result, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        message: "Receptionist deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting receptionist:", error);
      return NextResponse.json(
        { success: false, message: "Failed to delete receptionist" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _DELETE(request, { params } as any);
}
