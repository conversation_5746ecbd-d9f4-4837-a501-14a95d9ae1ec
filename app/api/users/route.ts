import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { AuthMiddleware, AuthContext } from "@/lib/auth/services"
import { Prisma, UserType } from "@prisma/client"

// GET /api/users - Fetch users with optional filtering
const _GET = AuthMiddleware.withAuth(
async (request: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
  try {
    // Get tenantId from authenticated context for tenant isolation
    const tenantId = context.authContext.user?.tenantId ?? context.authContext.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url)
    const userType = searchParams.get('userType')
    const limit = parseInt(searchParams.get('limit') || '100')
    const search = searchParams.get('search')

    // Build where clause
    const whereClause: Prisma.UserWhereInput = {
      tenantId,
      isActive: true
    }

    // Add userType filter if specified
    if (userType) {
      whereClause.userType = userType as UserType // Type assertion for valid userType string
    }

    // Add search filter if specified
    if (search) {
      whereClause.OR = [
        {
          firstName: {
            contains: search
          }
        },
        {
          lastName: {
            contains: search
          }
        },
        {
          email: {
            contains: search
          }
        }
      ]
    }

    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        userType: true,
        isActive: true
      },
      take: limit,
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' }
      ]
    })

    return NextResponse.json(
      {
        success: true,
        users
      },
      { status: 200 }
    )
  } catch (e) {
    console.error("Failed to fetch users:", e)
    return NextResponse.json({ success: false, message: "Failed to fetch users" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function GET(request: NextRequest) {
  return _GET(request)
}
