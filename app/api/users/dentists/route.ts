import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { UserService } from "@/lib/auth/services/user-service";
import { AuthMiddleware, RoleGuard, AuthContext } from "@/lib/auth/services";
import { UserType } from "@prisma/client";
import { passwordSchema } from "@/lib/auth/schemas";
import { AuthUser } from "@/lib/auth/types";

const _GET = AuthMiddleware.withAuth(
  async (req: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
    try {
      const tenantId = context.authContext.user?.tenantId ?? context.authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      const { searchParams } = new URL(req.url);
      const search = searchParams.get("search") ?? undefined;

      const dentists = await UserService.getUsersByType(tenantId, UserType.DENTIST, {
        search,
        includeInactive: false,
      });

      return NextResponse.json({
        success: true,
        data: dentists.map((user: AuthUser) => ({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          username: user.username,
          isActive: user.isActive,
          createdAt: user.createdAt,
          // Working hours fields
          sat: user.sat,
          satstart: user.satstart,
          satend: user.satend,
          sun: user.sun,
          sunstart: user.sunstart,
          sunend: user.sunend,
          mon: user.mon,
          monstart: user.monstart,
          monend: user.monend,
          tue: user.tue,
          tuestart: user.tuestart,
          tueend: user.tueend,
          wed: user.wed,
          wedstart: user.wedstart,
          wedend: user.wedend,
          thu: user.thu,
          thustart: user.thustart,
          thuend: user.thuend,
          fri: user.fri,
          fristart: user.fristart,
          friend: user.friend,
        })),
      });
    } catch (error) {
      console.error("Error fetching dentists:", error);
      return NextResponse.json(
        { success: false, message: "Failed to fetch dentists" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function GET(request: NextRequest) {
  return _GET(request);
}

const _POST = AuthMiddleware.withAuth(
  async (req: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
    try {
      const tenantId = context.authContext.user?.tenantId ?? context.authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      const body = await req.json();
      const { 
        firstName, lastName, email, phoneNumber, username, password,
        // Working hours fields
        sat, satstart, satend,
        sun, sunstart, sunend,
        mon, monstart, monend,
        tue, tuestart, tueend,
        wed, wedstart, wedend,
        thu, thustart, thuend,
        fri, fristart, friend
      } = body;

      if (!firstName || !lastName) {
        return NextResponse.json(
          { success: false, message: "First name and last name are required" },
          { status: 400 }
        );
      }

      if (!username || !password) {
        return NextResponse.json(
          { success: false, message: "Username and password are required" },
          { status: 400 }
        );
      }

      const passwordParse = z
        .object({
          password: passwordSchema,
          confirmPassword: z.string().optional(),
        })
        .refine(
          (data) => !data.confirmPassword || data.password === data.confirmPassword,
          { path: ["confirmPassword"], message: "Passwords don't match" }
        )
        .safeParse({ password, confirmPassword: body.confirmPassword });

      if (!passwordParse.success) {
        const firstError = passwordParse.error.issues[0]?.message ?? "Invalid password";
        return NextResponse.json(
          { success: false, message: firstError },
          { status: 400 }
        );
      }

      const result = await UserService.createUser({
        tenantId,
        firstName: String(firstName).trim(),
        lastName: String(lastName).trim(),
        email: email?.trim() || null,
        phoneNumber: phoneNumber?.trim() || null,
        username: String(username).trim(),
        password,
        userType: UserType.DENTIST,
        isActive: true,
        createdById: context.authContext.user?.id,
      });

      if (!result.success) {
        return NextResponse.json(result, { status: 400 });
      }

      // Update with working hours fields
      const updateResult = await UserService.updateUser(parseInt(result.userId!), {
        // Working hours fields
        sat: sat ?? true,
        satstart: satstart?.trim() || null,
        satend: satend?.trim() || null,
        sun: sun ?? true,
        sunstart: sunstart?.trim() || null,
        sunend: sunend?.trim() || null,
        mon: mon ?? true,
        monstart: monstart?.trim() || null,
        monend: monend?.trim() || null,
        tue: tue ?? true,
        tuestart: tuestart?.trim() || null,
        tueend: tueend?.trim() || null,
        wed: wed ?? true,
        wedstart: wedstart?.trim() || null,
        wedend: wedend?.trim() || null,
        thu: thu ?? true,
        thustart: thustart?.trim() || null,
        thuend: thuend?.trim() || null,
        fri: fri ?? true,
        fristart: fristart?.trim() || null,
        friend: friend?.trim() || null,
      });

      if (!updateResult.success) {
        return NextResponse.json(updateResult, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        message: "Dentist created successfully",
        data: { id: result.userId },
      });
    } catch (error) {
      console.error("Error creating dentist:", error);
      return NextResponse.json(
        { success: false, message: "Failed to create dentist" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function POST(request: NextRequest) {
  return _POST(request);
}


