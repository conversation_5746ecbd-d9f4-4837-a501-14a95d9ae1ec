import { NextRequest, NextResponse } from "next/server";
// RouteContext removed in Next 15; inline context types instead
import { z } from "zod";
import { UserService } from "@/lib/auth/services/user-service";
import { AuthContext, AuthMiddleware, RoleGuard } from "@/lib/auth/services";
import { UserType } from "@prisma/client";
import { passwordSchema } from "@/lib/auth/schemas";

const _PUT = AuthMiddleware.withAuth(
  async (req: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
    const params = context.params || ({} as Record<string, string>)
    const authContext = context.authContext
    try {
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );
      }

      const userId = parseInt(params.id);
      if (isNaN(userId)) {
        return NextResponse.json(
          { success: false, message: "Invalid user ID" },
          { status: 400 }
        );
      }

      const body = await req.json();
      const { 
        firstName, lastName, email, phoneNumber, username, password,
        // Working hours fields
        sat, satstart, satend,
        sun, sunstart, sunend,
        mon, monstart, monend,
        tue, tuestart, tueend,
        wed, wedstart, wedend,
        thu, thustart, thuend,
        fri, fristart, friend
      } = body;

      if (!firstName || !lastName) {
        return NextResponse.json(
          { success: false, message: "First name and last name are required" },
          { status: 400 }
        );
      }

      if (!username) {
        return NextResponse.json(
          { success: false, message: "Username is required" },
          { status: 400 }
        );
      }

      if (password) {
        const passwordParse = z
          .object({
            password: passwordSchema,
            confirmPassword: z.string().optional(),
          })
          .refine(
            (data) => !data.confirmPassword || data.password === data.confirmPassword,
            { path: ["confirmPassword"], message: "Passwords don't match" }
          )
          .safeParse({ password, confirmPassword: body.confirmPassword });

        if (!passwordParse.success) {
          const firstError = passwordParse.error.issues[0]?.message ?? "Invalid password";
          return NextResponse.json(
            { success: false, message: firstError },
            { status: 400 }
          );
        }
      }

      const existingUser = await UserService.getUserById(userId);
      if (!existingUser || existingUser.tenantId !== tenantId || existingUser.userType !== UserType.DENTIST) {
        return NextResponse.json(
          { success: false, message: "Dentist not found" },
          { status: 404 }
        );
      }

      const updateData: Record<string, any> = {
        firstName: String(firstName).trim(),
        lastName: String(lastName).trim(),
        email: email?.trim() || null,
        phoneNumber: phoneNumber?.trim() || null,
        username: String(username).trim(),
        updatedById: authContext.user?.id,
        // Working hours fields
        sat: sat ?? true,
        satstart: satstart?.trim() || null,
        satend: satend?.trim() || null,
        sun: sun ?? true,
        sunstart: sunstart?.trim() || null,
        sunend: sunend?.trim() || null,
        mon: mon ?? true,
        monstart: monstart?.trim() || null,
        monend: monend?.trim() || null,
        tue: tue ?? true,
        tuestart: tuestart?.trim() || null,
        tueend: tueend?.trim() || null,
        wed: wed ?? true,
        wedstart: wedstart?.trim() || null,
        wedend: wedend?.trim() || null,
        thu: thu ?? true,
        thustart: thustart?.trim() || null,
        thuend: thuend?.trim() || null,
        fri: fri ?? true,
        fristart: fristart?.trim() || null,
        friend: friend?.trim() || null,
      };

      if (password) {
        updateData.password = password;
      }

      const result = await UserService.updateUser(userId, updateData as any);

      if (!result.success) {
        return NextResponse.json(result, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        message: "Dentist updated successfully",
      });
    } catch (error) {
      console.error("Error updating dentist:", error);
      return NextResponse.json(
        { success: false, message: "Failed to update dentist" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function PUT(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _PUT(request, { params } as any);
}

const _DELETE = AuthMiddleware.withAuth(
  async (req: NextRequest, context: { params?: Record<string, string>; authContext: AuthContext }) => {
    const params = context.params || ({} as Record<string, string>)
    const authContext = context.authContext
    try {
      const tenantId = authContext.user?.tenantId ?? authContext.tenant?.id;
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Tenant context required" },
          { status: 400 }
        );  
      }

      const userId = parseInt(params.id);
      if (isNaN(userId)) {
        return NextResponse.json(
          { success: false, message: "Invalid user ID" },
          { status: 400 }
        );
      }

      const existingUser = await UserService.getUserById(userId);
      if (!existingUser || existingUser.tenantId !== tenantId || existingUser.userType !== UserType.DENTIST) {
        return NextResponse.json(
          { success: false, message: "Dentist not found" },
          { status: 404 }
        );
      }

      if (authContext.user?.id && String(userId) === String(authContext.user.id)) {
        return NextResponse.json(
          { success: false, message: "Cannot delete your own account" },
          { status: 400 }
        );
      }

      const result = await UserService.updateUser(userId, {
        isActive: false,
        updatedById: authContext.user?.id,
      });

      if (!result.success) {
        return NextResponse.json(result, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        message: "Dentist deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting dentist:", error);
      return NextResponse.json(
        { success: false, message: "Failed to delete dentist" },
        { status: 500 }
      );
    }
  },
  RoleGuard.adminOnly()
);

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: number }> }) {
  const params = await context.params;
  return _DELETE(request, { params } as any);
}


