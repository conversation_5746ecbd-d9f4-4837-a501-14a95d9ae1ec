import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/auth/services";
import { SanitizationUtils, ValidationUtils } from "@/lib/auth/utils";
import { TenantContextManager } from "@/lib/tenant-context";
import prisma from "@/lib/prisma";

// PUT /api/users/profile - Update user profile information
const _PUT = AuthMiddleware.withAuth(
  async (request: NextRequest, context: { authContext: { user?: { id?: string; tenantId?: number } } }) => {
    try {
      const userId = context.authContext?.user?.id;
      const tenantId = context.authContext?.user?.tenantId;

      if (!userId || !tenantId) {
        return NextResponse.json(
          { success: false, message: "Missing authentication context" },
          { status: 401 }
        );
      }

      const body = await request.json();
      const { firstName, lastName, email } = body;

      // Sanitize input data
      const sanitizedData = {
        firstName: SanitizationUtils.sanitizeString(firstName),
        lastName: SanitizationUtils.sanitizeString(lastName),
        email: SanitizationUtils.sanitizeEmail(email),
      };

      // Validate input data
      const errors: string[] = [];
      
      if (!sanitizedData.firstName || sanitizedData.firstName.length < 1) {
        errors.push("First name is required");
      }
      
      if (!sanitizedData.lastName || sanitizedData.lastName.length < 1) {
        errors.push("Last name is required");
      }
      
      if (!ValidationUtils.isValidEmail(sanitizedData.email)) {
        errors.push("Valid email address is required");
      }

      if (errors.length > 0) {
        return NextResponse.json(
          {
            success: false,
            message: "Validation failed",
            errors,
          },
          { status: 400 }
        );
      }

      // Check if email is already in use by another user in the same tenant
      const existingUser = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.findFirst({
            where: {
              email: sanitizedData.email,
              tenantId,
              id: { not: parseInt(userId) },
              isActive: true,
            },
          });
        }
      );

      if (existingUser) {
        return NextResponse.json(
          {
            success: false,
            message: "Email is already in use by another user",
          },
          { status: 400 }
        );
      }

      // Update user profile
      const updatedUser = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.update({
            where: {
              id: parseInt(userId),
              tenantId,
            },
            data: {
              firstName: sanitizedData.firstName,
              lastName: sanitizedData.lastName,
              email: sanitizedData.email,
            },
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              username: true,
              userType: true,
            },
          });
        }
      );

      return NextResponse.json(
        {
          success: true,
          message: "Profile updated successfully",
          user: updatedUser,
        },
        { status: 200 }
      );
    } catch (error) {
      console.error("Failed to update profile:", error);
      return NextResponse.json(
        { success: false, message: "Failed to update profile" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function PUT(request: NextRequest) {
  return _PUT(request);
}
