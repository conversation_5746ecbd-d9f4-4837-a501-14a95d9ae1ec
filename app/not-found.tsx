"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export default function NotFound() {
  return (
    <main className="container mx-auto flex min-h-[60vh] flex-col items-center justify-center gap-4 p-6 text-center">
      <div className="text-5xl font-bold">404</div>
      <p className="text-muted-foreground max-w-md text-balance">
        The page you are looking for could not be found.
      </p>
      <div className="mt-2 flex items-center gap-2">
        <Button asChild>
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href="/patients">Patients</Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href="/billing">Billing</Link>
        </Button>
      </div>
    </main>
  )
}


