"use client"

import * as React from "react"
import Link from "next/link"
import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"

export default function GlobalError({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  useEffect(() => {
    // Simple console logging; replace with error reporting service integration if needed
    // e.g., Sentry.captureException(error)
     
    console.error("App error:", error)
  }, [error])

  return (
    <html>
      <body>
        <main className="container mx-auto flex min-h-svh flex-col items-center justify-center gap-4 p-6 text-center">
          <div className="text-2xl font-semibold">Something went wrong</div>
          <p className="text-muted-foreground max-w-md text-balance">
            {error?.message || "An unexpected error occurred."}
          </p>
          <div className="mt-2 flex items-center gap-2">
            <Button onClick={reset}>Try again</Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard">Go to Dashboard</Link>
            </Button>
          </div>
          {error?.digest ? (
            <div className="text-muted-foreground mt-4 text-xs">Error ID: {error.digest}</div>
          ) : null}
        </main>
      </body>
    </html>
  )
}


