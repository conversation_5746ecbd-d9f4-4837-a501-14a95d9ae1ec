"use client"

import * as React from "react"
import { Building2, <PERSON>, <PERSON><PERSON>oscope } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ClinicInformationTab } from "@/components/settings/ClinicInformationTab"
import { ReceptionistsTab } from "@/components/settings/ReceptionistsTab"
import { DentistsTab } from "@/components/settings/DentistsTab"

/**
 * Settings Page - Tabbed interface for clinic configuration
 * 
 * Tab 1: Clinic Information (tenant details)
 * Tab 2: Receptionists (to be implemented after approval)
 * Tab 3: Dentists (to be implemented after approval)
 */
export default function SettingsPage() {
  const [activeTab, setActiveTab] = React.useState("clinic")

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your clinic information, staff, and system preferences
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="clinic" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Clinic Information
          </TabsTrigger>
          <TabsTrigger value="receptionists" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Receptionists
          </TabsTrigger>
          <TabsTrigger value="dentists" className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Dentists
          </TabsTrigger>
        </TabsList>

        <TabsContent value="clinic" className="space-y-6">
          <ClinicInformationTab />
        </TabsContent>

        <TabsContent value="receptionists" className="space-y-6">
          <ReceptionistsTab />
        </TabsContent>

        <TabsContent value="dentists" className="space-y-6">
          <DentistsTab />
        </TabsContent>
      </Tabs>
    </div>
  )
}
