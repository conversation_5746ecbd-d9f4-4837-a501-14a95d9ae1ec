"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import InvoiceTable, { type InvoiceRow, type InvoiceTableFilters } from "@/components/billing/InvoiceTable"
import { ConfirmDialog } from "@/components/ui/ConfirmDialog"
import { Breadcrumbs } from "@/components/layout/Breadcrumbs"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"

export default function BillingPage() {
  const router = useRouter()
  const [filters, setFilters] = React.useState<InvoiceTableFilters>({ status: "all" })
  const [invoices, setInvoices] = React.useState<InvoiceRow[]>([])
  const [loading, setLoading] = React.useState<boolean>(false)
  const [confirmOpen, setConfirmOpen] = React.useState(false)
  const [pendingDelete, setPendingDelete] = React.useState<InvoiceRow | null>(null)

  React.useEffect(() => {
    const load = async () => {
      setLoading(true)
      try {
        const res = await fetch(`/api/billing/invoices`)
        if (!res.ok) { setInvoices([]); return }
        const data = await res.json()
        const rows: InvoiceRow[] = (Array.isArray(data) ? data : []).map((inv: any) => ({
          id: inv.id,
          serial: inv.serial,
          date: inv.invoiceDate,
          patientName: `${inv.patient?.firstName ?? ''} ${inv.patient?.lastName ?? ''}`.trim() || '—',
          status: (String(inv.status || '').toLowerCase() as any),
          totalAmount: Number(inv.totalAmount ?? 0),
          amountPaid: Number(inv.amountPaid ?? 0),
          balanceDue: Number(inv.balanceDue ?? 0),
        }))
        setInvoices(rows)
      } finally {
        setLoading(false)
      }
    }
    load()
  }, [])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col">
            <CardTitle>Billing</CardTitle>
            <CardDescription>Review and filter invoices.</CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <InvoiceTable
            invoices={invoices}
            filters={filters}
            onFiltersChange={setFilters}
            onRowClick={(invoice) => router.push(`/dashboard/billing/invoices/${invoice.id}`)}
            onEditInvoice={undefined}
            onDeleteInvoice={(invoice) => { setPendingDelete(invoice); setConfirmOpen(true) }}
          />
        </CardContent>
      </Card>
      <ConfirmDialog
        open={confirmOpen}
        onOpenChange={setConfirmOpen}
        title="Delete invoice?"
        description="This will permanently remove the invoice. This action cannot be undone."
        confirmText="Delete"
        destructive
        onConfirm={async () => {
          if (!pendingDelete) return
          try {
            await fetch(`/api/billing/invoices/${pendingDelete.id}`, { method: 'DELETE' })
            setInvoices((prev) => prev.filter((i) => String(i.id) !== String(pendingDelete.id)))
          } finally {
            setConfirmOpen(false); setPendingDelete(null)
          }
        }}
        onCancel={() => { setConfirmOpen(false); setPendingDelete(null) }}
      />
    </div>
  )
}


