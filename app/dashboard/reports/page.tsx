"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { formatCurrency } from "@/lib/currency"
import { Download, ChevronLeft, ChevronRight, BarChart3 } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from "recharts"

type ReportType = "revenue-summary" | "accounts-receivable" | "appointment-utilization" | "patient-demographics" | "treatment-completion"

interface ReportData {
  data: any[]
  metadata: {
    totalRecords: number
    generatedAt: Date
    dateRange: { start: Date; end: Date }
  }
}

// Helper function to determine if a field should be formatted as currency
const isCurrencyField = (key: string): boolean => {
  const currencyFields = ['amount', 'cost', 'balance', 'paid', 'due']
  const nonCurrencyFields = ['total visits', 'totalvisits', 'visits', 'count', 'records']
  
  // First check if it's explicitly a non-currency field
  if (nonCurrencyFields.some(field => key.toLowerCase().includes(field))) {
    return false
  }
  
  // Then check if it contains 'total' - only format as currency if it's actually monetary
  if (key.toLowerCase().includes('total')) {
    return currencyFields.some(field => key.toLowerCase().includes(field)) || 
           key.toLowerCase().includes('revenue') || 
           key.toLowerCase().includes('payment')
  }
  
  return currencyFields.some(field => key.toLowerCase().includes(field))
}

// Helper function to format values based on their type and field name
const formatValue = (value: any, key: string): string => {
  if (typeof value === 'number') {
    if (isCurrencyField(key)) {
      return formatCurrency(value)
    }
    // For non-currency numbers, just format with locale
    return value.toLocaleString()
  }
  return String(value)
}

// Helper function to export data to CSV
const exportToCSV = (data: any[], filename: string) => {
  if (!data || data.length === 0) return

  // Filter out summary and separator rows for CSV export
  const exportData = data.filter(row => !row.summary && !row.separator)
  
  if (exportData.length === 0) return

  // Get headers from the first row
  const headers = Object.keys(exportData[0])
  
  // Create CSV content
  const csvContent = [
    // Headers
    headers.join(','),
    // Data rows
    ...exportData.map(row => 
      headers.map(header => {
        const value = row[header]
        // Handle values that might contain commas or quotes
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      }).join(',')
    )
  ].join('\n')

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export default function ReportsPage() {
  const [report, setReport] = React.useState<ReportType>("revenue-summary")
  const [dateFrom, setDateFrom] = React.useState<string>(() => {
    const today = new Date()
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1)
    return firstDay.toISOString().split('T')[0]
  })
  const [dateTo, setDateTo] = React.useState<string>(() => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  })
  const [query, setQuery] = React.useState<string>("")
  const [reportData, setReportData] = React.useState<ReportData | null>(null)
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string>("")
  const [currentPage, setCurrentPage] = React.useState(1)
  const [itemsPerPage] = React.useState(50)
  const [showCharts, setShowCharts] = React.useState(false)

  // Date range presets
  const setDatePreset = (preset: string) => {
    const today = new Date()
    let start: Date
    let end: Date = today

    switch (preset) {
      case 'last7days':
        start = new Date(today)
        start.setDate(today.getDate() - 7)
        break
      case 'last30days':
        start = new Date(today)
        start.setDate(today.getDate() - 30)
        break
      case 'last90days':
        start = new Date(today)
        start.setDate(today.getDate() - 90)
        break
      case 'thisMonth':
        start = new Date(today.getFullYear(), today.getMonth(), 1)
        break
      case 'lastMonth':
        start = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        end = new Date(today.getFullYear(), today.getMonth(), 0)
        break
      case 'thisYear':
        start = new Date(today.getFullYear(), 0, 1)
        break
      default:
        return
    }

    setDateFrom(start.toISOString().split('T')[0])
    setDateTo(end.toISOString().split('T')[0])
  }

  const reportConfig = {
    "revenue-summary": {
      name: "Revenue Summary",
      description: "Track overall practice financial performance and payment trends"
    },
    "accounts-receivable": {
      name: "Accounts Receivable",
      description: "Monitor outstanding payments and collection efficiency"
    },
    "appointment-utilization": {
      name: "Appointment Utilization",
      description: "Analyze scheduling efficiency and appointment completion rates"
    },
    "patient-demographics": {
      name: "Patient Demographics",
      description: "Understand patient base and demographic distribution"
    },
    "treatment-completion": {
      name: "Treatment Completion",
      description: "Monitor clinical productivity and treatment status"
    }
  }

  const run = async () => {
    if (!dateFrom || !dateTo) {
      setError("Please select both start and end dates")
      return
    }

    setLoading(true)
    setError("")
    setReportData(null)
    setCurrentPage(1) // Reset to first page

    try {
      const response = await fetch("/api/reports", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reportType: report,
          dateFrom,
          dateTo,
          filters: query ? { search: query } : {}
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || "Failed to generate report")
      }

      if (result.success) {
        setReportData(result.report)
      } else {
        throw new Error(result.message || "Failed to generate report")
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred while generating the report")
    } finally {
      setLoading(false)
    }
  }

  const handleExport = () => {
    if (!reportData) return
    
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `${reportConfig[report].name.replace(/\s+/g, '_')}_${timestamp}.csv`
    exportToCSV(reportData.data, filename)
  }

  // Prepare chart data from summary rows
  const getChartData = () => {
    if (!reportData) return null
    
    const summaryRows = reportData.data.filter(row => row.summary && !row.separator)
    
    // Find breakdown data (payment methods, status breakdown, age groups, etc.)
    const breakdownRow = summaryRows.find(row => 
      row.summary?.toLowerCase().includes('breakdown') || 
      row.summary?.toLowerCase().includes('distribution')
    )
    
    if (!breakdownRow) return null
    
    // Convert breakdown data to chart format
    const chartData = Object.entries(breakdownRow)
      .filter(([key]) => key !== 'summary')
      .map(([key, value]) => ({
        name: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
        value: typeof value === 'number' ? value : 0,
        formattedValue: formatValue(value, key)
      }))
    
    return chartData.length > 0 ? chartData : null
  }

  // Prepare treatment-specific chart data for procedure vs tooth analysis
  const getTreatmentChartData = () => {
    if (!reportData || report !== 'treatment-completion') return null
    
    const dataRows = reportData.data.filter(row => !row.summary && !row.separator)
    
    if (dataRows.length === 0) return null
    
    // Group by procedure name
    const procedureData = dataRows.reduce((acc: any, row: any) => {
      const procedure = row.procedureName || 'Unknown'
      acc[procedure] = (acc[procedure] || 0) + 1
      return acc
    }, {})
    
    // Group by tooth number
    const toothData = dataRows.reduce((acc: any, row: any) => {
      const tooth = row.toothNumber || 'N/A'
      if (tooth !== 'N/A') {
        acc[tooth] = (acc[tooth] || 0) + 1
      }
      return acc
    }, {})
    
    // Convert to chart format
    const procedureChartData = Object.entries(procedureData)
      .map(([name, count]) => ({
        name,
        value: count as number,
        formattedValue: `${count} treatments`
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 10) // Top 10 procedures
    
    const toothChartData = Object.entries(toothData)
      .map(([name, count]) => ({
        name: `Tooth ${name}`,
        value: count as number,
        formattedValue: `${count} treatments`
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 15) // Top 15 teeth
    
    return {
      procedures: procedureChartData.length > 0 ? procedureChartData : null,
      teeth: toothChartData.length > 0 ? toothChartData : null
    }
  }

  const chartData = getChartData()
  const treatmentChartData = getTreatmentChartData()
  
  // Colors for charts
  const CHART_COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  const renderReportData = () => {
    if (!reportData) return null

    const { data, metadata } = reportData

    // Separate summary rows from data rows, and handle aging headers
    const summaryRows = data.filter(row => row.summary || row.separator)
    const agingHeaders = data.filter(row => row.agingHeader)
    const allDataRows = data.filter(row => !row.summary && !row.separator && !row.agingHeader)
    
    // Pagination logic
    const totalDataRows = allDataRows.length
    const totalPages = Math.ceil(totalDataRows / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const dataRows = allDataRows.slice(startIndex, endIndex)

    return (
      <div className="space-y-4">
        {/* Report Metadata */}
        <div className="flex justify-between items-center text-sm text-muted-foreground">
          <div>
            Generated on {new Date(metadata.generatedAt).toLocaleDateString()} at{" "}
            {new Date(metadata.generatedAt).toLocaleTimeString()}
          </div>
          <div>
            Records: {metadata.totalRecords} |
            Period: {new Date(metadata.dateRange.start).toLocaleDateString()} -{" "}
            {new Date(metadata.dateRange.end).toLocaleDateString()}
          </div>
        </div>

        {/* Charts Section */}
        {showCharts && (chartData || treatmentChartData) && (
          <div className="space-y-6">
            <h3 className="font-medium">Data Visualization</h3>
            
            {/* Standard Charts (for all report types) */}
            {chartData && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Bar Chart */}
                <div className="border rounded-md p-4">
                  <h4 className="text-sm font-medium mb-4 text-center">Bar Chart</h4>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="name" 
                        tick={{ fontSize: 12 }}
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip 
                        formatter={(value, name) => {
                          const item = chartData.find(d => d.value === value)
                          return [item?.formattedValue || value, 'Value']
                        }}
                      />
                      <Bar dataKey="value" fill="#0088FE" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>

                {/* Pie Chart */}
                <div className="border rounded-md p-4">
                  <h4 className="text-sm font-medium mb-4 text-center">Distribution</h4>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={chartData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        labelLine={false}
                      >
                        {chartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip 
                        formatter={(value, name) => {
                          const item = chartData.find(d => d.value === value)
                          return [item?.formattedValue || value, 'Value']
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            )}

            {/* Treatment-Specific Charts */}
            {treatmentChartData && (treatmentChartData.procedures || treatmentChartData.teeth) && (
              <div className="space-y-6">
                <h3 className="font-medium">Treatment Analysis</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Procedures Chart */}
                  {treatmentChartData.procedures && (
                    <div className="border rounded-md p-4">
                      <h4 className="text-sm font-medium mb-4 text-center">Top Procedures</h4>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={treatmentChartData.procedures}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis 
                            dataKey="name" 
                            tick={{ fontSize: 11 }}
                            angle={-45}
                            textAnchor="end"
                            height={100}
                          />
                          <YAxis tick={{ fontSize: 12 }} />
                          <Tooltip 
                            formatter={(value, name) => {
                              const item = treatmentChartData.procedures?.find(d => d.value === value)
                              return [item?.formattedValue || value, 'Treatments']
                            }}
                          />
                          <Bar dataKey="value" fill="#00C49F" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  )}

                  {/* Tooth Numbers Chart */}
                  {treatmentChartData.teeth && (
                    <div className="border rounded-md p-4">
                      <h4 className="text-sm font-medium mb-4 text-center">Most Treated Teeth</h4>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={treatmentChartData.teeth}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis 
                            dataKey="name" 
                            tick={{ fontSize: 11 }}
                            angle={-45}
                            textAnchor="end"
                            height={80}
                          />
                          <YAxis tick={{ fontSize: 12 }} />
                          <Tooltip 
                            formatter={(value, name) => {
                              const item = treatmentChartData.teeth?.find(d => d.value === value)
                              return [item?.formattedValue || value, 'Treatments']
                            }}
                          />
                          <Bar dataKey="value" fill="#FFBB28" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Accounts Receivable Aging Buckets */}
        {!showCharts && report === 'accounts-receivable' && (
          <div>
            <h3 className="font-medium mb-4">Accounts Receivable Aging Analysis</h3>
            
            {/* Summary Cards */}
            {agingHeaders.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                {agingHeaders.map((header, i) => (
                  <div key={i} className="p-4 border rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
                    <div className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">
                      {header.agingHeader}
                    </div>
                    <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                      {formatValue(header.total, 'total')}
                    </div>
                    <div className="text-xs text-blue-600 dark:text-blue-400">
                      {header.count} {header.count === 1 ? 'invoice' : 'invoices'}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Detailed Aging Buckets */}
            {(() => {
              // Group data by aging periods
              const agingBuckets: Record<string, any[]> = {}
              let currentBucket = ''
              
              data.forEach(row => {
                if (row.agingHeader) {
                  currentBucket = row.agingHeader
                  agingBuckets[currentBucket] = []
                } else if (currentBucket && !row.separator && !row.summary && row.invoiceId) {
                  agingBuckets[currentBucket].push(row)
                }
              })

              const bucketOrder = ['0-30 days', '31-60 days', '61-90 days', '90+ days']
              
              return (
                <div className="space-y-6">
                  {bucketOrder.map(bucketName => {
                    const bucketInvoices = agingBuckets[bucketName] || []
                    const bucketHeader = agingHeaders.find(h => h.agingHeader === bucketName)
                    
                    if (bucketInvoices.length === 0) return null
                    
                    return (
                      <div key={bucketName} className="border rounded-lg overflow-hidden">
                        {/* Bucket Header */}
                        <div className="bg-muted px-4 py-3 border-b">
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium text-lg">{bucketName}</h4>
                            <div className="flex items-center gap-4">
                              <span className="text-sm text-muted-foreground">
                                {bucketInvoices.length} {bucketInvoices.length === 1 ? 'invoice' : 'invoices'}
                              </span>
                              <span className="font-semibold text-lg">
                                {formatValue(bucketHeader?.total || 0, 'total')}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        {/* Bucket Table */}
                        <Table>
                          <TableHeader>
                            <TableRow>
                              {Object.keys(bucketInvoices[0]).map((key) => (
                                <TableHead key={key} className="capitalize">
                                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                                </TableHead>
                              ))}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {bucketInvoices.map((row, i) => (
                              <TableRow key={i}>
                                {Object.keys(row).map((key) => (
                                  <TableCell key={key}>
                                    {formatValue(row[key], key)}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )
                  })}
                </div>
              )
            })()}
          </div>
        )}

        {/* Regular Data Table for other reports */}
        {!showCharts && report !== 'accounts-receivable' && dataRows.length > 0 && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium">Report Data</h3>
              {totalDataRows > itemsPerPage && (
                <div className="text-sm text-muted-foreground">
                  Showing {startIndex + 1}-{Math.min(endIndex, totalDataRows)} of {totalDataRows} records
                </div>
              )}
            </div>
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    {Object.keys(dataRows[0]).map((key) => (
                      <TableHead key={key} className="capitalize">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dataRows.map((row, i) => (
                    <TableRow key={i}>
                      {Object.keys(row).map((key) => (
                        <TableCell key={key}>
                          {formatValue(row[key], key)}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Summary Section */}
        {summaryRows.length > 0 && (
          <div>
            <h3 className="font-medium mb-2">Summary</h3>
            
            {/* Special handling for Accounts Receivable total */}
            {report === 'accounts-receivable' ? (
              (() => {
                const totalOutstandingRow = summaryRows.find(row => row.summary === "Total Outstanding")
                const agingBreakdownRow = summaryRows.find(row => row.summary === "Aging Breakdown")
                
                return (
                  <div className="space-y-4">
                    {/* Overall Total - Prominent Display */}
                    {totalOutstandingRow && (
                      <div className="p-6 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950 dark:to-orange-950 border-2 border-red-200 dark:border-red-800 rounded-lg">
                        <div className="text-center">
                          <div className="text-sm font-medium text-red-700 dark:text-red-300 mb-2">
                            TOTAL ACCOUNTS RECEIVABLE
                          </div>
                          <div className="text-4xl font-bold text-red-900 dark:text-red-100">
                            {formatValue(totalOutstandingRow.amount, 'amount')}
                          </div>
                          <div className="text-sm text-red-600 dark:text-red-400 mt-1">
                            Outstanding Balance
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Aging Breakdown Summary */}
                    {agingBreakdownRow && (
                      <div className="p-4 bg-muted rounded-md">
                        <div className="font-medium mb-3">Aging Period Summary</div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          {Object.entries(agingBreakdownRow).filter(([key]) => key !== 'summary').map(([key, value]) => (
                            <div key={key} className="text-center p-3 bg-background rounded border">
                              <div className="text-xs text-muted-foreground mb-1">
                                {key}
                              </div>
                              <div className="font-semibold">
                                {formatValue(value, key)}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )
              })()
            ) : (
              <div className="grid gap-2">
                {summaryRows.map((row, i) => {
                  if (row.separator) {
                    return <hr key={i} className="my-4" />
                  }

                  if (row.summary) {
                    const summaryData = Object.entries(row).filter(([key]) => key !== 'summary')

                    if (summaryData.length === 1) {
                      const [key, value] = summaryData[0]
                      return (
                        <div key={i} className="flex justify-between items-center p-3 bg-muted rounded-md">
                          <span className="font-medium">{row.summary}</span>
                          <span className="text-lg font-bold">
                            {formatValue(value, key)}
                          </span>
                        </div>
                      )
                    }

                    return (
                      <div key={i} className="p-3 bg-muted rounded-md">
                        <div className="font-medium mb-2">{row.summary}</div>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {summaryData.map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-sm text-muted-foreground capitalize">
                                {key.replace(/([A-Z])/g, ' $1')}
                              </span>
                              <Badge variant="secondary">
                                {formatValue(value, key)}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )
                  }

                  return null
                })}
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Reports</CardTitle>
          <CardDescription>
            Generate comprehensive reports for your dental practice. Select a report type and date range to get started.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Date Range Presets */}
            <div>
              <label className="text-xs text-muted-foreground mb-2 block">Quick Date Ranges</label>
              <div className="flex flex-wrap gap-2">
                {[
                  { key: 'last7days', label: 'Last 7 Days' },
                  { key: 'last30days', label: 'Last 30 Days' },
                  { key: 'last90days', label: 'Last 90 Days' },
                  { key: 'thisMonth', label: 'This Month' },
                  { key: 'lastMonth', label: 'Last Month' },
                  { key: 'thisYear', label: 'This Year' }
                ].map(preset => (
                  <Button
                    key={preset.key}
                    variant="outline"
                    size="sm"
                    onClick={() => setDatePreset(preset.key)}
                    className="text-xs h-7"
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Main Controls */}
            <div className="flex flex-wrap items-end gap-2">
              <div className="flex flex-col">
                <label className="text-xs text-muted-foreground">Report Type</label>
                <Select value={report} onValueChange={(v) => setReport(v as ReportType)}>
                  <SelectTrigger className="h-9 w-52">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(reportConfig).map(([value, config]) => (
                      <SelectItem key={value} value={value}>
                        <div>
                          <div className="font-medium">{config.name}</div>
                          <div className="text-xs text-muted-foreground">{config.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col">
                <label className="text-xs text-muted-foreground">From Date</label>
                <Input type="date" value={dateFrom} onChange={(e) => setDateFrom(e.target.value)} className="h-9 w-44" />
              </div>
              <div className="flex flex-col">
                <label className="text-xs text-muted-foreground">To Date</label>
                <Input type="date" value={dateTo} onChange={(e) => setDateTo(e.target.value)} className="h-9 w-44" />
              </div>
              <div className="flex flex-col min-w-40">
                <label className="text-xs text-muted-foreground">Search Filter</label>
                <Input
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="Optional search term"
                  className="h-9 w-64"
                />
              </div>
              <Button onClick={run} disabled={loading} className="ml-auto">
                {loading ? "Generating..." : "Generate Report"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Results */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>
              {reportConfig[report].name} Report
              {reportData && (
                <Badge variant="outline" className="ml-2">
                  {reportData.metadata.totalRecords} records
                </Badge>
              )}
            </CardTitle>
            {reportData && (
              <div className="flex items-center gap-2">
                {(chartData || treatmentChartData) && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setShowCharts(!showCharts)}
                    className="flex items-center gap-2"
                  >
                    <BarChart3 className="h-4 w-4" />
                    {showCharts ? 'Show Table' : 'Show Charts'}
                  </Button>
                )}
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleExport}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export CSV
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Generating report...</p>
            </div>
          ) : reportData ? (
            renderReportData()
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              Select your report parameters and click &ldquo;Generate Report&rdquo; to view results.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}


