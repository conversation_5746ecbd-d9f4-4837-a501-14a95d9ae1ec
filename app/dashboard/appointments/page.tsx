"use client"

import React, { Suspense } from "react"
import { AppointmentsCalendar } from "@/components/appointments/AppointmentsCalendar"

export default function AppointmentsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">Appointments</h1>
        <p className="text-muted-foreground">
          Manage your patient appointments and schedule.
        </p>
      </div>
      <Suspense fallback={<div className="flex items-center justify-center h-96"><div className="text-muted-foreground">Loading calendar...</div></div>}>
        <AppointmentsCalendar />
      </Suspense>
    </div>
  )
}
