"use client"

import * as React from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { PatientHeader } from "@/components/patients/PatientHeader"
import { CaseSheetCard } from "@/components/patients/CaseSheetCard"
import { DentalChart } from "@/components/clinical/DentalChart"
import { DentalChartSummary } from "@/components/clinical/DentalChartSummary"
import { FindingsPanel } from "@/components/clinical/FindingsPanel"
import { FindingsList } from "@/components/clinical/FindingsList"
import { FindingForm } from "@/components/clinical/FindingForm"
import { TreatmentPlanningPanel } from "@/components/clinical/TreatmentPlanningPanel"
import { TreatmentForm } from "@/components/clinical/TreatmentForm"
import { LoadingSpinner } from "@/components/ui/LoadingSpinner"
import { EmptyState } from "@/components/ui/EmptyState"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Nfc } from "lucide-react"
import { PatientForm } from "@/components/patients/PatientForm"
import { PatientNFC } from "@/components/patients/PatientNFC"
import { toast } from "sonner"
import { formatDateLocale, formatDateForInput, formatDate } from "@/lib/date-utils"
import { cn } from "@/lib/utils"
import DeleteTreatmentConfirm from "@/components/clinical/DeleteTreatmentConfirm"
import DeleteFindingConfirm from "@/components/clinical/DeleteFindingConfirm"

interface PatientBasic {
  id: number
  firstName: string
  lastName: string
  phoneNumber?: string | null
  email?: string | null
  address?: string | null
  dateOfBirth?: string | Date | null
  uid?: string | null
  caseSheetId?: number | null
}

async function fetchPatientById(id: number): Promise<PatientBasic | null> {
  const tenantId = typeof window !== "undefined" ? localStorage.getItem("tenantId") : null
  const res = await fetch(`/api/patients/search`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ tenantId, patientId: id }),
  })
  if (!res.ok) return null
  const json = await res.json()
  const p = (json.patients ?? [])[0]
  return p ?? null
}

export default function PatientDetailsPage() {
  const params = useParams<{ id: number }>()
  const router = useRouter()
  const [loading, setLoading] = React.useState(true)
  const [patient, setPatient] = React.useState<PatientBasic | null>(null)
  const [caseSheet, setCaseSheet] = React.useState<any | null>(null)
  const [editOpen, setEditOpen] = React.useState(false)
  const [selectedTooth, setSelectedTooth] = React.useState<number | undefined>(undefined)
  const [panelOpen, setPanelOpen] = React.useState(false)
  const [addingFinding, setAddingFinding] = React.useState(false)
  const [activeFinding, setActiveFinding] = React.useState<{ id: number; description: string } | null>(null)
  const [editingFinding, setEditingFinding] = React.useState<{ id: number; description: string; severity?: "low" | "medium" | "high" } | null>(null)
  const [planningOpen, setPlanningOpen] = React.useState(false)
  const [treatmentDialogOpen, setTreatmentDialogOpen] = React.useState(false)
  const [submitting, setSubmitting] = React.useState(false)
  const lastCreatedFindingRef = React.useRef<{ id: number; description: string } | null>(null)
  const [deletingTreatment, setDeletingTreatment] = React.useState<{ id: number | string } | null>(null)
  const [editingTreatment, setEditingTreatment] = React.useState<{ id: number | string; procedureName: string; cost: number; notes?: string } | null>(null)
  const [deletingFinding, setDeletingFinding] = React.useState<{ id: number | string } | null>(null)
  const [nfcDialogOpen, setNfcDialogOpen] = React.useState(false)

  React.useEffect(() => {
    const load = async () => {
      const idNum = Number(params.id)
      if (!Number.isFinite(idNum)) {
        setLoading(false)
        return
      }
      let current: PatientBasic | null = null
      // Prefer full GET endpoint; fallback to search if unavailable
      try {
        const resFull = await fetch(`/api/patients/${idNum}`, { method: "GET", credentials: "include" })
        if (resFull.ok) {
          const json = await resFull.json()
          current = json.patient ?? null
        }
      } catch {}
      if (!current) {
        current = await fetchPatientById(idNum)
      }
      setPatient(current)
      if (current?.caseSheetId) {
        const res = await fetch(`/api/clinical/case-sheets/${current.caseSheetId}`, { method: "GET", credentials: "include" })
        if (res.ok) {
          const json = await res.json()
          setCaseSheet(json.caseSheet)
        }
      }
      setLoading(false)
    }
    load()
  }, [params.id])

  const reloadCaseSheet = React.useCallback(async () => {
    if (!patient?.caseSheetId) return
    try {
      const res = await fetch(`/api/clinical/case-sheets/${patient.caseSheetId}`, { method: "GET", credentials: "include" })
      if (!res.ok) throw new Error("Failed to reload case sheet")
      const json = await res.json()
      setCaseSheet(json.caseSheet)
    } catch (e) {
      toast.error("Unable to refresh clinical data")
    }
  }, [patient?.caseSheetId])

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <LoadingSpinner size="lg" label="Loading patient" />
      </div>
    )
  }

  if (!patient) {
    return (
      <div className="p-4">
        <EmptyState title="Patient not found" description="Return to the patients list and try again." />
      </div>
    )
  }

  const name = `${patient.firstName} ${patient.lastName}`

  return (
    <div className="p-4 space-y-4">

      {/* Patient information */}
      <Card>
        <CardHeader className="flex-row items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle>Patient Information</CardTitle>
            <Badge 
              variant={patient.uid ? "default" : "outline"}
              className={patient.uid 
                ? "bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800" 
                : "bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-800"
              }
            >
              <Nfc className="w-3 h-3" />
              {patient.uid ? "NFC Assigned" : "No NFC"}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline" onClick={() => setNfcDialogOpen(true)}>
              Assign New NFC Tag
            </Button>
            <Button size="sm" onClick={() => setEditOpen(true)}>Edit Patient</Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 text-sm">
            <div>
              <div className="text-muted-foreground">First name</div>
              <div className="font-medium">{patient.firstName}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Last name</div>
              <div className="font-medium">{patient.lastName}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Phone</div>
              <div className="font-medium font-mono">{patient.phoneNumber ?? "—"}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Email</div>
              <div className="font-medium font-mono">{patient.email ?? "—"}</div>
            </div>
            <div className="sm:col-span-2 lg:col-span-1">
              <div className="text-muted-foreground">Address</div>
              <div className="font-medium">{patient.address ?? "—"}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Date of birth</div>
              <div className="font-medium">{patient.dateOfBirth ? formatDate(patient.dateOfBirth) : "—"}</div>
            </div>
            <div>
              <div className="text-muted-foreground">NFC UID</div>
              <div className="font-medium font-mono">{patient.uid ?? "—"}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Case sheet</div>
              <div className="font-medium">{caseSheet?.serial ? `#${caseSheet.serial}` : "—"}</div>
            </div>
          </div>
        </CardContent>
      </Card>
      <CaseSheetCard
        hasCaseSheet={Boolean(caseSheet)}
        lastUpdated={caseSheet?.updatedAt ?? null}
        loading={false}
        onInitialize={async () => {
          if (!patient?.id) return
          const res = await fetch(`/api/clinical/case-sheets/${patient.id}/initialize`, { method: "POST", credentials: "include" })
          if (res.ok) {
            const json = await res.json()
            setCaseSheet(json.caseSheet)
            toast.success("Case sheet initialized")
          } else {
            toast.error("Failed to initialize case sheet")
          }
        }}
      />

      {caseSheet ? (
        <div className="space-y-4">
          <DentalChart
            statusByTooth={Object.fromEntries(
              (caseSheet.teeth ?? []).map((t: any) => [
                t.toothNumber,
                {
                  present: t.status !== "MISSING" && t.status !== "EXTRACTED",
                  hasFindings: Array.isArray(t.findings) && t.findings.length > 0,
                  hasPendingTreatments: Array.isArray(t.findings) && t.findings.some((f: any) => (f.treatments ?? []).some((tr: any) => tr.status === "PENDING")),
                  hasCompletedTreatments: Array.isArray(t.findings) && t.findings.some((f: any) => (f.treatments ?? []).some((tr: any) => tr.status === "COMPLETED")),
                },
              ])
            )}
            tooltipDataByTooth={Object.fromEntries(
              (caseSheet.teeth ?? []).map((t: any) => [
                t.toothNumber,
                {
                  findings: (t.findings ?? []).map((f: any) => ({ id: f.id, description: f.description, severity: f.severity, createdAt: f.recordedDate ?? f.createdAt })),
                  treatments: (t.findings ?? [])
                    .flatMap((f: any) => f.treatments ?? [])
                    .map((tr: any) => ({ id: tr.id, procedureName: tr.procedureName, status: (tr.status ?? "PENDING").toLowerCase(), cost: typeof tr.cost === "number" ? tr.cost : Number(tr.cost) }))
                }
              ])
            )}
            toothIdByNumber={Object.fromEntries((caseSheet.teeth ?? []).map((t: any) => [t.toothNumber, t.id]))}
            onAfterBulkApply={reloadCaseSheet}
            patientId={patient.id}
            onAfterInvoiceCreate={reloadCaseSheet}
            onSelectionChange={(toothNumber) => {
              setSelectedTooth(toothNumber)
              setPanelOpen(true)
              setAddingFinding(false)
              setActiveFinding(null)
            }}
          />

          {/* Textual, recent-activity style summary of chart */}
          <DentalChartSummary
            teeth={(caseSheet.teeth ?? []).map((t: any) => ({
              toothNumber: t.toothNumber,
              status: t.status,
              findings: (t.findings ?? []).map((f: any) => ({
                id: f.id,
                description: f.description,
                severity: f.severity ?? undefined,
                createdAt: f.recordedDate ?? f.createdAt,
                treatments: (f.treatments ?? []).map((tr: any) => ({
                  id: tr.id,
                  procedureName: tr.procedureName,
                  status: String(tr.status ?? "PENDING").toLowerCase(),
                  cost: Number(tr.cost ?? 0),
                  notes: tr.notes ?? undefined,
                })),
              })),
            }))}
          />

          <FindingsPanel
            open={panelOpen}
            onClose={() => setPanelOpen(false)}
            tooth={{ number: selectedTooth ?? 0 }}
            findingsCount={(() => {
              const t = (caseSheet.teeth ?? []).find((x: any) => x.toothNumber === selectedTooth)
              return (t?.findings ?? []).length
            })()}
            onAddClick={() => setAddingFinding(true)}
          >
            <div className="space-y-4">
              {addingFinding && (
                <FindingForm
                  loading={submitting}
                  onSubmit={async (values) => {
                    const tooth = (caseSheet.teeth ?? []).find((x: any) => x.toothNumber === selectedTooth)
                    if (!tooth) {
                      toast.error("Select a valid tooth")
                      return
                    }
                    setSubmitting(true)
                    try {
                      const res = await fetch(`/api/clinical/teeth/${tooth.id}/findings`, {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        credentials: "include",
                        body: JSON.stringify({ description: values.description, severity: values.severity }),
                      })
                      if (!res.ok) throw new Error("Failed to save finding")
                      const json = await res.json()
                      const created = json.finding
                      lastCreatedFindingRef.current = created ? { id: created.id, description: created.description } : null
                      toast.success("Finding saved")
                      setAddingFinding(false)
                      await reloadCaseSheet()
                    } catch (e) {
                      toast.error((e as Error)?.message || "Failed to save finding")
                    } finally {
                      setSubmitting(false)
                    }
                  }}
                  onCancel={() => setAddingFinding(false)}
                  onCreateTreatment={() => {
                    const created = lastCreatedFindingRef.current
                    if (created) {
                      setActiveFinding(created)
                      setPlanningOpen(true)
                    }
                  }}
                />
              )}

              {(() => {
                const t = (caseSheet.teeth ?? []).find((x: any) => x.toothNumber === selectedTooth)
                const findings = t?.findings ?? []
                return (
                  <FindingsList
                    findings={findings.map((f: any) => ({ id: f.id, description: f.description, severity: f.severity ?? undefined, recordedBy: f.recordedBy?.firstName ? `${f.recordedBy.firstName} ${f.recordedBy.lastName ?? ''}`.trim() : (f.recordedBy?.name ?? "—"), createdAt: f.recordedDate ?? f.createdAt }))}
                    loading={false}
                    onSelect={(findingId) => {
                      const f = findings.find((x: any) => x.id === findingId)
                      if (f) {
                        setActiveFinding({ id: f.id, description: f.description })
                        setPlanningOpen(true)
                      }
                    }}
                    renderActions={(finding) => (
                      <div className="flex items-center gap-2 text-xs">
                        <button
                          className="inline-flex items-center rounded-md border px-2 py-1 text-[11px] hover:bg-muted"
                          onClick={() => {
                            const f = findings.find((x: any) => x.id === finding.id)
                            if (f) {
                              setEditingFinding({ id: f.id, description: f.description, severity: f.severity ?? undefined })
                            }
                          }}
                        >
                          Edit
                        </button>
                        <button
                          className="inline-flex items-center rounded-md border border-destructive/40 text-destructive px-2 py-1 text-[11px] hover:bg-destructive/10"
                          onClick={() => setDeletingFinding({ id: finding.id })}
                        >
                          Delete
                        </button>
                        <button
                          className="inline-flex items-center rounded-md border px-2 py-1 text-[11px] hover:bg-muted"
                          onClick={() => {
                            const f = findings.find((x: any) => x.id === finding.id)
                            if (f) {
                              setActiveFinding({ id: f.id, description: f.description })
                              setPlanningOpen(true)
                            }
                          }}
                        >
                          Plan treatments
                        </button>
                      </div>
                    )}
                  />
                )
              })()}
            </div>
          </FindingsPanel>

          <Dialog open={Boolean(editingFinding)} onOpenChange={(open) => !open && setEditingFinding(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Finding</DialogTitle>
              </DialogHeader>
              <FindingForm
                defaultValues={editingFinding ? { description: editingFinding.description, severity: editingFinding.severity } : undefined}
                loading={submitting}
                onSubmit={async (values) => {
                  if (!editingFinding) return
                  setSubmitting(true)
                  try {
                    const res = await fetch(`/api/clinical/findings/${editingFinding.id}`, {
                      method: "PATCH",
                      headers: { "Content-Type": "application/json" },
                      credentials: "include",
                      body: JSON.stringify({ description: values.description, severity: values.severity }),
                    })
                    if (!res.ok) throw new Error("Failed to update finding")
                    toast.success("Finding updated")
                    setEditingFinding(null)
                    await reloadCaseSheet()
                  } catch (e) {
                    toast.error((e as Error)?.message || "Failed to update finding")
                  } finally {
                    setSubmitting(false)
                  }
                }}
                onCancel={() => setEditingFinding(null)}
                showCreateTreatmentPrompt={false}
              />
            </DialogContent>
          </Dialog>

          <DeleteFindingConfirm
            open={Boolean(deletingFinding)}
            onOpenChange={(open) => !open && setDeletingFinding(null)}
            onConfirm={async () => {
              if (!deletingFinding) return
              try {
                const res = await fetch(`/api/clinical/findings/${deletingFinding.id}`, { method: "DELETE", credentials: "include" })
                if (!res.ok) throw new Error("Failed to delete finding")
                toast.success("Finding deleted")
                setDeletingFinding(null)
                await reloadCaseSheet()
              } catch (e) {
                toast.error((e as Error)?.message || "Failed to delete finding")
              }
            }}
          />

          <TreatmentPlanningPanel
            open={planningOpen && Boolean(activeFinding)}
            onClose={() => setPlanningOpen(false)}
            finding={activeFinding ?? { id: 0, description: "" }}
            treatments={(function () {
              if (!activeFinding) return []
              // derive treatments for active finding
              const tooth = (caseSheet.teeth ?? []).find((t: any) => (t.findings ?? []).some((f: any) => f.id === activeFinding.id))
              const f = tooth?.findings?.find((x: any) => x.id === activeFinding.id)
              return (f?.treatments ?? []).map((tr: any) => ({ id: tr.id, procedureName: tr.procedureName, status: String(tr.status ?? "PENDING").toLowerCase(), cost: Number(tr.cost ?? 0), notes: tr.notes }))
            })()}
            patient={{ id: patient.id, name }}
            onAddTreatment={() => setTreatmentDialogOpen(true)}
            renderTreatmentActions={(t) => (
              <div className="flex items-center gap-2 text-xs">
                <button
                  className="inline-flex items-center rounded-md border px-2 py-1 text-[11px] hover:bg-muted"
                  onClick={() => setEditingTreatment({ id: t.id, procedureName: t.procedureName, cost: Number(t.cost ?? 0), notes: t.notes })}
                >
                  Edit
                </button>
                <button
                  className="inline-flex items-center rounded-md border border-destructive/40 text-destructive px-2 py-1 text-[11px] hover:bg-destructive/10"
                  onClick={() => setDeletingTreatment({ id: t.id })}
                >
                  Delete
                </button>
                <button
                  className={cn(
                    "inline-flex items-center rounded-md border px-2 py-1 text-[11px]",
                    t.status === "completed"
                      ? "border-amber-400 text-amber-700 hover:bg-amber-50"
                      : "border-emerald-400 text-emerald-700 hover:bg-emerald-50"
                  )}
                  onClick={async () => {
                    const target = t.status === "completed" ? "pending" : "completed"
                    try {
                      const res = await fetch(`/api/clinical/treatments/${t.id}`, {
                        method: "PATCH",
                        headers: { "Content-Type": "application/json" },
                        credentials: "include",
                        body: JSON.stringify({ status: target }),
                      })
                      if (!res.ok) throw new Error("Failed to update status")
                      toast.success(target === "completed" ? "Treatment marked as completed" : "Treatment set to pending")
                      await reloadCaseSheet()
                    } catch (e) {
                      toast.error((e as Error)?.message || "Failed to update status")
                    }
                  }}
                >
                  {t.status === "completed" ? "Mark pending" : "Mark complete"}
                </button>
              </div>
            )}
          />

          <Dialog open={treatmentDialogOpen} onOpenChange={setTreatmentDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Treatment</DialogTitle>
              </DialogHeader>
              <TreatmentForm
                loading={submitting}
                onSubmit={async (values) => {
                  if (!activeFinding) return
                  setSubmitting(true)
                  try {
                    const res = await fetch(`/api/clinical/findings/${activeFinding.id}/treatments`, {
                      method: "POST",
                      headers: { "Content-Type": "application/json" },
                      credentials: "include",
                      body: JSON.stringify({ procedureName: values.procedureName, cost: values.cost, notes: values.notes }),
                    })
                    if (!res.ok) throw new Error("Failed to save treatment")
                    toast.success("Treatment saved. Invoice updated.")
                    setTreatmentDialogOpen(false)
                    await reloadCaseSheet()
                  } catch (e) {
                    toast.error((e as Error)?.message || "Failed to save treatment")
                  } finally {
                    setSubmitting(false)
                  }
                }}
                onCancel={() => setTreatmentDialogOpen(false)}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={Boolean(editingTreatment)} onOpenChange={(open) => !open && setEditingTreatment(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Treatment</DialogTitle>
              </DialogHeader>
              <TreatmentForm
                defaultValues={editingTreatment ? { procedureName: editingTreatment.procedureName, cost: String(editingTreatment.cost), notes: editingTreatment.notes ?? "" } : undefined}
                loading={submitting}
                onSubmit={async (values) => {
                  if (!editingTreatment) return
                  setSubmitting(true)
                  try {
                    const res = await fetch(`/api/clinical/treatments/${editingTreatment.id}`, {
                      method: "PATCH",
                      headers: { "Content-Type": "application/json" },
                      credentials: "include",
                      body: JSON.stringify({ procedureName: values.procedureName, cost: values.cost, status: undefined }),
                    })
                    if (!res.ok) throw new Error("Failed to update treatment")
                    toast.success("Treatment updated")
                    setEditingTreatment(null)
                    await reloadCaseSheet()
                  } catch (e) {
                    toast.error((e as Error)?.message || "Failed to update treatment")
                  } finally {
                    setSubmitting(false)
                  }
                }}
                onCancel={() => setEditingTreatment(null)}
              />
            </DialogContent>
          </Dialog>

          <DeleteTreatmentConfirm
            open={Boolean(deletingTreatment)}
            onOpenChange={(open) => !open && setDeletingTreatment(null)}
            onConfirm={async () => {
              if (!deletingTreatment) return
              try {
                const res = await fetch(`/api/clinical/treatments/${deletingTreatment.id}`, { method: "DELETE", credentials: "include" })
                if (!res.ok) throw new Error("Failed to delete treatment")
                toast.success("Treatment deleted")
                setDeletingTreatment(null)
                await reloadCaseSheet()
              } catch (e) {
                toast.error((e as Error)?.message || "Failed to delete treatment")
              }
            }}
          />
        </div>
      ) : (
        <EmptyState title="No case sheet" description="Initialize a case sheet to manage clinical data." />
      )}

      {/* Edit Patient dialog */}
      <Dialog open={editOpen} onOpenChange={setEditOpen}>
        <DialogContent className="max-w-xl">
          <DialogHeader>
            <DialogTitle>Edit Patient</DialogTitle>
          </DialogHeader>
          <PatientForm
            patient={{
              firstName: patient.firstName,
              lastName: patient.lastName,
              phoneNumber: patient.phoneNumber ?? "",
              email: patient.email ?? "",
              address: patient.address ?? "",
              dateOfBirth: patient.dateOfBirth ? formatDateForInput(patient.dateOfBirth) : "",
              uid: patient.uid ?? "",
            }}
            loading={submitting}
            onSubmit={async (values) => {
              setSubmitting(true)
              try {
                const res = await fetch(`/api/patients/${patient.id}`, {
                  method: "PATCH",
                  headers: { "Content-Type": "application/json" },
                  credentials: "include",
                  body: JSON.stringify(values),
                })
                if (!res.ok) throw new Error("Failed to update patient")
                const json = await res.json()
                setPatient(json.patient)
                setEditOpen(false)
                toast.success("Patient updated")
              } catch (e) {
                toast.error((e as Error)?.message || "Failed to update patient")
              } finally {
                setSubmitting(false)
              }
            }}
            onCancel={() => setEditOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* NFC Assignment Dialog */}
      <Dialog open={nfcDialogOpen} onOpenChange={setNfcDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Assign NFC Tag to {patient.firstName} {patient.lastName}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {patient.uid && (
              <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg dark:bg-amber-900/20 dark:border-amber-800">
                <div className="flex items-center gap-2 text-amber-800 dark:text-amber-400">
                  <Nfc className="w-4 h-4" />
                  <span className="text-sm font-medium">Current NFC UID: <code className="font-mono">{patient.uid}</code></span>
                </div>
                <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
                  Scanning a new UID will replace the current assignment.
                </p>
              </div>
            )}
            <PatientNFC
              autoFocus
              onPatientFound={(foundPatientId) => {
                // If the scanned UID belongs to another patient, show warning
                if (foundPatientId !== patient.id) {
                  toast.error("This NFC tag is already assigned to another patient")
                } else {
                  toast.info("This NFC tag is already assigned to this patient")
                  setNfcDialogOpen(false)
                }
              }}
              onPatientNotFound={async (uid) => {
                // Assign the UID to current patient
                try {
                  const res = await fetch(`/api/patients/${patient.id}`, {
                    method: "PATCH",
                    headers: { "Content-Type": "application/json" },
                    credentials: "include",
                    body: JSON.stringify({
                      firstName: patient.firstName,
                      lastName: patient.lastName,
                      phoneNumber: patient.phoneNumber,
                      email: patient.email,
                      address: patient.address,
                      dateOfBirth: patient.dateOfBirth,
                      uid: uid,
                    }),
                  })
                  
                  if (!res.ok) throw new Error("Failed to assign NFC tag")
                  
                  const json = await res.json()
                  setPatient(json.patient)
                  setNfcDialogOpen(false)
                  toast.success(`NFC tag ${uid} successfully assigned to ${patient.firstName} ${patient.lastName}`)
                } catch (error) {
                  console.error("Failed to assign NFC tag:", error)
                  toast.error("Failed to assign NFC tag. Please try again.")
                }
              }}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
