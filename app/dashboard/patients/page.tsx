"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { PatientSearchForm, type SearchCriteria } from "@/components/patients/PatientSearchForm"
import { PatientTable, type Patient } from "@/components/patients/PatientTable"
import { LoadingSpinner } from "@/components/ui/LoadingSpinner"
import { EmptyState } from "@/components/ui/EmptyState"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { PatientCreateModal } from "@/components/patients/PatientCreateModal"
import { PatientNFC } from "@/components/patients/PatientNFC"

async function searchPatients(criteria: SearchCriteria): Promise<Patient[]> {
  const res = await fetch("/api/patients/search", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ searchTerm: criteria.searchTerm }),
  })
  if (!res.ok) return []
  const json = await res.json()
  return json.patients ?? []
}

export default function PatientsPage() {
  const router = useRouter()
  const [loading, setLoading] = React.useState(false)
  const [patients, setPatients] = React.useState<Patient[] | null>(null)
  const [createOpen, setCreateOpen] = React.useState(false)
  const [prefilledUid, setPrefilledUid] = React.useState<string | null>(null)
  
  const loadInitialPatients = React.useCallback(async () => {
    setLoading(true)
    try {
      const res = await fetch('/api/patients/search?limit=50', {
        method: 'GET',
        headers: { 'Accept': 'application/json' },
      })
      if (!res.ok) return
      const json = await res.json()
      setPatients(json.patients ?? [])
    } finally {
      setLoading(false)
    }
  }, [])
  
  // Open create modal if query contains ?create=1 or ?add=true
  React.useEffect(() => {
    try {
      const params = new URLSearchParams(window.location.search)
      const shouldOpen = params.get('create') === '1' || params.get('add') === 'true'
      const uidParam = params.get('uid')
      
      if (shouldOpen) {
        setCreateOpen(true)
        if (uidParam) {
          setPrefilledUid(uidParam)
        }
      }
    } catch {}
  }, [])

  // Initial load: fetch recent patients so the table is visible without a search
  React.useEffect(() => {
    loadInitialPatients()
  }, [])

  const handleSearch = async (criteria: SearchCriteria) => {
    setLoading(true)
    try {
      const result = await searchPatients(criteria)
      setPatients(result)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-center">
        <div className="w-full max-w-2xl">
          <PatientNFC 
            autoFocus 
            onPatientFound={(patientId) => {
              // Navigate to patient details page
              router.push(`/dashboard/patients/${patientId}`)
            }}
            onPatientNotFound={(uid) => {
              // Open create modal with pre-filled UID
              setPrefilledUid(uid)
              setCreateOpen(true)
            }}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-start justify-between gap-4">
            <div>
              <CardTitle>Patients</CardTitle>
              <CardDescription>Search and manage patient records.</CardDescription>
            </div>
            <Button onClick={() => setCreateOpen(true)}>Create Patient</Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <PatientSearchForm
              onSearch={handleSearch}
              onCreateNew={() => setCreateOpen(true)}
              loading={loading}
              showSearchButton={false}
              showCreateButton={false}
              showResetButton
              onResetToDefaultList={loadInitialPatients}
              autoFocus={false}
            />

            {loading && (
              <div className="flex justify-center py-10">
                <LoadingSpinner size="lg" label="Loading patients" />
              </div>
            )}

            {!loading && patients && patients.length === 0 && (
              <EmptyState title="No patients found" description="Adjust your search or create a new patient." />
            )}

            {!loading && patients && patients.length > 0 && (
              <PatientTable patients={patients} />
            )}
          </div>
        </CardContent>
      </Card>

      <PatientCreateModal 
        open={createOpen} 
        onOpenChange={(open) => {
          setCreateOpen(open)
          if (!open) {
            setPrefilledUid(null)
            // Clear URL parameters when modal closes
            const url = new URL(window.location.href)
            url.searchParams.delete('add')
            url.searchParams.delete('uid')
            window.history.replaceState({}, '', url.toString())
          }
        }}
        prefilledUid={prefilledUid}
      />
    </div>
  )
}
