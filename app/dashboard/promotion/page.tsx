"use client"

import * as React from "react"
import { Plus, Trash2, <PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingSpinner } from "@/components/ui/LoadingSpinner"
import { EmptyState } from "@/components/ui/EmptyState"
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ConfirmDialog } from "@/components/ui/ConfirmDialog"

type PromotionType = "BANNER" | "PREVIOUS_CASES"

type Promotion = {
  id: number
  type: PromotionType
  title: string | null
  fileUrl: string | null
  fileType: string | null
  createdAt: string
  userId: number | null
  user: {
    id: number
    firstName: string
    lastName: string
  } | null
}

type Dentist = {
  id: number
  firstName: string
  lastName: string
  email?: string | null
  phoneNumber?: string | null
  username: string
  isActive: boolean
}

function usePromotions(type: PromotionType) {
  const [items, setItems] = React.useState<Promotion[]>([])
  const [loading, setLoading] = React.useState<boolean>(false)

  const load = React.useCallback(async () => {
    setLoading(true)
    try {
      const res = await fetch(`/api/promotions?type=${type}`)
      if (!res.ok) { setItems([]); return }
      const data = await res.json()
      setItems(Array.isArray(data.promotions) ? data.promotions : [])
    } finally {
      setLoading(false)
    }
  }, [type])

  React.useEffect(() => { load() }, [load])

  return { items, setItems, loading, reload: load }
}

function useDentists() {
  const [dentists, setDentists] = React.useState<Dentist[]>([])
  const [loading, setLoading] = React.useState<boolean>(false)

  const load = React.useCallback(async () => {
    setLoading(true)
    try {
      const res = await fetch('/api/users/dentists')
      if (!res.ok) { setDentists([]); return }
      const data = await res.json()
      if (data.success && Array.isArray(data.data)) {
        setDentists(data.data)
      }
    } finally {
      setLoading(false)
    }
  }, [])

  React.useEffect(() => { load() }, [load])

  return { dentists, loading }
}

function PromotionList({ type }: { type: PromotionType }) {
  const { items, setItems, loading, reload } = usePromotions(type)
  const { dentists, loading: dentistsLoading } = useDentists()
  const [dialogOpen, setDialogOpen] = React.useState(false)
  const [editing, setEditing] = React.useState<Promotion | null>(null)
  const [form, setForm] = React.useState<{ title: string; file: File | null; fileUrl: string; fileType: string; userId: string }>({ title: "", file: null, fileUrl: "", fileType: "", userId: "" })
  const [confirmOpen, setConfirmOpen] = React.useState(false)
  const [pendingDelete, setPendingDelete] = React.useState<Promotion | null>(null)

  const update = async (id: number, data: Partial<Pick<Promotion, "title" | "fileUrl" | "fileType">>) => {
    const res = await fetch(`/api/promotions/${id}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    })
    if (res.ok) await reload()
  }

  const upsert = async () => {
    let payloadFileUrl = form.fileUrl || null
    let payloadFileType = form.fileType || null

    if (form.file) {
      const fd = new FormData()
      fd.append("file", form.file)
      const uploadRes = await fetch(`/api/uploads/promotion`, { method: "POST", body: fd })
      if (!uploadRes.ok) return
      const uploadJson = await uploadRes.json()
      payloadFileUrl = uploadJson?.data?.fileUrl ?? null
      payloadFileType = uploadJson?.data?.fileType ?? null
    }

    if (editing) {
      const res = await fetch(`/api/promotions/${editing.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: form.title || null,
          fileUrl: payloadFileUrl,
          fileType: payloadFileType,
          userId: type === "PREVIOUS_CASES" ? (form.userId || null) : null,
        }),
      })
      if (res.ok) { setDialogOpen(false); setEditing(null); setForm({ title: "", file: null, fileUrl: "", fileType: "", userId: "" }); await reload() }
      return
    }

    const res = await fetch(`/api/promotions`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ 
        type, 
        title: form.title || null, 
        fileUrl: payloadFileUrl, 
        fileType: payloadFileType, 
        userId: type === "PREVIOUS_CASES" ? (form.userId || null) : null 
      }),
    })
    if (res.ok) { setDialogOpen(false); setForm({ title: "", file: null, fileUrl: "", fileType: "", userId: "" }); await reload() }
  }

  const removeConfirmed = async () => {
    if (!pendingDelete) return
    const res = await fetch(`/api/promotions/${pendingDelete.id}`, { method: "DELETE" })
    if (res.ok) setItems((prev) => prev.filter((x) => x.id !== pendingDelete.id))
    setConfirmOpen(false)
    setPendingDelete(null)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between gap-4">
          <div>
            <CardTitle>{type === "BANNER" ? "Banner Promotions" : "Previous Cases"}</CardTitle>
            <CardDescription>
              {type === "BANNER" ? "Manage banner promotions." : "Manage previous cases promotions."}
            </CardDescription>
          </div>
          <div>
            <Button onClick={() => { setEditing(null); setForm({ title: "", fileUrl: "", fileType: "", file: null, userId: "" }); setDialogOpen(true) }}>
              <Plus className="h-4 w-4 mr-2" /> Add
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading && (
          <div className="flex justify-center py-10">
            <LoadingSpinner size="lg" label="Loading" />
          </div>
        )}

        {!loading && items.length === 0 && (
          <div className="pt-6">
            <EmptyState title="No items" description="Add your first promotion." />
          </div>
        )}

        {!loading && items.length > 0 && (
          <div className="mt-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Preview</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>Dentist</TableHead>
                  <TableHead>File Type</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((p) => (
                  <TableRow key={p.id}>
                    <TableCell>
                      {p.fileUrl && (p.fileType?.startsWith("image/") ?? false) ? (
                        <img
                          src={p.fileUrl}
                          alt={p.title ?? "Preview"}
                          className="h-12 w-16 object-cover rounded border"
                        />
                      ) : (
                        "—"
                      )}
                    </TableCell>
                    <TableCell className="max-w-[280px] truncate" title={p.title ?? ""}>{p.title ?? "—"}</TableCell>
                    <TableCell className="max-w-[200px] truncate">
                      {p.user ? `${p.user.firstName} ${p.user.lastName}` : "—"}
                    </TableCell>
                    <TableCell>{p.fileType ?? "—"}</TableCell>
                    <TableCell className="text-right space-x-2">
                      <Button size="sm" variant="outline" onClick={() => { setEditing(p); setForm({ title: p.title ?? "", fileUrl: p.fileUrl ?? "", fileType: p.fileType ?? "", file: null, userId: p.userId ? String(p.userId) : "" }); setDialogOpen(true) }}>
                        <Pencil className="h-4 w-4 mr-1" /> Edit
                      </Button>
                      <Button size="sm" variant="destructive" onClick={() => { setPendingDelete(p); setConfirmOpen(true) }}>
                        <Trash2 className="h-4 w-4 mr-1" /> Delete
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editing ? "Edit Promotion" : "Add Promotion"}</DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              <Input placeholder="URL (Optional)" value={form.title} onChange={(e) => setForm((f) => ({ ...f, title: e.target.value }))} />
              {type === "PREVIOUS_CASES" && (
                <Select value={form.userId || "none"} onValueChange={(value) => setForm((f) => ({ ...f, userId: value === "none" ? "" : value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select dentist (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No dentist</SelectItem>
                    {dentists.map((dentist) => (
                      <SelectItem key={dentist.id} value={String(dentist.id)}>
                        {dentist.firstName} {dentist.lastName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              <Input type="file" accept="image/*,video/*" onChange={(e) => setForm((f) => ({ ...f, file: e.target.files?.[0] ?? null }))} />
              {form.fileUrl ? (
                <Input placeholder="Uploaded URL" value={form.fileUrl} readOnly />
              ) : null}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
              <Button onClick={upsert}>{editing ? "Save" : "Create"}</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <ConfirmDialog
          open={confirmOpen}
          onOpenChange={setConfirmOpen}
          title="Delete item?"
          description="This will permanently delete the promotion."
          confirmText="Delete"
          destructive
          onConfirm={removeConfirmed}
          onCancel={() => { setConfirmOpen(false); setPendingDelete(null) }}
        />
      </CardContent>
    </Card>
  )
}

export default function PromotionPage() {
  const [tab, setTab] = React.useState<PromotionType>("BANNER")
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Promotion</h1>
        <p className="text-muted-foreground">Manage promotional content.</p>
      </div>

      <Tabs value={tab} onValueChange={(v) => setTab(v as PromotionType)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="BANNER">Banner</TabsTrigger>
          <TabsTrigger value="PREVIOUS_CASES">Previous Cases</TabsTrigger>
        </TabsList>

        <TabsContent value="BANNER" className="space-y-6">
          <PromotionList type="BANNER" />
        </TabsContent>

        <TabsContent value="PREVIOUS_CASES" className="space-y-6">
          <PromotionList type="PREVIOUS_CASES" />
        </TabsContent>
      </Tabs>
    </div>
  )
}


