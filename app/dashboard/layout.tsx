"use client";

import React from "react";
import { AuthGuard } from "@/components/auth/AuthGuard";
import { DashboardLayoutClient } from "@/components/layout/DashboardLayoutClient";

/**
 * Dashboard Layout with Authentication and Subscription Guards
 * 
 * This layout wraps all dashboard pages with authentication protection
 * and subscription validation. It ensures that only authenticated users
 * with valid subscriptions can access dashboard routes.
 * 
 * Requirements: 3.4, 3.6, 8.1, 8.2, Subscription Management
 */
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard
      requireAuth={true}
      allowedUserTypes={["ADMIN", "DENTIST", "RECEPTIONIST"]}
      requireTenant={true}
      redirectTo="/auth/login"
      loadingComponent={<DashboardLoadingSkeleton />}
      unauthorizedComponent={<UnauthorizedAccess />}
    >
      <DashboardLayoutClient>
        {children}
      </DashboardLayoutClient>
    </AuthGuard>
  );
}



/**
 * Loading Skeleton for Dashboard
 */
function DashboardLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-background">
      <div className="border-b border-border/40 bg-background/95">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-6 w-48 bg-muted animate-pulse rounded" />
            </div>
            <div className="flex items-center space-x-4">
              <div className="h-8 w-16 bg-muted animate-pulse rounded" />
              <div className="h-8 w-16 bg-muted animate-pulse rounded" />
            </div>
          </div>
        </div>
      </div>
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          <div className="h-8 w-64 bg-muted animate-pulse rounded" />
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-muted animate-pulse rounded-lg" />
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}

/**
 * Unauthorized Access Component
 */
function UnauthorizedAccess() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center space-y-4">
        <h1 className="text-2xl font-bold text-destructive">Access Denied</h1>
        <p className="text-muted-foreground">
          You don&apos;t have permission to access this page.
        </p>
        <a
          href="/auth/login"
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        >
          Go to Login
        </a>
      </div>
    </div>
  );
}