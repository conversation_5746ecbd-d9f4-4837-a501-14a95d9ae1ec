
import { notFound, redirect } from "next/navigation";
import prisma from "@/lib/prisma";
import { AuthService } from "@/lib/auth/services/auth-service";
import PrintableInvoice, { type PrintableInvoiceData } from "@/components/billing/PrintableInvoice";
import AutoPrint from "@/components/print/AutoPrint";
import { PathSanitizationUtils } from "@/lib/auth/utils";

export const dynamic = "force-dynamic";

export default async function PrintInvoicePage({ params, searchParams }: { params: Promise<{ id: number }>; searchParams: Promise<{ [k: string]: string | string[] | undefined }> }) {
  const { id } = await params;
  const resolvedSearchParams = await searchParams;
  // Server-side auth to enforce access and extract tenant
  const sessionValidation = await AuthService.validateSession();
  if (!sessionValidation.isValid || !sessionValidation.session) {
    redirect("/auth/login?redirect=" + encodeURIComponent(`/print/invoice/${id}`));
  }
  const { tenantId } = sessionValidation.session!;

  const numericId = Number(id);
  if (!Number.isInteger(numericId) || numericId <= 0) notFound();

  // Multitenant-safe fetch: restrict to current tenant
  const invoice = await prisma.invoice.findFirst({
    where: { id: numericId, tenantId },
    include: {
      patient: true,
      treatments: true,
      payments: true,
      tenant: true,
    },
  });

  if (!invoice) notFound();

  const logoPath = invoice.tenant?.logoImage

  const data: PrintableInvoiceData = {
    id: invoice.id,
    serial: invoice.serial,
    date: invoice.invoiceDate,
    status: String(invoice.status).toLowerCase() as "draft" | "sent" | "paid" | "overdue",
    patient: { id: invoice.patientId, name: `${invoice.patient.firstName ?? ''} ${invoice.patient.lastName ?? ''}`.trim() || '—' },
    clinic: {
      name: invoice.tenant?.name,
      address: invoice.tenant?.address ?? undefined,
      phone: invoice.tenant?.phoneNumber ?? undefined,
      email: undefined,
      logoUrl: logoPath ?? '',
    },
    treatments: (invoice.treatments ?? []).map((t) => ({ id: t.id, procedureName: t.procedureName, status: t.status === 'COMPLETED' ? 'completed' as const : 'pending' as const, cost: Number(t.cost ?? 0) })),
    payments: (invoice.payments ?? []).map((p) => ({ id: p.id, amount: Number(p.amount ?? 0), date: p.paymentDate, method: String(p.paymentMethod ?? '').toLowerCase() })),
    totalAmount: Number(invoice.totalAmount ?? 0),
    amountPaid: Number(invoice.amountPaid ?? 0),
    balanceDue: Number(invoice.balanceDue ?? 0),
  };

  const shouldAutoPrint = typeof resolvedSearchParams?.auto !== "undefined";

  return (
    <div className="min-h-screen bg-background print:bg-white print:text-black">
      {shouldAutoPrint ? <AutoPrint /> : null}
      <div className="mx-auto w-full max-w-[900px] p-4 print:p-0">
        <PrintableInvoice invoice={data} />
      </div>
    </div>
  );
}

