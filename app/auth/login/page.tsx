"use client";

import React, { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { LoginForm } from "@/components/auth/LoginForm";
import { type LoginData, type LoginResponse } from "@/lib/auth/schemas";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";

/**
 * Login Page Content Component that uses search params
 * This component needs to be wrapped in Suspense to use useSearchParams
 */
function LoginPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Handle success messages from URL params
  useEffect(() => {
    const message = searchParams.get("message");
    if (message === "registration-success") {
      setSuccessMessage("Registration successful! You can now log in with your credentials.");
    } else if (message === "logout-success") {
      setSuccessMessage("You have been successfully logged out.");
    }
  }, [searchParams]);

  const handleSubmit = async (data: LoginData & { rememberMe?: boolean }): Promise<LoginResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: data.username,
          password: data.password,
          rememberMe: data.rememberMe || false,
        }),
      });

      const result: LoginResponse = await response.json();

      if (!response.ok) {
        const errorMessage = result.message || "Login failed. Please check your credentials.";
        setError(errorMessage);
        
        // Return the error response for the LoginForm to handle
        return {
          success: false,
          message: errorMessage,
        };
      }

      // Login successful - redirect to dashboard or specified URL
      const redirectUrl = result.redirectUrl || "/dashboard";
      
      // Clear any success messages
      setSuccessMessage(null);
      
      // Redirect to the appropriate page
      router.push(redirectUrl);

      return result;

    } catch (error) {
      console.error("Login error:", error);
      const errorMessage = "Network error. Please check your connection and try again.";
      setError(errorMessage);
      
      return {
        success: false,
        message: errorMessage,
      };
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Navigation Header */}
      <nav className="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Home</span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">Need an account?</span>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/auth/register">Register</Link>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8">
          

          {/* Success Message */}
          {successMessage && (
            <Alert className="border-green-200 bg-green-50 text-green-800">
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Login Form */}
          <Card>
            <CardContent className="p-6">
              <LoginForm
                onSubmit={handleSubmit}
                isLoading={isLoading}
                error={error || undefined}
                enableRememberMe={true}
              />
            </CardContent>
          </Card>

          {/* Additional Links */}
          <div className="text-center space-y-4">
            <div className="text-sm">
              <Link href="/auth/forgot-password" className="text-primary hover:underline">
                Forgot your password?
              </Link>
            </div>
            
            <div className="text-sm text-muted-foreground">
              Don&apos;t have an account?{" "}
              <Link href="/auth/register" className="text-primary hover:underline font-medium">
                Register your clinic
              </Link>
            </div>
          </div>

          {/* Help Section */}
          <div className="bg-muted/30 rounded-lg p-4 text-center">
            <h3 className="text-sm font-medium mb-2">Need help?</h3>
            <p className="text-xs text-muted-foreground mb-3">
              Contact our support team for assistance with your account
            </p>
            <Button variant="outline" size="sm" asChild>
              <Link href="/support">Contact Support</Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}

/**
 * Login Page Component with Suspense boundary
 * Route: /auth/login
 * 
 * Requirements: 3.1, 3.3 - Display login form and handle successful authentication
 */
export default function LoginPage() {
  return (
    <div className="min-h-screen bg-background">
      <Suspense
        fallback={
          <div className="min-h-screen bg-background flex items-center justify-center">
            <LoadingSpinner />
          </div>
        }
      >
        <LoginPageContent />
      </Suspense>
    </div>
  );
}
