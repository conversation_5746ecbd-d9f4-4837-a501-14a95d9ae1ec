// Modern, polished landing page for Dental and Diagnosis (DnD)
// - Server component renders different actions based on authentication state
// - Contemporary visual design with subtle gradients, glass surfaces, and icons
// Based on Django-to-Prisma migration requirements for dental practice software

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { AuthService } from "@/lib/auth/services/auth-service";
import { LogoutButton } from "@/components/auth/LogoutButton";
import { Users, Calendar, ClipboardList, Receipt, Layers, Shield } from "lucide-react";

export default async function Home() {
  const validation = await AuthService.validateSession();
  const isAuthenticated = validation.isValid && !!validation.session;
  const username = validation.session?.username;

  return (
    <div className="min-h-screen bg-background relative">
      {/* Background aesthetics */}
      <div className="pointer-events-none absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 left-1/2 h-[32rem] w-[32rem] -translate-x-1/2 rounded-full bg-primary/10 blur-3xl" />
        <div className="absolute bottom-0 right-0 h-[24rem] w-[24rem] translate-x-1/3 translate-y-1/3 rounded-full bg-accent/10 blur-3xl" />
      </div>

      {/* Navigation */}
      <nav className="sticky top-0 z-20 border-b border-border/40 bg-background/80 backdrop-blur-lg">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="size-6 rounded-md bg-primary/15 ring-1 ring-primary/30 flex items-center justify-center">
                <div className="size-3 rounded-sm bg-primary/50" />
              </div>
              <span className="text-sm font-semibold tracking-wide text-muted-foreground">Dental & Diagnosis</span>
            </div>

            <div className="flex items-center gap-2">
              {isAuthenticated ? (
                <>
                  <Button size="sm" asChild>
                    <Link href="/dashboard">Dashboard</Link>
                  </Button>
                  <LogoutButton size="sm" variant="ghost" />
                </>
              ) : (
                <>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href="/auth/login">Login</Link>
                  </Button>
                  <Button size="sm" asChild>
                    <Link href="/auth/register">Register</Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-28">
        <div className="mx-auto max-w-5xl text-center">
          <Badge variant="secondary" className="mb-5 inline-flex items-center gap-2">
            <span className="inline-block size-2 rounded-full bg-primary" />
            Modern dental practice management
          </Badge>

          <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight">
            <span className="bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-transparent">
              Streamline care. Elevate outcomes.
            </span>
          </h1>

          <p className="mt-6 text-balance text-lg sm:text-xl leading-8 text-muted-foreground max-w-3xl mx-auto">
            DnD unifies patient records, scheduling, treatment planning, and billing — all secured and optimized for multi-clinic growth.
          </p>

          <div className="mt-10 flex flex-col sm:flex-row gap-3 justify-center">
            {isAuthenticated ? (
              <>
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link href="/dashboard">Go to Dashboard</Link>
                </Button>
              </>
            ) : (
              <>
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link href="/auth/register">Get Started</Link>
                </Button>
                <Button variant="outline" size="lg" className="text-base px-7 py-5" asChild>
                  <Link href="/auth/login">Login</Link>
                </Button>
              </>
            )}
          </div>

          <p className="mt-6 text-sm text-muted-foreground">
            HIPAA compliant • End‑to‑end encryption • Role‑based access
          </p>
          {isAuthenticated && (
            <p className="mt-2 text-xs text-muted-foreground">Signed in{username ? ` as ${username}` : ""}</p>
          )}
        </div>
      </section>

      <Separator className="container mx-auto" />

      {/* Features Section */}
      <section id="features" className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="mx-auto max-w-2xl text-center mb-14">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Everything your practice needs</h2>
          <p className="mt-4 text-lg leading-8 text-muted-foreground">
            Purpose‑built features that help teams deliver excellent patient care.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Patient Management</CardTitle>
              <CardDescription className="text-base">
                Unified records, medical histories, and charts with secure access controls.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Calendar className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Appointment Scheduling</CardTitle>
              <CardDescription className="text-base">
                Smart calendars with reminders, conflict resolution, and multi‑provider views.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <ClipboardList className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Treatment Planning</CardTitle>
              <CardDescription className="text-base">
                Digital plans with cost estimates, progress tracking, and patient communication.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Receipt className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Billing & Insurance</CardTitle>
              <CardDescription className="text-base">
                Automated billing, claims, and financial reporting across tenants.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Layers className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Multi‑Tenant Architecture</CardTitle>
              <CardDescription className="text-base">
                Tenant isolation, centralized controls, and scalable performance.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Advanced Security</CardTitle>
              <CardDescription className="text-base">
                Encryption, audit trails, and role‑based access — HIPAA compliant.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      <Separator className="container mx-auto" />

      {/* Call to Action Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <Card className="max-w-4xl mx-auto border-border/50">
          <CardContent className="p-10 sm:p-12 text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Ready to modernize your practice?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join clinics improving outcomes with efficient workflows and secure data.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center mb-6">
              {isAuthenticated ? (
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link href="/dashboard">Open Dashboard</Link>
                </Button>
              ) : (
                <>
                  <Button size="lg" className="text-base px-7 py-5" asChild>
                    <Link href="/auth/register">Register Now</Link>
                  </Button>
                  <Button variant="outline" size="lg" className="text-base px-7 py-5" asChild>
                    <Link href="/auth/login">Login</Link>
                  </Button>
                </>
              )}
            </div>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
              <span>✓ HIPAA compliant</span>
              <span>✓ Secure & reliable</span>
              <span>✓ Professional support</span>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t border-border/40 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-3 mb-4 md:mb-0">
              <div className="size-6 rounded-md bg-primary/15 ring-1 ring-primary/30 flex items-center justify-center">
                <div className="size-3 rounded-sm bg-primary/50" />
              </div>
              <h3 className="text-sm font-semibold tracking-tight">Dental & Diagnosis</h3>
            </div>
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              <a href="#" className="hover:text-foreground transition-colors">Privacy</a>
              <a href="#" className="hover:text-foreground transition-colors">Terms</a>
              <a href="#" className="hover:text-foreground transition-colors">Support</a>
              <a href="#" className="hover:text-foreground transition-colors">Contact</a>
            </div>
          </div>
          <Separator className="my-8" />
          <div className="text-center text-sm text-muted-foreground">
            © 2025 Dental and Diagnosis. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
