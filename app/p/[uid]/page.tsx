"use client";

import * as React from "react";
import { useParams } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CalendarDays, Phone, Activity, Users } from "lucide-react";
import { formatDate } from "@/lib/date-utils";
import { CarouselContent, CarouselItem } from "@/components/ui/carousel";

// Import extracted components
import { PatientData, ApiResponse } from "@/components/patient-page/types";
import { LoadingCard } from "@/components/patient-page/LoadingCard";
import { ClinicInformation } from "@/components/patient-page/ClinicInformation";
import { SerenitySection } from "@/components/patient-page/SerenitySection";
import { AutoCarousel } from "@/components/patient-page/AutoCarousel";
import { DentistAccordionItem } from "@/components/patient-page/DentistAccordionItem";
import { ClinicalSummary } from "@/components/patient-page/ClinicalSummary";
import { DetailedClinicalSummary } from "@/components/patient-page/DetailedClinicalSummary";
import { ClinicFooter } from "@/components/patient-page/ClinicFooter";




export default function PatientPage() {
  const params = useParams();
  const uid = params.uid as string;
  const [data, setData] = React.useState<PatientData | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (!uid) return;

    const fetchPatientData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/p/${uid}`);
        const result: ApiResponse = await response.json();

        if (!result.success) {
          setError(result.message || "Failed to fetch patient data");
          return;
        }

        setData(result.data!);
      } catch (err) {
        setError("Failed to load patient information");
      } finally {
        setLoading(false);
      }
    };

    fetchPatientData();
  }, [uid]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            <LoadingCard />
            <LoadingCard />
            <LoadingCard />
          </div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="max-w-md mx-auto">
          <div className="p-8 text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2 font-serif">
              Patient Not Found
            </h2>
            <p className="text-gray-600">
              {error || "The requested patient information could not be found."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  const fullName = `${data.patient.firstName} ${data.patient.lastName}`;
  const age = data.patient.dateOfBirth
    ? Math.floor(
        (Date.now() - new Date(data.patient.dateOfBirth).getTime()) /
          (365.25 * 24 * 60 * 60 * 1000)
      )
    : null;

  return (
    <div className="min-h-screen bg-background font-serif smooth-scroll">
      {/* Patient header moved below into Patient Information section */}

      {/* Full-bleed Clinic Hero */}
      <div className="animate-fade-in">
        <ClinicInformation clinic={data.clinic} />
      </div>
      <div className="animate-fade-in-delayed">
        <SerenitySection clinicName={data.clinic.name} />
      </div>

      <div className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-10 md:space-y-14">
          {/* Promotional Banners */}
          {(() => {
            const banners = (data.promotions || []).filter(
              (p) =>
                p.type === "BANNER" &&
                p.fileUrl &&
                (p.fileType?.startsWith("image/") ||
                  p.fileType?.startsWith("video/"))
            );
            const cases = (data.promotions || []).filter(
              (p) =>
                p.type === "PREVIOUS_CASES" &&
                p.fileUrl &&
                (p.fileType?.startsWith("image/") ||
                  p.fileType?.startsWith("video/"))
            );
            return (
              <>
                {banners.length > 0 && (
                  <div className="my-6 animate-slide-up">
                    <AutoCarousel className="w-full">
                      <CarouselContent>
                        {banners.map((item) => (
                          <CarouselItem key={item.id}>
                            <div className="relative w-full h-40 sm:h-52 md:h-64 lg:h-72 overflow-hidden rounded-xl border border-border/20 shadow-sm card-hover">
                              {item.title ? (
                                <a
                                  href={item.title}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="block w-full h-full"
                                >
                                  {item.fileType?.startsWith("image/") ? (
                                    <img
                                      src={item.fileUrl!}
                                      alt="Banner"
                                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                    />
                                  ) : (
                                    <video
                                      src={item.fileUrl!}
                                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                      autoPlay
                                      muted
                                      loop
                                      playsInline
                                    />
                                  )}
                                </a>
                              ) : (
                                <>
                                  {item.fileType?.startsWith("image/") ? (
                                    <img
                                      src={item.fileUrl!}
                                      alt="Banner"
                                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                    />
                                  ) : (
                                    <video
                                      src={item.fileUrl!}
                                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                      autoPlay
                                      muted
                                      loop
                                      playsInline
                                    />
                                  )}
                                </>
                              )}
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                    </AutoCarousel>
                  </div>
                )}
                {cases.length > 0 && (
                  <div className="my-6 animate-slide-up-delayed">
                    <AutoCarousel className="w-full">
                      <CarouselContent>
                        {cases.map((item) => (
                          <CarouselItem key={item.id}>
                            <div className="relative w-full h-40 sm:h-52 md:h-64 lg:h-72 overflow-hidden rounded-xl border border-border/20 shadow-sm card-hover">
                              {item.title ? (
                                <a
                                  href={item.title}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="block w-full h-full"
                                >
                                  {item.fileType?.startsWith("image/") ? (
                                    <img
                                      src={item.fileUrl!}
                                      alt="Previous Case"
                                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                    />
                                  ) : (
                                    <video
                                      src={item.fileUrl!}
                                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                      autoPlay
                                      muted
                                      loop
                                      playsInline
                                    />
                                  )}
                                </a>
                              ) : (
                                <>
                                  {item.fileType?.startsWith("image/") ? (
                                    <img
                                      src={item.fileUrl!}
                                      alt="Previous Case"
                                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                    />
                                  ) : (
                                    <video
                                      src={item.fileUrl!}
                                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                      autoPlay
                                      muted
                                      loop
                                      playsInline
                                    />
                                  )}
                                </>
                              )}
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                    </AutoCarousel>
                  </div>
                )}
              </>
            );
          })()}

          <Separator className="border-border/40 my-8 md:my-12 bg-gradient-to-b from-border/40 to-border/0" />

          {/* Dentists Section */}
          {data.dentists.length > 0 && (
            <section id="dentists" className="my-12 md:my-16 animate-slide-up">
              <h3 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center gap-2 tracking-tight font-serif gradient-text">
                <Users className="w-6 h-6" />
                Our Dental Team
              </h3>
              <div className="border-t border-border/40 bg-gradient-to-b from-card to-card/50 rounded-t-lg">
                <Accordion type="single" collapsible className="w-full">
                  {data.dentists.map((dentist, index) => (
                    <div
                      key={dentist.id}
                      className={index % 2 === 0 ? "animate-fade-in" : "animate-fade-in-delayed"}
                    >
                      <DentistAccordionItem
                        dentist={dentist}
                        previousCases={data.promotions}
                      />
                    </div>
                  ))}
                </Accordion>
              </div>
            </section>
          )}
          <Separator className="border-border/40 my-8 md:my-12 bg-gradient-to-b from-border/40 to-border/0" />

          {/* Patient Information */}
          <section id="patient-info" className="my-12 md:my-16 animate-scale-in">
            <div className="flex items-center gap-4 p-6 bg-gradient-to-r from-card to-card/80 rounded-xl border border-border/20 shadow-sm card-hover">
              <Avatar className="w-12 h-12 sm:w-14 sm:h-14 glow-effect">
                <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-600 text-white text-lg sm:text-xl font-bold">
                  {data.patient.firstName.charAt(0)}
                  {data.patient.lastName.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h2 className="text-xl sm:text-2xl font-semibold text-gray-900 truncate tracking-tight gradient-text">
                  {fullName}
                </h2>
                <div className="flex flex-wrap gap-3 sm:gap-4 text-sm text-gray-600 mt-2">
                  <div className="flex items-center gap-1.5">
                    <Badge variant="outline" className="text-xs hover:bg-primary/10 transition-colors">
                      UID: {data.patient.uid}
                    </Badge>
                  </div>
                  {age && (
                    <div className="flex items-center gap-1.5">
                      <CalendarDays className="w-3.5 h-3.5 text-primary" />
                      <span>{age} years old</span>
                    </div>
                  )}
                  {data.patient.phoneNumber && (
                    <div className="flex items-center gap-1.5">
                      <Phone className="w-3.5 h-3.5 text-primary" />
                      <a
                        href={`tel:${data.patient.phoneNumber}`}
                        className="underline-offset-4 hover:underline hover:text-primary transition-colors"
                      >
                        {data.patient.phoneNumber}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </section>

          {/* Clinical History Section */}
          <section id="clinical-summary" className="my-12 md:my-16 animate-slide-up">
            <h3 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center gap-2 tracking-tight font-serif gradient-text">
              <Activity className="w-6 h-6" />
              Clinical History Summary
            </h3>
            <div className="card-hover">
              <ClinicalSummary data={data.clinicalSummary} />
            </div>
          </section>

          {/* Detailed Clinical Summary with Tooth Visualization */}
          <section id="clinical-details" className="my-12 md:my-16 animate-slide-up-delayed">
            <div className="card-hover">
              <DetailedClinicalSummary data={data} />
            </div>
          </section>
        </div>
      </div>
      <div className="animate-fade-in-slow">
        <ClinicFooter clinic={data.clinic} />
      </div>
    </div>
  );
}
