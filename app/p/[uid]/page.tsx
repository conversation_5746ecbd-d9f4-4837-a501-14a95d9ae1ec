"use client";

import * as React from "react";
import { useParams } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Calendar,
  Phone as PhoneIcon,
  Activity,
  Users
} from "@phosphor-icons/react";
import { formatDate } from "@/lib/date-utils";
import { CarouselContent, CarouselItem } from "@/components/ui/carousel";

// Import extracted components
import { PatientData, ApiResponse } from "@/components/patient-page/types";
import { LoadingCard } from "@/components/patient-page/LoadingCard";
import { ClinicInformation } from "@/components/patient-page/ClinicInformation";
import { SerenitySection } from "@/components/patient-page/SerenitySection";
import { AutoCarousel } from "@/components/patient-page/AutoCarousel";
import { DentistAccordionItem } from "@/components/patient-page/DentistAccordionItem";
import { ClinicalSummary } from "@/components/patient-page/ClinicalSummary";
import { DetailedClinicalSummary } from "@/components/patient-page/DetailedClinicalSummary";
import { ClinicFooter } from "@/components/patient-page/ClinicFooter";




export default function PatientPage() {
  const params = useParams();
  const uid = params.uid as string;
  const [data, setData] = React.useState<PatientData | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (!uid) return;

    const fetchPatientData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/p/${uid}`);
        const result: ApiResponse = await response.json();

        if (!result.success) {
          setError(result.message || "Failed to fetch patient data");
          return;
        }

        setData(result.data!);
      } catch (err) {
        setError("Failed to load patient information");
      } finally {
        setLoading(false);
      }
    };

    fetchPatientData();
  }, [uid]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            <LoadingCard />
            <LoadingCard />
            <LoadingCard />
          </div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="max-w-md mx-auto">
          <div className="p-8 text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2 font-serif">
              Patient Not Found
            </h2>
            <p className="text-gray-600">
              {error || "The requested patient information could not be found."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  const fullName = `${data.patient.firstName} ${data.patient.lastName}`;
  const age = data.patient.dateOfBirth
    ? Math.floor(
        (Date.now() - new Date(data.patient.dateOfBirth).getTime()) /
          (365.25 * 24 * 60 * 60 * 1000)
      )
    : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-card/30 to-background font-serif smooth-scroll overflow-hidden">
      {/* Patient header moved below into Patient Information section */}

      {/* Full-bleed Clinic Hero */}
      <div className="animate-luxury-fade-in">
        <ClinicInformation clinic={data.clinic} />
      </div>
      <div className="animate-luxury-slide-up">
        <SerenitySection clinicName={data.clinic.name} />
      </div>

      <div className="py-12 relative">
        {/* Floating background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl animate-luxury-float"></div>
          <div className="absolute bottom-20 right-10 w-48 h-48 bg-accent/5 rounded-full blur-2xl animate-luxury-float-delayed"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-16 md:space-y-20 relative z-10">
          {/* Promotional Banners */}
          {(() => {
            const banners = (data.promotions || []).filter(
              (p) =>
                p.type === "BANNER" &&
                p.fileUrl &&
                (p.fileType?.startsWith("image/") ||
                  p.fileType?.startsWith("video/"))
            );
            const cases = (data.promotions || []).filter(
              (p) =>
                p.type === "PREVIOUS_CASES" &&
                p.fileUrl &&
                (p.fileType?.startsWith("image/") ||
                  p.fileType?.startsWith("video/"))
            );
            return (
              <>
                {banners.length > 0 && (
                  <div className="my-8 animate-luxury-slide-up">
                    <AutoCarousel className="w-full">
                      <CarouselContent>
                        {banners.map((item) => (
                          <CarouselItem key={item.id}>
                            <div className="relative w-full h-40 sm:h-52 md:h-64 lg:h-72 overflow-hidden rounded-3xl bg-gradient-to-br from-card to-card/80 backdrop-blur-sm luxury-card">
                              {item.title ? (
                                <a
                                  href={item.title}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="block w-full h-full"
                                >
                                  {item.fileType?.startsWith("image/") ? (
                                    <img
                                      src={item.fileUrl!}
                                      alt="Banner"
                                      className="w-full h-full object-cover transition-transform duration-700 ease-out"
                                    />
                                  ) : (
                                    <video
                                      src={item.fileUrl!}
                                      className="w-full h-full object-cover transition-transform duration-700 ease-out"
                                      autoPlay
                                      muted
                                      loop
                                      playsInline
                                    />
                                  )}
                                </a>
                              ) : (
                                <>
                                  {item.fileType?.startsWith("image/") ? (
                                    <img
                                      src={item.fileUrl!}
                                      alt="Banner"
                                      className="w-full h-full object-cover transition-transform duration-700 ease-out"
                                    />
                                  ) : (
                                    <video
                                      src={item.fileUrl!}
                                      className="w-full h-full object-cover transition-transform duration-700 ease-out"
                                      autoPlay
                                      muted
                                      loop
                                      playsInline
                                    />
                                  )}
                                </>
                              )}
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                    </AutoCarousel>
                  </div>
                )}
                {cases.length > 0 && (
                  <div className="my-8 animate-luxury-slide-up-delayed">
                    <AutoCarousel className="w-full">
                      <CarouselContent>
                        {cases.map((item) => (
                          <CarouselItem key={item.id}>
                            <div className="relative w-full h-40 sm:h-52 md:h-64 lg:h-72 overflow-hidden rounded-3xl bg-gradient-to-br from-card to-card/80 backdrop-blur-sm luxury-card">
                              {item.title ? (
                                <a
                                  href={item.title}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="block w-full h-full"
                                >
                                  {item.fileType?.startsWith("image/") ? (
                                    <img
                                      src={item.fileUrl!}
                                      alt="Previous Case"
                                      className="w-full h-full object-cover transition-transform duration-700 ease-out"
                                    />
                                  ) : (
                                    <video
                                      src={item.fileUrl!}
                                      className="w-full h-full object-cover transition-transform duration-700 ease-out"
                                      autoPlay
                                      muted
                                      loop
                                      playsInline
                                    />
                                  )}
                                </a>
                              ) : (
                                <>
                                  {item.fileType?.startsWith("image/") ? (
                                    <img
                                      src={item.fileUrl!}
                                      alt="Previous Case"
                                      className="w-full h-full object-cover transition-transform duration-700 ease-out"
                                    />
                                  ) : (
                                    <video
                                      src={item.fileUrl!}
                                      className="w-full h-full object-cover transition-transform duration-700 ease-out"
                                      autoPlay
                                      muted
                                      loop
                                      playsInline
                                    />
                                  )}
                                </>
                              )}
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                    </AutoCarousel>
                  </div>
                )}
              </>
            );
          })()}

          <div className="luxury-separator my-16 md:my-20"></div>

          {/* Dentists Section */}
          {data.dentists.length > 0 && (
            <section id="dentists" className="my-16 md:my-20 animate-luxury-slide-up">
              <h3 className="text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3 tracking-tight font-serif luxury-gradient-text">
                <Users className="w-7 h-7" weight="duotone" />
                Our Dental Team
              </h3>
              <div className="luxury-glass-panel rounded-3xl overflow-hidden">
                <Accordion type="single" collapsible className="w-full">
                  {data.dentists.map((dentist, index) => (
                    <div
                      key={dentist.id}
                      className={index % 2 === 0 ? "animate-luxury-fade-in" : "animate-luxury-fade-in-delayed"}
                    >
                      <DentistAccordionItem
                        dentist={dentist}
                        previousCases={data.promotions}
                      />
                    </div>
                  ))}
                </Accordion>
              </div>
            </section>
          )}
          <div className="luxury-separator my-16 md:my-20"></div>

          {/* Patient Information */}
          <section id="patient-info" className="my-16 md:my-20 animate-luxury-scale-in">
            <div className="luxury-glass-panel p-8 rounded-3xl backdrop-blur-xl">
              <div className="flex items-center gap-6">
                <Avatar className="w-16 h-16 sm:w-20 sm:h-20 luxury-avatar">
                  <AvatarFallback className="bg-gradient-to-br from-primary via-accent to-primary/80 text-white text-2xl sm:text-3xl font-bold">
                    {data.patient.firstName.charAt(0)}
                    {data.patient.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 truncate tracking-tight luxury-gradient-text mb-3">
                    {fullName}
                  </h2>
                  <div className="flex flex-wrap gap-4 sm:gap-6 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs luxury-badge">
                        UID: {data.patient.uid}
                      </Badge>
                    </div>
                    {age && (
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-primary" weight="duotone" />
                        <span className="font-medium">{age} years old</span>
                      </div>
                    )}
                    {data.patient.phoneNumber && (
                      <div className="flex items-center gap-2">
                        <PhoneIcon className="w-4 h-4 text-primary" weight="duotone" />
                        <a
                          href={`tel:${data.patient.phoneNumber}`}
                          className="font-medium luxury-link"
                        >
                          {data.patient.phoneNumber}
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Clinical History Section */}
          <section id="clinical-summary" className="my-16 md:my-20 animate-luxury-slide-up">
            <h3 className="text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3 tracking-tight font-serif luxury-gradient-text">
              <Activity className="w-7 h-7" weight="duotone" />
              Clinical History Summary
            </h3>
            <div className="luxury-card">
              <ClinicalSummary data={data.clinicalSummary} />
            </div>
          </section>

          {/* Detailed Clinical Summary with Tooth Visualization */}
          <section id="clinical-details" className="my-16 md:my-20 animate-luxury-slide-up-delayed">
            <div className="luxury-card">
              <DetailedClinicalSummary data={data} />
            </div>
          </section>
        </div>
      </div>
      <div className="animate-luxury-fade-in-slow">
        <ClinicFooter clinic={data.clinic} />
      </div>
    </div>
  );
}
