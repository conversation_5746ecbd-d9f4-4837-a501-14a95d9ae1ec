@import "tailwindcss";
@import "tw-animate-css";
@source "../node_modules/@ilamy/calendar/dist";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /*
    Warm terracotta theme to match the reference invoice UI
    - Backgrounds use soft cream tones
    - Primary actions use terracotta (#C86A49)
    - Text uses deep brown for strong contrast on light backgrounds
  */
  --radius: 0.625rem; /* Slightly rounded corners consistent with UI */

  /* Core surfaces */
  --background: #f5efe9; /* App canvas (left sidebar area tone) */
  --foreground: #2e221d; /* Primary text (dark brown) */
  --card: #fcf9f6; /* Panel/paper surface on the right */
  --card-foreground: #2e221d;
  --popover: #fefdfb; /* Lightweight pop sheet */
  --popover-foreground: #2e221d;

  /* Brand / action colors */
  --primary: #c86a49; /* Terracotta (Approve, Save buttons) */
  --primary-foreground: #fffaf7; /* High contrast on terracotta */
  --secondary: #ede4db; /* Subtle filled controls */
  --secondary-foreground: #3c2f29;
  --muted: #f2ebe5; /* Muted backgrounds (table rows, chips) */
  --muted-foreground: #7b665c; /* Muted text for hints/labels */
  --accent: #b18c7b; /* Soft accent (badges/links subtle) */
  --accent-foreground: #fffaf7;
  --destructive: #a23c2b; /* Error/destructive */

  /* Structure */
  --border: #3a2b26; /* Hairline borders */
  --input: #fdf8f4; /* Input background (very light cream) */
  --ring: #c86a49; /* Focus ring matches primary */

  /* Charts (earthy complementary palette) */
  --chart-1: #c86a49; /* terracotta */
  --chart-2: #7a4633; /* deep clay */
  --chart-3: #e0b39c; /* light clay */
  --chart-4: #9f6b59; /* rosewood */
  --chart-5: #d8c3b6; /* pale sand */

  /* Sidebar */
  --sidebar: #ede5dc; /* Slightly darker cream */
  --sidebar-foreground: #3a2b26;
  --sidebar-primary: #c86a49;
  --sidebar-primary-foreground: #fffaf7;
  --sidebar-accent: #f3ece6; /* Hover and selected in sidebar */
  --sidebar-accent-foreground: #3a2b26;
  --sidebar-border: #e2d6cc;
  --sidebar-ring: #c86a49;
}

.dark {
  /* Dark mode retains warmth while ensuring WCAG contrast */
  --background: #1e1511; /* Deep brown */
  --foreground: #f7f3ef; /* Cream text */
  --card: #2a201b; /* Elevated surface */
  --card-foreground: #f7f3ef;
  --popover: #2a201b;
  --popover-foreground: #f7f3ef;

  --primary: #e07a59; /* Brightened terracotta for dark bg */
  --primary-foreground: #1a120f;
  --secondary: #3a2b26; /* Subtle filled controls */
  --secondary-foreground: #f7f3ef;
  --muted: #332521;
  --muted-foreground: #cbbab0;
  --accent: #a47f6e;
  --accent-foreground: #1a120f;
  --destructive: #d34a3a;

  --border: #725a50;
  --input: #513d34;
  --ring: #e07a59;

  --chart-1: #e07a59;
  --chart-2: #b56549;
  --chart-3: #7d4a3a;
  --chart-4: #f0a889;
  --chart-5: #cbbab0;

  --sidebar: #1a120f;
  --sidebar-foreground: #f7f3ef;
  --sidebar-primary: #e07a59;
  --sidebar-primary-foreground: #1a120f;
  --sidebar-accent: #2a201b;
  --sidebar-accent-foreground: #f7f3ef;
  --sidebar-border: #332521;
  --sidebar-ring: #e07a59;
}

@layer base {
  * {
    /* Use 1px hairline borders in warm neutral */
    @apply border-border outline-ring/50;
  }
  body {
    /* Cream canvas + dark brown text */
    @apply bg-background text-foreground;
  }
  
}

@layer utilities {
  .border {
    border: 1px solid lightgray;
  }
}

/* ilamy Calendar styling improvements */
@layer components {
  /* Calendar grid styling */
  .ilamy-calendar {
    @apply w-full h-full;
  }

  /* Calendar header styling */
  .ilamy-calendar-header {
    @apply border-b border-border/50 bg-card/50 backdrop-blur-sm;
  }

  /* Calendar day cells */
  .ilamy-calendar-day {
    @apply hover:bg-accent/50 transition-colors;
  }

  /* Calendar events */
  .ilamy-calendar-event {
    @apply shadow-sm hover:shadow-md transition-shadow cursor-pointer rounded-md;
  }

  /* Weekend days styling */
  .ilamy-calendar-weekend {
    @apply bg-muted/20;
  }

  /* Today highlighting */
  .ilamy-calendar-today {
    @apply bg-primary/10 border border-primary/20;
  }
}

/* Luxury animations and modern styling */
@layer utilities {
  /* Luxury fade in animations */
  .animate-luxury-fade-in {
    animation: luxuryFadeIn 1.2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    opacity: 0;
  }

  .animate-luxury-fade-in-delayed {
    animation: luxuryFadeIn 1.2s cubic-bezier(0.16, 1, 0.3, 1) 0.3s forwards;
    opacity: 0;
  }

  .animate-luxury-fade-in-slow {
    animation: luxuryFadeIn 1.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    opacity: 0;
  }

  /* Luxury slide up animations */
  .animate-luxury-slide-up {
    animation: luxurySlideUp 1.2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    opacity: 0;
    transform: translateY(40px);
  }

  .animate-luxury-slide-up-delayed {
    animation: luxurySlideUp 1.2s cubic-bezier(0.16, 1, 0.3, 1) 0.4s forwards;
    opacity: 0;
    transform: translateY(40px);
  }

  /* Luxury scale in animation */
  .animate-luxury-scale-in {
    animation: luxuryScaleIn 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    opacity: 0;
    transform: scale(0.95);
  }

  /* Luxury float animations */
  .animate-luxury-float {
    animation: luxuryFloat 8s ease-in-out infinite;
  }

  .animate-luxury-float-delayed {
    animation: luxuryFloat 8s ease-in-out infinite;
    animation-delay: 2s;
  }

  /* Luxury gradient text */
  .luxury-gradient-text {
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 50%, var(--primary) 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: luxuryGradientShift 4s ease-in-out infinite;
  }

  /* Luxury glass panel */
  .luxury-glass-panel {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Luxury card */
  .luxury-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    transform: translateZ(0);
  }

  /* Luxury separator */
  .luxury-separator {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--primary)/20 20%, var(--accent)/30 50%, var(--primary)/20 80%, transparent 100%);
    position: relative;
  }

  .luxury-separator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 5px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 3px;
    opacity: 0.6;
  }

  /* Luxury avatar */
  .luxury-avatar {
    position: relative;
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .luxury-avatar::before {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: inherit;
    z-index: -1;
    opacity: 0.3;
    animation: luxuryPulse 3s ease-in-out infinite;
  }

  /* Luxury badge */
  .luxury-badge {
    background: linear-gradient(135deg, var(--primary)/10, var(--accent)/10);
    border: 1px solid var(--primary)/20;
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Luxury link */
  .luxury-link {
    position: relative;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .luxury-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    transition: width 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .luxury-link:hover::after {
    width: 100%;
  }

  /* Smooth scroll behavior */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: var(--muted);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--accent), var(--primary));
  }
}

/* Luxury keyframes */
@keyframes luxuryFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes luxurySlideUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes luxuryScaleIn {
  from {
    opacity: 0;
    transform: scale(0.9) rotate(-1deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes luxuryFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-8px) rotate(-0.5deg);
  }
}

@keyframes luxuryGradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes luxuryPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}
