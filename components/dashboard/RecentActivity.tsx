"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Users, CheckCircle2, CreditCard, Send } from "lucide-react"

export type ActivityType = "patient_created" | "treatment_completed" | "payment_received" | "invoice_sent"

export interface ActivityItem {
  id: string
  type: ActivityType
  description: string
  timestamp: string | Date
  user: string
  relatedEntity?: {
    type: "patient" | "invoice" | "treatment"
    id: number | string
    name: string
  }
}

export interface RecentActivityProps {
  activities: ActivityItem[]
  loading?: boolean
  className?: string
}

export function RecentActivity({ activities, loading = false, className }: RecentActivityProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-start gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-4/5" />
                  <Skeleton className="h-3 w-1/3" />
                </div>
              </div>
            ))}
          </div>
        ) : activities.length === 0 ? (
          <div className="text-muted-foreground text-sm">No recent activity.</div>
        ) : (
          <ul className="space-y-3">
            {activities.map((a) => {
              const showPatientPrimary = a.type === "treatment_completed" && a.relatedEntity?.type === "patient"
              return (
                <li key={a.id} className="flex items-start gap-3">
                  <ActivityIcon type={a.type} />
                  <div className="min-w-0 flex-1">
                    <div className="text-sm">
                      {showPatientPrimary ? (
                        <span>{a.relatedEntity?.name}</span>
                      ) : (
                        <>
                          {a.description}
                          {a.relatedEntity ? (
                            <span className="text-muted-foreground"> — {a.relatedEntity.name}</span>
                          ) : null}
                        </>
                      )}
                    </div>
                    <div className="text-muted-foreground mt-1 text-xs" title={formatAbsoluteTime(a.timestamp)}>
                      {showPatientPrimary ? (
                        <>
                          {a.description} • {formatRelativeTime(a.timestamp)} • {a.user}
                        </>
                      ) : (
                        <>
                          {formatRelativeTime(a.timestamp)} • {a.user}
                        </>
                      )}
                    </div>
                  </div>
                </li>
              )
            })}
          </ul>
        )}
      </CardContent>
    </Card>
  )
}

function ActivityIcon({ type }: { type: ActivityType }) {
  const iconClass = "h-5 w-5 text-muted-foreground"
  switch (type) {
    case "patient_created":
      return (
        <div className="mt-0.5 rounded-full bg-primary/10 p-1.5">
          <Users className={iconClass} />
        </div>
      )
    case "treatment_completed":
      return (
        <div className="mt-0.5 rounded-full bg-emerald-500/10 p-1.5">
          <CheckCircle2 className={cn(iconClass, "text-emerald-600")} />
        </div>
      )
    case "payment_received":
      return (
        <div className="mt-0.5 rounded-full bg-sky-500/10 p-1.5">
          <CreditCard className={cn(iconClass, "text-sky-600")} />
        </div>
      )
    case "invoice_sent":
    default:
      return (
        <div className="mt-0.5 rounded-full bg-amber-500/10 p-1.5">
          <Send className={cn(iconClass, "text-amber-600")} />
        </div>
      )
  }
}

function formatRelativeTime(value: string | Date): string {
  try {
    const d = typeof value === "string" ? new Date(value) : value
    const delta = Date.now() - d.getTime()
    const seconds = Math.floor(delta / 1000)
    if (seconds < 60) return `${seconds}s ago`
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`
    const days = Math.floor(hours / 24)
    return `${days}d ago`
  } catch {
    return String(value)
  }
}

function formatAbsoluteTime(value: string | Date): string {
  try {
    const d = typeof value === "string" ? new Date(value) : value
    return d.toLocaleString()
  } catch {
    return String(value)
  }
}

export default RecentActivity


