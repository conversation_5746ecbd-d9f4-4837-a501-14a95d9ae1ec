"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Users, ClipboardList, FileWarning, <PERSON>r<PERSON><PERSON><PERSON>, CheckCircle2 } from "lucide-react"

export interface DashboardMetricsData {
  todayPatients: number
  totalPatients: number
  pendingTreatments: number
  outstandingInvoices: number
  completedTreatments: number
}

export interface DashboardMetricsProps {
  metrics: DashboardMetricsData
  loading?: boolean
  className?: string
}

export function DashboardMetrics({ metrics, loading = false, className }: DashboardMetricsProps) {
  const items = [
    {
      label: "Today's Patients",
      value: metrics.todayPatients,
      icon: Users,
    },
    {
      label: "Total Patients",
      value: metrics.totalPatients,
      icon: UserCheck,
    },
    {
      label: "Pending Treatments",
      value: metrics.pendingTreatments,
      icon: ClipboardList,
    },
    {
      label: "Outstanding Invoices",
      value: metrics.outstandingInvoices,
      icon: <PERSON><PERSON><PERSON>ning,
    },
    {
      label: "Completed Treatments",
      value: metrics.completedTreatments,
      icon: CheckCircle2,
    },
  ]

  return (
    <div className={cn("grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5", className)}>
      {items.map((item) => (
        <MetricCard key={item.label} label={item.label} value={item.value} icon={item.icon} loading={loading} />)
      )}
    </div>
  )
}

function MetricCard({
  label,
  value,
  icon: Icon,
  loading,
}: {
  label: string
  value: number
  icon: React.ComponentType<{ className?: string }>
  loading?: boolean
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{label}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold tabular-nums">
          {loading ? <span className="inline-block h-7 w-16 animate-pulse rounded bg-muted" /> : value}
        </div>
      </CardContent>
    </Card>
  )
}

export default DashboardMetrics


