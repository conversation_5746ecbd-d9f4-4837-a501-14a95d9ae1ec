"use client"

import React, { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { CalendarIcon, Clock, User, FileText } from "lucide-react"
import { format } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Combobox, ComboGroup } from "@/components/ui/Combobox"
import { cn } from "@/lib/utils"

const appointmentFormSchema = z.object({
  patientId: z.number().min(1, "Please select a patient"),
  appointmentDate: z.date({
    error: "Please select an appointment date",
  }),
  appointmentTime: z.string().min(1, "Please select an appointment time"),
  durationMinutes: z.number().min(15, "Duration must be at least 15 minutes").max(480, "Duration cannot exceed 8 hours"),
  appointmentType: z.enum(["CONSULTATION", "TREATMENT", "CHECKUP"]),
  status: z.enum(["SCHEDULED", "COMPLETED", "CANCELLED"]),
  primaryProviderId: z.number().optional(),
  notes: z.string().optional(),
})

type AppointmentFormValues = z.infer<typeof appointmentFormSchema>

interface Patient {
  id: number
  firstName: string
  lastName: string
  phoneNumber: string
}

interface Provider {
  id: number
  firstName: string
  lastName: string
  email?: string
  userType: string
}

interface AppointmentFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedDate?: Date
  selectedTime?: string
  editingAppointment?: {
    id: number
    patient: {
      id: number
      firstName: string
      lastName: string
      phoneNumber: string
    }
    start: Date
    end: Date
    appointmentType: 'CONSULTATION' | 'TREATMENT' | 'CHECKUP'
    status: 'SCHEDULED' | 'COMPLETED' | 'CANCELLED'
    notes?: string
    primaryProvider?: {
      id: number
      firstName: string
      lastName: string
    }
  }
  onSubmit: (data: AppointmentFormValues) => Promise<boolean>
}

export function AppointmentForm({
  open,
  onOpenChange,
  selectedDate,
  selectedTime,
  editingAppointment,
  onSubmit,
}: AppointmentFormProps) {
  const [patients, setPatients] = useState<Patient[]>([])
  const [providers, setProviders] = useState<Provider[]>([])
  const [loading, setLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedPatient, setSelectedPatient] = useState<string>("")
  const [selectedProvider, setSelectedProvider] = useState<string>("")

  // Helper functions for combobox data conversion
  const patientsComboGroups: ComboGroup[] = [
    {
      label: "Patients",
      items: patients.map(patient => ({
        value: patient.id.toString(),
        label: `${patient.firstName} ${patient.lastName} - ${patient.phoneNumber}`
      }))
    }
  ]

  const providersComboGroups: ComboGroup[] = [
    {
      label: "Dentists",
      items: [
        { value: "", label: "Clear selection" },
        ...providers.map(provider => ({
          value: provider.id.toString(),
          label: `Dr. ${provider.firstName} ${provider.lastName}`
        }))
      ]
    }
  ]

  const handlePatientSelect = (patientLabel: string) => {
    const patient = patients.find(p => 
      `${p.firstName} ${p.lastName} - ${p.phoneNumber}` === patientLabel
    )
    if (patient) {
      form.setValue("patientId", patient.id)
      setSelectedPatient(patientLabel)
    }
  }

  const handleProviderSelect = (providerLabel: string) => {
    if (providerLabel === "" || providerLabel === "Clear selection") {
      form.setValue("primaryProviderId", undefined)
      setSelectedProvider("")
      return
    }
    
    const provider = providers.find(p => 
      `Dr. ${p.firstName} ${p.lastName}` === providerLabel
    )
    if (provider) {
      form.setValue("primaryProviderId", provider.id)
      setSelectedProvider(providerLabel)
    }
  }

  // Helper function to get default appointment time
  const getDefaultAppointmentTime = () => {
    if (editingAppointment) {
      return format(editingAppointment.start, "HH:mm")
    }
    
    // If we have a specific time from the clicked calendar slot, use it
    if (selectedTime) {
      return selectedTime
    }
    
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    
    // If it's within business hours (8 AM - 5 PM) and not too late in the day
    if (currentHour >= 8 && currentHour < 17) {
      // Round up to next 30-minute slot
      const nextSlotMinute = currentMinute < 30 ? 30 : 0
      const nextSlotHour = currentMinute < 30 ? currentHour : currentHour + 1
      
      // If next slot would be after 5 PM, default to 9 AM
      if (nextSlotHour >= 17) {
        return "09:00"
      }
      
      return `${nextSlotHour.toString().padStart(2, '0')}:${nextSlotMinute.toString().padStart(2, '0')}`
    }
    
    // Default to 9 AM if outside business hours
    return "09:00"
  }

  const form = useForm<AppointmentFormValues>({
    resolver: zodResolver(appointmentFormSchema),
    defaultValues: {
      patientId: editingAppointment?.patient.id || 0,
      appointmentDate: editingAppointment?.start || selectedDate || new Date(),
      appointmentTime: getDefaultAppointmentTime(),
      durationMinutes: editingAppointment 
        ? Math.round((editingAppointment.end.getTime() - editingAppointment.start.getTime()) / 60000)
        : 60,
      appointmentType: editingAppointment?.appointmentType || "CONSULTATION",
      status: editingAppointment?.status || "SCHEDULED",
      primaryProviderId: editingAppointment?.primaryProvider?.id,
      notes: editingAppointment?.notes || "",
    },
  })

  // Load patients and providers when form opens
  useEffect(() => {
    if (open) {
      loadPatientsAndProviders()
      
      // Reset form values based on editing appointment or selected date
      if (editingAppointment) {
        form.reset({
          patientId: editingAppointment.patient.id,
          appointmentDate: editingAppointment.start,
          appointmentTime: format(editingAppointment.start, "HH:mm"),
          durationMinutes: Math.round((editingAppointment.end.getTime() - editingAppointment.start.getTime()) / 60000),
          appointmentType: editingAppointment.appointmentType,
          status: editingAppointment.status,
          primaryProviderId: editingAppointment.primaryProvider?.id,
          notes: editingAppointment.notes || "",
        })
        // Set combobox selected values
        setSelectedPatient(`${editingAppointment.patient.firstName} ${editingAppointment.patient.lastName} - ${editingAppointment.patient.phoneNumber}`)
        setSelectedProvider(editingAppointment.primaryProvider ? `Dr. ${editingAppointment.primaryProvider.firstName} ${editingAppointment.primaryProvider.lastName}` : "")
      } else {
        // Reset to default state for new appointments
        setSelectedPatient("")
        setSelectedProvider("")
        if (selectedDate) {
          form.setValue("appointmentDate", selectedDate)
        }
        // Set appropriate default time for new appointments
        form.setValue("appointmentTime", getDefaultAppointmentTime())
      }
    }
  }, [open, selectedDate, selectedTime, editingAppointment, form])

  const loadPatientsAndProviders = async () => {
    setLoading(true)
    try {
      // Load patients
      const patientsResponse = await fetch('/api/patients/search?limit=100')
      if (patientsResponse.ok) {
        const patientsData = await patientsResponse.json()
        if (patientsData.success) {
          setPatients(patientsData.patients || [])
        }
      }

      // Load providers (dentists)
      const providersResponse = await fetch('/api/users?userType=DENTIST&limit=100')
      if (providersResponse.ok) {
        const providersData = await providersResponse.json()
        if (providersData.success) {
          setProviders(providersData.users || [])
        }
      }
    } catch (error) {
      console.error('Failed to load patients and providers:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (data: AppointmentFormValues) => {
    setIsSubmitting(true)
    try {
      // Combine date and time
      const [hours, minutes] = data.appointmentTime.split(':').map(Number)
      const appointmentDateTime = new Date(data.appointmentDate)
      appointmentDateTime.setHours(hours, minutes, 0, 0)

      const success = await onSubmit({
        ...data,
        appointmentDate: appointmentDateTime,
      })

      if (success) {
        form.reset()
        onOpenChange(false)
      }
    } catch (error) {
      console.error('Failed to create appointment:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] w-full">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            {editingAppointment ? "Edit Appointment" : "New Appointment"}
          </DialogTitle>
          <DialogDescription>
            {editingAppointment ? "Update the appointment details." : "Create a new appointment for a patient."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Patient Selection */}
            <FormField
              control={form.control}
              name="patientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Patient
                  </FormLabel>
                  <FormControl>
                    <Combobox
                      value={selectedPatient}
                      onChange={handlePatientSelect}
                      groups={patientsComboGroups}
                      placeholder="Search for a patient..."
                      emptyMessage="No patients found."
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date and Time */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="appointmentDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="appointmentTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Time</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.from({ length: 30 }, (_, i) => {
                          const hour = Math.floor(i / 2) + 8 // Start from 8 AM
                          const minute = (i % 2) * 30 // 0 or 30 minutes
                          const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
                          return (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Duration and Type */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="durationMinutes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Duration (minutes)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="appointmentType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="CONSULTATION">Consultation</SelectItem>
                        <SelectItem value="TREATMENT">Treatment</SelectItem>
                        <SelectItem value="CHECKUP">Checkup</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Provider and Status */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="primaryProviderId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Provider (Optional)</FormLabel>
                    <FormControl>
                      <Combobox
                        value={selectedProvider}
                        onChange={handleProviderSelect}
                        groups={providersComboGroups}
                        placeholder="Search for a dentist..."
                        emptyMessage="No dentists found."
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                        <SelectItem value="COMPLETED">Completed</SelectItem>
                        <SelectItem value="CANCELLED">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Notes (Optional)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes about this appointment..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || loading}>
                {isSubmitting 
                  ? (editingAppointment ? "Updating..." : "Creating...") 
                  : (editingAppointment ? "Update Appointment" : "Create Appointment")
                }
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
