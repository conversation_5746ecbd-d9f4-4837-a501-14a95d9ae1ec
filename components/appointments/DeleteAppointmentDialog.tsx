"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { AppointmentEvent } from "@/hooks/useAppointments"
import dayjs from "dayjs"

interface DeleteAppointmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  appointment: AppointmentEvent | null
  onConfirm: () => Promise<void>
  isDeleting: boolean
}

export function DeleteAppointmentDialog({
  open,
  onOpenChange,
  appointment,
  onConfirm,
  isDeleting
}: DeleteAppointmentDialogProps) {
  if (!appointment) return null

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Appointment</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this appointment? This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <div className="my-4 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold">{appointment.title}</h4>
          <p className="text-sm text-muted-foreground mt-1">
            {dayjs(appointment.start).format('MMM D, YYYY')} at {dayjs(appointment.start).format('h:mm A')} - {dayjs(appointment.end).format('h:mm A')}
          </p>
          <p className="text-sm text-muted-foreground">
            {appointment.appointmentType} • {appointment.status}
          </p>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Deleting..." : "Delete Appointment"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
