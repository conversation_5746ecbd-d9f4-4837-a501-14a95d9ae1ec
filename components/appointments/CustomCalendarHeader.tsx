"use client"

import React, { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { useIlamyCalendarContext } from "@ilamy/calendar"

interface CustomCalendarHeaderProps {
  className?: string
}

export const CustomCalendarHeader: React.FC<CustomCalendarHeaderProps> = ({ 
  className = "" 
}) => {
  const {
    currentDate,
    view,
    setView,
    nextPeriod,
    prevPeriod,
    today,
  } = useIlamyCalendarContext()

  // Helper function to get button styling based on active view
  const getViewButtonVariant = (viewType: 'day' | 'week' | 'month' | 'year') => {
    return view === viewType ? 'default' : 'outline'
  }

  // Helper function to get button classes based on active view
  const getViewButtonClass = (viewType: 'day' | 'week' | 'month' | 'year') => {
    return cn(
      // Hide year button on mobile for space efficiency
      viewType === 'year' ? 'hidden md:inline-flex' : '',
      // Add active state styling
      view === viewType && 'bg-primary/80'
    )
  }

  useEffect(() => {
    setView('week')
  }, [])

  // Format the current date/title based on the current view
  const getFormattedTitle = () => {
    switch (view) {
      case 'day':
        return currentDate.format('MMMM D, YYYY')
      case 'week':
        const startOfWeek = currentDate.startOf('week')
        const endOfWeek = currentDate.endOf('week')
        if (startOfWeek.month() === endOfWeek.month()) {
          return `${startOfWeek.format('MMMM D')} - ${endOfWeek.format('D, YYYY')}`
        }
        return `${startOfWeek.format('MMM D')} - ${endOfWeek.format('MMM D, YYYY')}`
      case 'month':
        return currentDate.format('MMMM YYYY')
      case 'year':
        return currentDate.format('YYYY')
      default:
        return currentDate.format('MMMM YYYY')
    }
  }

  return (
    <div
      className={cn(
        '@container flex justify-center lg:justify-between flex-wrap items-center gap-2 border-b p-2',
        className
      )}
      data-testid="custom-calendar-header"
    >
      {/* Title area - Left section */}
      <div className="flex flex-wrap items-center justify-center gap-2 2xl:col-span-5 2xl:justify-start">
        <CalendarIcon className="h-5 w-5" />
        <h2 className="text-lg font-semibold text-foreground">
          {getFormattedTitle()}
        </h2>
      </div>

      {/* Navigation and View Controls - Right section */}
      <div className="flex flex-wrap justify-center gap-1 2xl:col-span-7 2xl:justify-end">
        {/* Desktop controls - centralized */}
        <div className="flex items-center justify-center gap-1 w-full overflow-x-auto">
          {/* Navigation Controls */}
          <div className="flex items-center gap-1">
            <Button onClick={prevPeriod} variant="outline" size="sm">
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button onClick={nextPeriod} variant="outline" size="sm">
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button onClick={today} variant="outline" size="sm">
              Today
            </Button>
          </div>

          {/* View Controls */}
          <div className="flex items-center gap-1 ml-2">
            <Button
              onClick={() => setView('day')}
              variant={getViewButtonVariant('day')}
              size="sm"
              className={getViewButtonClass('day')}
            >
              Day
            </Button>
            <Button
              onClick={() => setView('week')}
              variant={getViewButtonVariant('week')}
              size="sm"
              className={getViewButtonClass('week')}
            >
              Week
            </Button>
            <Button
              onClick={() => setView('month')}
              variant={getViewButtonVariant('month')}
              size="sm"
              className={getViewButtonClass('month')}
            >
              Month
            </Button>
            <Button
              onClick={() => setView('year')}
              variant={getViewButtonVariant('year')}
              size="sm"
              className={getViewButtonClass('year')}
            >
              Year
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
