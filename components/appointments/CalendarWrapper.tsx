"use client"

import React, { useEffect } from 'react'

interface CalendarWrapperProps {
  children: React.ReactNode
}

/**
 * Wrapper component to suppress React warnings from the ilamy-calendar library
 * The viewPortProps warning is a known issue from the library itself
 */
export function CalendarWrapper({ children }: CalendarWrapperProps) {
  useEffect(() => {
    // Store original console.error
    const originalError = console.error

    // Filter out the specific React warning about viewPortProps
    console.error = (...args: unknown[]) => {
      const message = args[0]
      if (
        typeof message === 'string' &&
        message.includes('React does not recognize the `viewPortProps` prop')
      ) {
        // Suppress this specific warning from ilamy-calendar
        return
      }
    }

    // Cleanup: restore original console.error when component unmounts
    return () => {
      console.error = originalError
    }
  }, [])

  return <>{children}</>
}
