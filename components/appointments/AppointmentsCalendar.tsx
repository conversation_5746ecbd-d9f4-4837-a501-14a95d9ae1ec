"use client"

import React, { useState, useMemo, useEffect } from "react"
import dayjs from "dayjs"
import isSameOrAfter from "dayjs/plugin/isSameOrAfter"
import isSameOrBefore from "dayjs/plugin/isSameOrBefore"
import timezone from "dayjs/plugin/timezone"
import utc from "dayjs/plugin/utc"
import { useAppointments, type AppointmentEvent } from "@/hooks/useAppointments"
import { AppointmentForm } from "./AppointmentForm"
import { DeleteAppointmentDialog } from "./DeleteAppointmentDialog"
import { CalendarWrapper } from "./CalendarWrapper"
import { CustomCalendarHeader } from "./CustomCalendarHeader"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CalendarIcon, Clock, User, Phone, FileText, Plus, Edit, Trash2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

// Import normally and use dynamic only for the actual component usage
import { IlamyCalendar as IlamyCalendarComponent } from "@ilamy/calendar"
import dynamic from "next/dynamic"

const IlamyCalendar = dynamic(
  () => Promise.resolve(IlamyCalendarComponent),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-96">
        <div className="text-muted-foreground">Loading calendar...</div>
      </div>
    ),
  }
)

// Define our own CalendarEvent interface based on the TypeScript definition
interface CalendarEvent {
  id: string | number
  title: string
  start: dayjs.Dayjs
  end: dayjs.Dayjs
  color?: string
  backgroundColor?: string
  description?: string
  location?: string
  allDay?: boolean
  data?: AppointmentEvent
}

// Extend dayjs with required plugins for ilamy-calendar
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)
dayjs.extend(timezone)
dayjs.extend(utc)

// Remove local interface - using imported CalendarEvent from @ilamy/calendar

export function AppointmentsCalendar() {
  const [currentDate] = useState(new Date())
  const [selectedEvent, setSelectedEvent] = useState<AppointmentEvent | null>(null)
  const [showAppointmentForm, setShowAppointmentForm] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedTime, setSelectedTime] = useState<string | null>(null)
  const [editingAppointment, setEditingAppointment] = useState<AppointmentEvent | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingAppointment, setDeletingAppointment] = useState<AppointmentEvent | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  
  // Calculate date range for the current view (extend by a month on each side)
  const startDate = useMemo(() => {
    const start = new Date(currentDate)
    start.setMonth(start.getMonth() - 1)
    start.setDate(1)
    return start
  }, [currentDate])
  
  const endDate = useMemo(() => {
    const end = new Date(currentDate)
    end.setMonth(end.getMonth() + 2)
    end.setDate(0) // Last day of the month
    return end
  }, [currentDate])

  const { appointments, loading, error, createAppointment, updateAppointment, deleteAppointment, refetch } = useAppointments(startDate, endDate)

  // Get status-based colors
  const getStatusColors = (status: string) => {
    switch (status) {
      case 'SCHEDULED':
        return { backgroundColor: '#3b82f6', color: 'white' } // Blue
      case 'COMPLETED':
        return { backgroundColor: '#10b981', color: 'white' } // Green
      case 'CANCELLED':
        return { backgroundColor: '#ef4444', color: 'white' } // Red
      default:
        return { backgroundColor: '#6b7280', color: 'white' } // Gray
    }
  }

  // Transform appointments to ilamy-calendar event format
  const calendarEvents: CalendarEvent[] = useMemo(() => {
    return appointments.map(appointment => {
      return {
        id: appointment.id,
        title: appointment.title, // Already includes patient name from API
        start: dayjs(appointment.start),
        end: dayjs(appointment.end),
        backgroundColor: appointment.backgroundColor, // Use colors from API
        color: appointment.color, // Use text color from API
        description: `${appointment.appointmentType} - ${appointment.status}`,
        location: appointment.notes || undefined, // Use notes as location
        data: appointment // Store the full appointment data for sidebar
      }
    })
  }, [appointments])

  const handleEventClick = (event: CalendarEvent) => {
    if (event.data) {
      setSelectedEvent(event.data as AppointmentEvent)
    }
  }

  const handleDateClick = (start: dayjs.Dayjs | Date | string, end?: dayjs.Dayjs | Date | string) => {
    // Handle multiple input types and convert to Date
    let date: Date
    let time: string | null = null
    
    if (dayjs.isDayjs(start)) {
      // It's a dayjs object - extract both date and time
      date = start.toDate()
      time = start.format('HH:mm')
    } else if (start instanceof Date) {
      // It's already a Date object - extract both date and time
      date = start
      time = dayjs(start).format('HH:mm')
    } else {
      // It's a string or other format, convert via dayjs
      const dayjsDate = dayjs(start)
      date = dayjsDate.toDate()
      time = dayjsDate.format('HH:mm')
    }
    
    setSelectedDate(date)
    setSelectedTime(time)
    setShowAppointmentForm(true)
    setSelectedEvent(null) // Clear selected event when creating new
    setEditingAppointment(null) // Clear editing appointment when creating new
  }

  const handleFormClose = (open: boolean) => {
    setShowAppointmentForm(open)
    if (!open) {
      setEditingAppointment(null)
      setSelectedDate(null)
      setSelectedTime(null)
    }
  }

  const handleCreateOrUpdateAppointment = async (formData: {
    patientId: number
    appointmentDate: Date
    appointmentTime: string
    durationMinutes: number
    appointmentType: "CONSULTATION" | "TREATMENT" | "CHECKUP"
    status: "SCHEDULED" | "COMPLETED" | "CANCELLED"
    primaryProviderId?: number
    notes?: string
  }) => {
    try {
      const appointmentData = {
        patient: { id: formData.patientId } as AppointmentEvent['patient'],
        start: formData.appointmentDate,
        end: new Date(formData.appointmentDate.getTime() + formData.durationMinutes * 60000),
        appointmentType: formData.appointmentType,
        status: formData.status,
        notes: formData.notes,
        primaryProvider: formData.primaryProviderId ? { id: formData.primaryProviderId } as AppointmentEvent['primaryProvider'] : undefined,
      }

      let success: boolean
      if (editingAppointment) {
        success = await updateAppointment(editingAppointment.id, appointmentData)
        if (success) {
          toast.success("Appointment updated", {
            description: "The appointment has been successfully updated.",
          })
          setEditingAppointment(null)
          // Update the selected event to reflect changes
          const updatedEvent = appointments.find(apt => apt.id === editingAppointment.id)
          if (updatedEvent) setSelectedEvent(updatedEvent)
        }
      } else {
        success = await createAppointment(appointmentData)
        if (success) {
          toast.success("Appointment created", {
            description: "The appointment has been successfully created.",
          })
        }
      }

      if (success) {
        refetch() // Refresh the appointments list
        return true
      }
      return false
    } catch {
      toast.error("Error", {
        description: `Failed to ${editingAppointment ? 'update' : 'create'} appointment. Please try again.`,
      })
      return false
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SCHEDULED':
        return 'bg-blue-100 text-blue-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'CONSULTATION':
        return 'bg-purple-100 text-purple-800'
      case 'TREATMENT':
        return 'bg-orange-100 text-orange-800'
      case 'CHECKUP':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleEditAppointment = (appointment: AppointmentEvent) => {
    setEditingAppointment(appointment)
    setShowAppointmentForm(true)
    setSelectedDate(null) // Clear selected date when editing
    setSelectedTime(null) // Clear selected time when editing
  }

  const handleDeleteAppointment = (appointment: AppointmentEvent) => {
    setDeletingAppointment(appointment)
    setShowDeleteDialog(true)
  }

  const confirmDeleteAppointment = async () => {
    if (!deletingAppointment) return

    setIsDeleting(true)
    try {
      const success = await deleteAppointment(deletingAppointment.id)
      if (success) {
        toast.success("Appointment deleted", {
          description: "The appointment has been successfully deleted.",
        })
        setShowDeleteDialog(false)
        setDeletingAppointment(null)
        setSelectedEvent(null) // Clear selected event
        refetch()
      }
    } catch {
      toast.error("Error", {
        description: "Failed to delete appointment. Please try again.",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-destructive mb-2">Error loading appointments</p>
          <p className="text-sm text-muted-foreground">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">Calendar View</h2>
          <p className="text-sm text-muted-foreground">
            Click on a date to create a new appointment, or click on an appointment to view details.
          </p>
        </div>
        <Button onClick={() => handleDateClick(dayjs())} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Appointment
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Calendar */}
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-0 overflow-hidden">
              <div className="ilamy-calendar min-h-[600px]">
                <CalendarWrapper>
                  <IlamyCalendar
                    events={calendarEvents}
                    firstDayOfWeek="saturday"
                    locale="en"
                    onEventClick={handleEventClick as any}
                    onCellClick={handleDateClick}
                    dayMaxEvents={3}
                    headerComponent={<CustomCalendarHeader />}
                    stickyViewHeader={true}
                    disableDragAndDrop={true}
                  />
                </CalendarWrapper>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Appointment Details Sidebar */}
        <div className="lg:col-span-1">
          {selectedEvent ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  Appointment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">{selectedEvent.title}</h3>
                  <div className="flex gap-2 mt-2">
                    <Badge className={cn("text-xs", getStatusColor(selectedEvent.status))}>
                      {selectedEvent.status}
                    </Badge>
                    <Badge className={cn("text-xs", getTypeColor(selectedEvent.appointmentType))}>
                      {selectedEvent.appointmentType}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{dayjs(selectedEvent.start).format('MMM D, YYYY')}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {dayjs(selectedEvent.start).format('h:mm A')} - {dayjs(selectedEvent.end).format('h:mm A')}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedEvent.patient.firstName} {selectedEvent.patient.lastName}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedEvent.patient.phoneNumber}</span>
                  </div>

                  {selectedEvent.patient.email && (
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedEvent.patient.email}</span>
                    </div>
                  )}

                  {selectedEvent.primaryProvider && (
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>Dr. {selectedEvent.primaryProvider.firstName} {selectedEvent.primaryProvider.lastName}</span>
                    </div>
                  )}

                  {selectedEvent.notes && (
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Notes</span>
                      </div>
                      <p className="text-sm text-muted-foreground pl-6">
                        {selectedEvent.notes}
                      </p>
                    </div>
                  )}
                </div>
                
                {/* Action Buttons at the bottom */}
                <div className="flex gap-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditAppointment(selectedEvent)}
                    className="flex-1 flex items-center gap-2"
                  >
                    <Edit className="h-4 w-4" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteAppointment(selectedEvent)}
                    className="flex-1 flex items-center gap-2 text-destructive hover:bg-destructive hover:text-destructive-foreground"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  Appointment Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground text-center py-8">
                  Click on an appointment to view details
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Appointment Creation/Edit Form */}
      <AppointmentForm
        open={showAppointmentForm}
        onOpenChange={handleFormClose}
        selectedDate={selectedDate || undefined}
        selectedTime={selectedTime || undefined}
        editingAppointment={editingAppointment || undefined}
        onSubmit={handleCreateOrUpdateAppointment}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteAppointmentDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        appointment={deletingAppointment}
        onConfirm={confirmDeleteAppointment}
        isDeleting={isDeleting}
      />
    </div>
  )
}
