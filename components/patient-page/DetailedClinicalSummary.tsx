"use client"

import * as React from "react"
import { Activity, FileText } from "@phosphor-icons/react"
import { PatientData } from "./types"
import { getToothName } from "./utils"
import { DentalArcView } from "./DentalArcView"
import { ClinicalFindingCard } from "./ClinicalFindingCard"

interface DetailedClinicalSummaryProps {
  data: PatientData
}

export function DetailedClinicalSummary({ data }: DetailedClinicalSummaryProps) {
  const clinicalFindings = React.useMemo(() => {
    if (!data.caseSheet?.teeth) return []

    const teethWithFindings: Array<{
      toothNumber: number
      toothName: string
      findings: Array<{
        finding: any
        treatments: any[]
      }>
    }> = []

    data.caseSheet.teeth.forEach((tooth: any) => {
      if (tooth.findings && tooth.findings.length > 0) {
        const findingsData = tooth.findings.map((finding: any) => ({
          finding,
          treatments: finding.treatments || []
        }))

        // Sort findings by date (most recent first)
        findingsData.sort((a: any, b: any) =>
          new Date(b.finding.recordedDate).getTime() - new Date(a.finding.recordedDate).getTime()
        )

        teethWithFindings.push({
          toothNumber: tooth.toothNumber,
          toothName: getToothName(tooth.toothNumber),
          findings: findingsData
        })
      }
    })

    // Sort teeth by tooth number
    return teethWithFindings.sort((a, b) => a.toothNumber - b.toothNumber)
  }, [data.caseSheet])

  const toothStatusMap = React.useMemo(() => {
    const statusMap: Record<number, { hasFindings: boolean; hasPending: boolean; hasCompleted: boolean }> = {}

    clinicalFindings.forEach(({ toothNumber, findings }) => {
      if (!statusMap[toothNumber]) {
        statusMap[toothNumber] = { hasFindings: false, hasPending: false, hasCompleted: false }
      }
      statusMap[toothNumber].hasFindings = true

      findings.forEach(({ treatments }) => {
        treatments.forEach((treatment: any) => {
          if (treatment.status === 'PENDING') {
            statusMap[toothNumber].hasPending = true
          } else if (treatment.status === 'COMPLETED') {
            statusMap[toothNumber].hasCompleted = true
          }
        })
      })
    })

    return statusMap
  }, [clinicalFindings])

  return (
    <div className="space-y-12">
      {/* Dental Arc Visualization */}
      <div className="mb-4">
        <h3 className="text-2xl flex items-center gap-3 font-bold tracking-tight font-serif luxury-gradient-text mb-8">
          <Activity className="w-6 h-6" weight="duotone" />
          Dental Overview
        </h3>
        <DentalArcView toothStatusMap={toothStatusMap} />
        {/* Legend */}
        <div className="flex flex-wrap items-center gap-8 mt-8 p-4 luxury-glass-panel rounded-2xl">
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 rounded-full bg-gradient-to-r from-red-400 to-red-500"></div>
            <span className="text-base font-medium text-gray-600">Active Findings</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 rounded-full bg-gradient-to-r from-amber-400 to-amber-500"></div>
            <span className="text-base font-medium text-gray-600">Pending Treatments</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 rounded-full bg-gradient-to-r from-green-400 to-green-500"></div>
            <span className="text-base font-medium text-gray-600">Completed Treatments</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 rounded-full bg-gradient-to-r from-gray-300 to-gray-400"></div>
            <span className="text-base font-medium text-gray-600">Healthy</span>
          </div>
        </div>
      </div>

      {/* Detailed Clinical Findings */}
      {clinicalFindings.length > 0 && (
        <div className="mt-16 luxury-separator pt-16">
          <h3 className="text-2xl flex items-center gap-3 font-bold tracking-tight font-serif luxury-gradient-text mb-8">
            <FileText className="w-6 h-6" weight="duotone" />
            Clinical Findings & Treatments
          </h3>
          <ul className="space-y-6">
            {clinicalFindings.map((tooth, index) => (
              <ClinicalFindingCard key={`${tooth.toothNumber}-${index}`} tooth={tooth} />
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}
