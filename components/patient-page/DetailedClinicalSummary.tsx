"use client"

import * as React from "react"
import { Activity, FileText } from "lucide-react"
import { PatientData } from "./types"
import { getToothName } from "./utils"
import { DentalArcView } from "./DentalArcView"
import { ClinicalFindingCard } from "./ClinicalFindingCard"

interface DetailedClinicalSummaryProps {
  data: PatientData
}

export function DetailedClinicalSummary({ data }: DetailedClinicalSummaryProps) {
  const clinicalFindings = React.useMemo(() => {
    if (!data.caseSheet?.teeth) return []

    const teethWithFindings: Array<{
      toothNumber: number
      toothName: string
      findings: Array<{
        finding: any
        treatments: any[]
      }>
    }> = []

    data.caseSheet.teeth.forEach((tooth: any) => {
      if (tooth.findings && tooth.findings.length > 0) {
        const findingsData = tooth.findings.map((finding: any) => ({
          finding,
          treatments: finding.treatments || []
        }))

        // Sort findings by date (most recent first)
        findingsData.sort((a: any, b: any) =>
          new Date(b.finding.recordedDate).getTime() - new Date(a.finding.recordedDate).getTime()
        )

        teethWithFindings.push({
          toothNumber: tooth.toothNumber,
          toothName: getToothName(tooth.toothNumber),
          findings: findingsData
        })
      }
    })

    // Sort teeth by tooth number
    return teethWithFindings.sort((a, b) => a.toothNumber - b.toothNumber)
  }, [data.caseSheet])

  const toothStatusMap = React.useMemo(() => {
    const statusMap: Record<number, { hasFindings: boolean; hasPending: boolean; hasCompleted: boolean }> = {}

    clinicalFindings.forEach(({ toothNumber, findings }) => {
      if (!statusMap[toothNumber]) {
        statusMap[toothNumber] = { hasFindings: false, hasPending: false, hasCompleted: false }
      }
      statusMap[toothNumber].hasFindings = true

      findings.forEach(({ treatments }) => {
        treatments.forEach((treatment: any) => {
          if (treatment.status === 'PENDING') {
            statusMap[toothNumber].hasPending = true
          } else if (treatment.status === 'COMPLETED') {
            statusMap[toothNumber].hasCompleted = true
          }
        })
      })
    })

    return statusMap
  }, [clinicalFindings])

  return (
    <div className="space-y-8">
      {/* Dental Arc Visualization */}
      <div className="mb-2">
        <h3 className="text-xl flex items-center gap-2 font-semibold tracking-tight font-serif">
          <Activity className="w-5 h-5" />
          Dental Overview
        </h3>
        <DentalArcView toothStatusMap={toothStatusMap} />
        {/* Legend */}
        <div className="flex flex-wrap items-center gap-6 mt-6 p-2">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span className="text-sm text-gray-600">Active Findings</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-amber-500"></div>
            <span className="text-sm text-gray-600">Pending Treatments</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-sm text-gray-600">Completed Treatments</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-gray-300"></div>
            <span className="text-sm text-gray-600">Healthy</span>
          </div>
        </div>
      </div>

      {/* Detailed Clinical Findings */}
      {clinicalFindings.length > 0 && (
        <div className="mt-12 border-t border-border/40 pt-12">
          <h3 className="text-xl flex items-center gap-2 font-semibold tracking-tight font-serif mb-4">
            <FileText className="w-5 h-5" />
            Clinical Findings & Treatments
          </h3>
          <ul className="space-y-4">
            {clinicalFindings.map((tooth, index) => (
              <ClinicalFindingCard key={`${tooth.toothNumber}-${index}`} tooth={tooth} />
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}
