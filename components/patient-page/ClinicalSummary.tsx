"use client"

import { FileText, Activity, TrendingUp, Clock, CalendarDays } from "lucide-react"
import { formatDate } from "@/lib/date-utils"
import { PatientData } from "./types"

interface ClinicalSummaryProps {
  data: PatientData['clinicalSummary']
}

export function ClinicalSummary({ data }: ClinicalSummaryProps) {
  const stats = [
    {
      label: 'Total Findings',
      value: data.totalFindings,
      icon: FileText,
      gradient: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-500/10',
      textColor: 'text-blue-700'
    },
    {
      label: 'Total Treatments',
      value: data.totalTreatments,
      icon: Activity,
      gradient: 'from-green-500 to-emerald-600',
      bgColor: 'bg-green-500/10',
      textColor: 'text-green-700'
    },
    {
      label: 'Completed',
      value: data.completedTreatments,
      icon: TrendingUp,
      gradient: 'from-emerald-500 to-teal-600',
      bgColor: 'bg-emerald-500/10',
      textColor: 'text-emerald-700'
    },
    {
      label: 'Pending',
      value: data.pendingTreatments,
      icon: Clock,
      gradient: 'from-orange-500 to-red-600',
      bgColor: 'bg-orange-500/10',
      textColor: 'text-orange-700'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className={`relative p-6 rounded-xl border border-border/20 ${stat.bgColor} card-hover animate-fade-in`}
              style={{
                animationDelay: `${index * 0.1}s`,
                background: `linear-gradient(135deg, ${stat.bgColor.replace('bg-', '').replace('/10', '')}10 0%, transparent 100%)`
              }}
            >
              {/* Background gradient */}
              <div className={`absolute inset-0 rounded-xl bg-gradient-to-br ${stat.gradient} opacity-5`}></div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${stat.gradient} flex items-center justify-center shadow-lg`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    <div className={`text-3xl font-bold ${stat.textColor} font-serif animate-scale-in`}>
                      {stat.value}
                    </div>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium text-gray-900">{stat.label}</div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full bg-gradient-to-r ${stat.gradient} transition-all duration-1000 ease-out`}
                      style={{
                        width: stat.value > 0 ? `${Math.min((stat.value / Math.max(...stats.map(s => s.value))) * 100, 100)}%` : '0%'
                      }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          )
        })}
      </div>

      {/* Last Visit Card */}
      {data.lastVisitDate && (
        <div className="relative p-6 rounded-xl border border-border/20 bg-gradient-to-r from-card to-card/80 shadow-sm card-hover animate-slide-up">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary/10 to-transparent rounded-full"></div>

          <div className="relative z-10 flex items-center gap-4">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
              <CalendarDays className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <div className="text-lg font-semibold text-gray-900 font-serif">Last Visit</div>
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              </div>
              <div className="text-sm text-gray-600">
                {formatDate(new Date(data.lastVisitDate))}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Click to view visit details
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary font-serif">
                {Math.floor((Date.now() - new Date(data.lastVisitDate).getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <div className="text-xs text-gray-500">days ago</div>
            </div>
          </div>
        </div>
      )}

      {/* Severity Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-6 rounded-xl border border-border/20 bg-gradient-to-br from-red-50 to-red-100/50 animate-fade-in-delayed">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            Findings by Severity
          </h4>
          <div className="space-y-3">
            {Object.entries(data.findingsBySeverity).map(([severity, count]) => (
              <div key={severity} className="flex items-center justify-between">
                <span className="text-sm font-medium capitalize">{severity}</span>
                <div className="flex items-center gap-2">
                  <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-red-400 to-red-600 transition-all duration-700"
                      style={{
                        width: `${data.totalFindings > 0 ? (count / data.totalFindings) * 100 : 0}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-bold text-red-700 w-8 text-right">{count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="p-6 rounded-xl border border-border/20 bg-gradient-to-br from-blue-50 to-blue-100/50 animate-fade-in-delayed">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            Treatment Progress
          </h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Completion Rate</span>
              <span className="text-lg font-bold text-blue-700">
                {data.totalTreatments > 0 ? Math.round((data.completedTreatments / data.totalTreatments) * 100) : 0}%
              </span>
            </div>
            <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-blue-400 to-blue-600 transition-all duration-1000"
                style={{
                  width: `${data.totalTreatments > 0 ? (data.completedTreatments / data.totalTreatments) * 100 : 0}%`
                }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 text-center">
              {data.completedTreatments} of {data.totalTreatments} treatments completed
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      {/* <ClinicalHistoryChart data={data} /> */}
    </div>
  )
}
