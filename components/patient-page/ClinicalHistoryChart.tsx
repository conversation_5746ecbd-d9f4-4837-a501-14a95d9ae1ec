"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>ontainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { PatientData } from "./types"
import { SEVERITY_COLORS } from "./constants"

interface ClinicalHistoryChartProps {
  data: PatientData['clinicalSummary']
}

export function ClinicalHistoryChart({ data }: ClinicalHistoryChartProps) {
  const severityData = [
    { name: 'Low', value: data.findingsBySeverity.low, fill: SEVERITY_COLORS.low },
    { name: 'Medium', value: data.findingsBySeverity.medium, fill: SEVERITY_COLORS.medium },
    { name: 'High', value: data.findingsBySeverity.high, fill: SEVERITY_COLORS.high },
    { name: 'Unspecified', value: data.findingsBySeverity.unspecified, fill: SEVERITY_COLORS.unspecified }
  ].filter(item => item.value > 0)

  const monthlyData = data.treatmentsByMonth.map(item => ({
    month: new Date(item.month + '-01').toLocaleDateString('en-US', { month: 'short', year: '2-digit' }),
    treatments: item.count
  }))

  const chartConfig = {
    treatments: {
      label: "Treatments",
    },
    low: {
      label: "Low",
    },
    medium: {
      label: "Medium",
    },
    high: {
      label: "High",
    },
    unspecified: {
      label: "Unspecified",
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 w-full overflow-hidden">
      {/* Findings by Severity */}
      {severityData.length > 0 && (
        <Card className="min-w-0 border-0 bg-transparent shadow-none">
          <CardHeader>
            <CardTitle className="text-lg font-semibold font-serif">Findings by Severity</CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <ChartContainer config={chartConfig} className="h-48 sm:h-56 md:h-64 w-full">
              <PieChart>
                <Pie
                  data={severityData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={60}
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {severityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <ChartTooltip content={<ChartTooltipContent />} />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
      )}

      {/* Monthly Treatments */}
      {monthlyData.length > 0 && (
        <Card className="min-w-0 border-0 bg-transparent shadow-none">
          <CardHeader>
            <CardTitle className="text-lg font-semibold font-serif">Treatment History</CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <ChartContainer config={chartConfig} className="h-48 sm:h-56 md:h-64 w-full">
              <BarChart data={monthlyData}>
                <XAxis dataKey="month" tick={{ fontSize: 10 }} />
                <YAxis tick={{ fontSize: 10 }} />
                <Bar dataKey="treatments" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                <ChartTooltip content={<ChartTooltipContent />} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
