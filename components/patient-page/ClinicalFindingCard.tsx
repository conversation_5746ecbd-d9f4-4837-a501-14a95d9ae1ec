"use client"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, TrendingUp, Clock } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { formatDate } from "@/lib/date-utils"
import { getToothName } from "./utils"

interface ClinicalFindingCardProps {
  tooth: any
}

export function ClinicalFindingCard({ tooth }: ClinicalFindingCardProps) {
  const { toothNumber, toothName, findings } = tooth

  const getSeverityBadge = (severity: string) => {
    const level = severity?.toLowerCase()
    const style =
      level === "high"
        ? "bg-red-500/15 text-red-600 border-red-500/30"
        : level === "medium"
        ? "bg-amber-500/15 text-amber-700 border-amber-500/30"
        : "bg-emerald-500/15 text-emerald-700 border-emerald-500/30"
    const label = level === "high" ? "High" : level === "medium" ? "Medium" : "Low"
    return (
      <span className={`inline-flex items-center rounded-full border px-2 py-0.5 text-[10px] font-medium ${style}`}>
        {label}
      </span>
    )
  }

  const getStatusBadge = (status: string) => {
    const normalizedStatus = status?.toLowerCase()
    const color =
      normalizedStatus === "completed"
        ? "bg-emerald-600 text-white"
        : normalizedStatus === "in_progress"
        ? "bg-sky-600 text-white"
        : "bg-amber-600 text-white"
    const label = normalizedStatus === "completed" ? "Completed" : normalizedStatus === "in_progress" ? "In progress" : "Pending"
    return (
      <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-[10px] font-medium ${color}`}>
        {label}
      </span>
    )
  }

  return (
    <li className="space-y-3">
      <div className="flex items-center gap-3">
        <div className="rounded-full bg-primary/10 p-1.5">
          <FileText className="h-5 w-5 text-primary" />
        </div>
        <div className="min-w-0">
          <div className="text-sm font-medium">
            Tooth {toothNumber}
            <span className="ml-2 text-muted-foreground font-normal">• {toothName}</span>
          </div>
          <div className="text-xs text-muted-foreground">
            {findings.length} {findings.length === 1 ? 'finding' : 'findings'}
          </div>
        </div>
      </div>

      <ul className="ml-10 space-y-3">
        {findings.map(({ finding, treatments }: any, findingIndex: number) => (
          <li key={finding.id} className="space-y-2">
            <div className="flex items-start gap-3">
              <div className="rounded-full bg-amber-500/10 p-1.5">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2">
                  <p className="text-sm">{finding.description}</p>
                  {finding.severity && getSeverityBadge(finding.severity)}
                </div>
                <div className="mt-1 text-[11px] text-muted-foreground">
                  Recorded: {formatDate(new Date(finding.recordedDate))}
                  {finding.recordedBy && (
                    <span className="ml-2">• By: Dr. {finding.recordedBy.firstName} {finding.recordedBy.lastName}</span>
                  )}
                </div>
              </div>
            </div>

            <ul className="ml-8 space-y-2">
              {treatments.length === 0 ? (
                <li className="text-xs text-muted-foreground">No treatments planned.</li>
              ) : (
                treatments.map((treatment: any) => (
                  <li key={treatment.id} className="flex items-start gap-3">
                    <div className={`rounded-full p-1.5 ${
                      treatment.status?.toLowerCase() === "completed"
                        ? "bg-emerald-500/10"
                        : treatment.status?.toLowerCase() === "in_progress"
                        ? "bg-sky-500/10"
                        : "bg-amber-500/10"
                    }`}>
                      {treatment.status?.toLowerCase() === "completed" ? (
                        <TrendingUp className="h-4 w-4 text-emerald-600" />
                      ) : (
                        <Clock className={`h-4 w-4 ${
                          treatment.status?.toLowerCase() === "in_progress" ? "text-sky-600" : "text-amber-600"
                        }`} />
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <p className="truncate text-sm font-medium">{treatment.procedureName}</p>
                        {getStatusBadge(treatment.status)}
                      </div>
                      <div className="mt-1 text-[11px] text-muted-foreground">
                        {treatment.completedDate && (
                          <span>Completed: {formatDate(new Date(treatment.completedDate))}</span>
                        )}
                      </div>
                    </div>
                  </li>
                ))
              )}
            </ul>
          </li>
        ))}
      </ul>

      <Separator />
    </li>
  )
}
