export interface PatientData {
  patient: {
    id: number
    firstName: string
    lastName: string
    uid: number
    dateOfBirth: Date | null
    phoneNumber: string | null
    email: string | null
  }
  clinic: {
    id: number
    name: string
    logoImage: string | null
    phoneNumber: string | null
    address: string | null
  }
  dentists: Array<{
    id: number
    firstName: string | null
    lastName: string | null
    email: string | null
    phoneNumber: string | null
    sat: boolean
    satstart: string | null
    satend: string | null
    sun: boolean
    sunstart: string | null
    sunend: string | null
    mon: boolean
    monstart: string | null
    monend: string | null
    tue: boolean
    tuestart: string | null
    tueend: string | null
    wed: boolean
    wedstart: string | null
    wedend: string | null
    thu: boolean
    thustart: string | null
    thuend: string | null
    fri: boolean
    fristart: string | null
    friend: string | null
  }>
  clinicalSummary: {
    totalFindings: number
    totalTreatments: number
    completedTreatments: number
    pendingTreatments: number
    lastVisitDate: Date | null
    findingsBySeverity: {
      low: number
      medium: number
      high: number
      unspecified: number
    }
    treatmentsByMonth: Array<{ month: string; count: number }>
  }
  caseSheet: any
  promotions?: Array<{
    id: number
    type: "BANNER" | "PREVIOUS_CASES"
    title: string | null
    fileUrl: string | null
    fileType: string | null
    userId: number | null
  }>
}

export interface ApiResponse {
  success: boolean
  data?: PatientData
  message?: string
}
