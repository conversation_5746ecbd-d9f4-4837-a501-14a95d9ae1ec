"use client"

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"

export function LoadingCard() {
  return (
    <Card className="border-0 shadow-none bg-gradient-to-br from-card to-card/50 card-hover animate-scale-in">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-accent/20 rounded-xl animate-pulse"></div>
          <div className="space-y-2 flex-1">
            <div className="h-5 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-1/3 animate-pulse"></div>
            <div className="h-3 bg-gradient-to-r from-gray-100 to-gray-200 rounded w-1/2 animate-pulse"></div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Shimmer lines */}
          <div className="space-y-2">
            <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded animate-pulse bg-[length:200%_100%] bg-[animation:shimmer_1.5s_ease-in-out_infinite]"></div>
            <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-2/3 animate-pulse bg-[length:200%_100%] bg-[animation:shimmer_1.5s_ease-in-out_infinite_0.2s]"></div>
            <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-1/2 animate-pulse bg-[length:200%_100%] bg-[animation:shimmer_1.5s_ease-in-out_infinite_0.4s]"></div>
          </div>

          {/* Stats skeleton */}
          <div className="grid grid-cols-2 gap-4 pt-4">
            <div className="text-center p-3 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg">
              <div className="w-8 h-8 mx-auto bg-gradient-to-br from-blue-200 to-blue-300 rounded-lg mb-2 animate-pulse"></div>
              <div className="h-6 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-12 mx-auto mb-1 animate-pulse"></div>
              <div className="h-3 bg-gradient-to-r from-gray-100 to-gray-200 rounded w-16 mx-auto animate-pulse"></div>
            </div>
            <div className="text-center p-3 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg">
              <div className="w-8 h-8 mx-auto bg-gradient-to-br from-green-200 to-green-300 rounded-lg mb-2 animate-pulse"></div>
              <div className="h-6 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-12 mx-auto mb-1 animate-pulse"></div>
              <div className="h-3 bg-gradient-to-r from-gray-100 to-gray-200 rounded w-16 mx-auto animate-pulse"></div>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Loading indicator */}
      <div className="px-6 pb-6">
        <div className="flex items-center justify-center gap-2">
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </Card>
  )
}
