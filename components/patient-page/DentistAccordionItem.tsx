"use client"

import { Mail, Phone, Clock, FileText } from "lucide-react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { CarouselContent, CarouselItem } from "@/components/ui/carousel"
import { PatientData } from "./types"
import { DAYS_OF_WEEK } from "./constants"
import { AutoCarousel } from "./AutoCarousel"

interface DentistAccordionItemProps {
  dentist: PatientData['dentists'][0]
  previousCases: PatientData['promotions']
}

export function DentistAccordionItem({ dentist, previousCases }: DentistAccordionItemProps) {
  const getWorkingHours = () => {
    const schedule = DAYS_OF_WEEK.map(day => {
      const isWorking = dentist[day.key as keyof typeof dentist] as boolean
      const startTime = dentist[`${day.key}start` as keyof typeof dentist] as number | null
      const endTime = day.key === 'fri'
        ? dentist.friend as number | null
        : dentist[`${day.key}end` as keyof typeof dentist] as number | null

      return {
        day: day.label,
        isWorking,
        hours: isWorking && startTime && endTime ? `${startTime} - ${endTime}` : 'Closed'
      }
    })

    return schedule
  }

  const workingHours = getWorkingHours()
  const fullName = `${dentist.firstName || ''} ${dentist.lastName || ''}`.trim() || 'Dr. Name'

  // Filter previous cases for this specific dentist
  const dentistCases = (previousCases || []).filter(p =>
    p.type === 'PREVIOUS_CASES' &&
    p.userId === dentist.id &&
    p.fileUrl &&
    (p.fileType?.startsWith('image/') || p.fileType?.startsWith('video/'))
  )

  return (
    <AccordionItem
      value={dentist.id.toString()}
      className="border-b border-border/30 hover:bg-accent/5 transition-colors duration-200"
    >
      <AccordionTrigger className="px-6 py-6 hover:no-underline">
        <div className="flex items-center gap-4 w-full">
          <div className="relative">
            <Avatar className="w-14 h-14 ring-2 ring-primary/10 ring-offset-2">
              <AvatarFallback className="bg-gradient-to-br from-green-500 to-emerald-600 text-white text-lg font-bold">
                {(dentist.firstName?.charAt(0) || 'D') + (dentist.lastName?.charAt(0) || 'R')}
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
          </div>
          <div className="text-left flex-1">
            <div className="text-xl font-semibold text-gray-900 font-serif">{fullName}</div>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="secondary" className="text-xs">Dentist</Badge>
              <div className="flex items-center gap-1 text-sm text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Available
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">
              {workingHours.filter(h => h.isWorking).length} days/week
            </div>
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent className="px-6 pb-8">
        <div className="space-y-8 animate-slide-up">
          {/* Contact Information */}
          <div className="p-6 rounded-xl border border-border/20 bg-gradient-to-r from-card to-card/50 card-hover">
            <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2 text-lg">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                <Mail className="w-4 h-4 text-white" />
              </div>
              Contact Information
            </h4>
            <div className="space-y-3 ml-10">
              {dentist.email && (
                <div className="flex items-center gap-3 text-sm">
                  <Mail className="w-4 h-4 text-blue-500" />
                  <span className="text-gray-700">{dentist.email}</span>
                </div>
              )}
              {dentist.phoneNumber && (
                <div className="flex items-center gap-3 text-sm">
                  <Phone className="w-4 h-4 text-green-500" />
                  <a
                    href={`tel:${dentist.phoneNumber}`}
                    className="text-gray-700 hover:text-primary transition-colors underline-offset-4 hover:underline"
                  >
                    {dentist.phoneNumber}
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Working Hours */}
          <div className="p-6 rounded-xl border border-border/20 bg-gradient-to-r from-card to-card/50 card-hover">
            <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2 text-lg">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                <Clock className="w-4 h-4 text-white" />
              </div>
              Working Hours
            </h4>
            <div className="ml-10 space-y-3">
              {workingHours.map(({ day, isWorking, hours }, index) => (
                <div
                  key={day}
                  className={`flex justify-between items-center py-2 px-3 rounded-lg transition-all duration-300 ${
                    isWorking ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
                  }`}
                  style={{ animationDelay: `${index * 0.05}s` }}
                >
                  <span className="text-sm font-medium text-gray-700">{day}</span>
                  <div className="flex items-center gap-2">
                    {isWorking && <div className="w-2 h-2 bg-green-500 rounded-full"></div>}
                    <span className={`text-sm font-medium ${
                      isWorking ? 'text-green-700' : 'text-gray-400'
                    }`}>
                      {hours}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Previous Cases Carousel */}
          {dentistCases.length > 0 && (
            <div className="p-6 rounded-xl border border-border/20 bg-gradient-to-r from-card to-card/50 card-hover">
              <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2 text-lg">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center">
                  <FileText className="w-4 h-4 text-white" />
                </div>
                Previous Cases
                <Badge variant="outline" className="ml-2">{dentistCases.length}</Badge>
              </h4>
              <div className="ml-10">
                <AutoCarousel className="w-full">
                  <CarouselContent>
                    {dentistCases.map((item, index) => (
                      <CarouselItem key={item.id}>
                        <div
                          className="relative w-full h-48 sm:h-56 md:h-64 overflow-hidden rounded-xl border border-border/20 shadow-sm card-hover group"
                          style={{ animationDelay: `${index * 0.1}s` }}
                        >
                          {/* Overlay gradient */}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent z-10"></div>

                          {item.title ? (
                            <a href={item.title} target="_blank" rel="noopener noreferrer" className="block w-full h-full">
                              {item.fileType?.startsWith('image/') ? (
                                <img
                                  src={item.fileUrl!}
                                  alt={item.title || "Previous Case"}
                                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                />
                              ) : (
                                <video
                                  src={item.fileUrl!}
                                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                  autoPlay
                                  muted
                                  loop
                                  playsInline
                                />
                              )}
                            </a>
                          ) : (
                            <>
                              {item.fileType?.startsWith('image/') ? (
                                <img
                                  src={item.fileUrl!}
                                  alt={item.title || "Previous Case"}
                                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                />
                              ) : (
                                <video
                                  src={item.fileUrl!}
                                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                  autoPlay
                                  muted
                                  loop
                                  playsInline
                                />
                              )}
                            </>
                          )}

                          {/* Case title overlay */}
                          {item.title && (
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 z-20">
                              <p className="text-white text-sm font-medium truncate">{item.title}</p>
                            </div>
                          )}
                        </div>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                </AutoCarousel>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex flex-wrap gap-3 pt-4">
            <button className="px-4 py-2 bg-gradient-to-r from-primary to-accent text-primary-foreground rounded-lg font-medium shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105">
              Book Appointment
            </button>
            <button className="px-4 py-2 border border-primary/20 bg-background text-primary rounded-lg font-medium hover:bg-primary hover:text-primary-foreground transition-all duration-200">
              View Profile
            </button>
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  )
}
