"use client"

import { MapP<PERSON>, Phone } from "lucide-react"
import { GridPattern } from "@/components/ui/grid-pattern"
import { PatientData } from "./types"

interface ClinicFooterProps {
  clinic: PatientData['clinic']
}

export function ClinicFooter({ clinic }: ClinicFooterProps) {
  return (
    <section className="relative w-full isolate overflow-hidden border-t border-border/40 mt-12 sm:mt-16 bg-gradient-to-b from-background to-card/50">
      {/* Enhanced gradient background */}
      <div
        className="absolute inset-0 -z-10"
        style={{
          background:
            "linear-gradient(135deg, var(--card) 0%, var(--secondary) 50%, var(--muted) 100%)",
        }}
      />

      {/* Animated grid pattern */}
      <GridPattern
        width={32}
        height={32}
        x={-1}
        y={-1}
        className="z-0 opacity-5 [mask-image:linear-gradient(to_top,white,transparent)] animate-pulse"
        style={{ stroke: 'var(--primary)', fill: 'var(--primary)' }}
      />

      {/* Floating elements */}
      <div className="absolute top-4 left-4 w-16 h-16 bg-primary/10 rounded-full blur-xl animate-float"></div>
      <div className="absolute bottom-4 right-4 w-12 h-12 bg-accent/10 rounded-full blur-lg animate-float" style={{ animationDelay: '1s' }}></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
          {/* Clinic Info */}
          <div className="space-y-4 animate-slide-up">
            <h3 className="text-xl font-semibold text-gray-900 font-serif gradient-text">
              {clinic.name}
            </h3>
            <p className="text-muted-foreground">
              Your trusted partner in dental care, committed to providing exceptional service and modern treatments.
            </p>
          </div>

          {/* Contact Information */}
          <div className="space-y-4 animate-fade-in-delayed">
            <h4 className="text-lg font-semibold text-gray-900">Contact Us</h4>
            <div className="space-y-3">
              {clinic.address && (
                <div className="flex items-start gap-3 p-3 rounded-lg bg-gradient-to-r from-card to-card/50 card-hover group">
                  <MapPin className="w-5 h-5 text-primary mt-0.5 group-hover:scale-110 transition-transform" />
                  <span className="text-sm text-gray-700">{clinic.address}</span>
                </div>
              )}
              {clinic.phoneNumber && (
                <div className="flex items-center gap-3 p-3 rounded-lg bg-gradient-to-r from-card to-card/50 card-hover group">
                  <Phone className="w-5 h-5 text-green-500 group-hover:scale-110 transition-transform" />
                  <a
                    href={`tel:${clinic.phoneNumber}`}
                    className="text-sm text-gray-700 hover:text-primary transition-colors underline-offset-4 hover:underline"
                  >
                    {clinic.phoneNumber}
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4 animate-fade-in-slow">
            <h4 className="text-lg font-semibold text-gray-900">Quick Links</h4>
            <div className="grid grid-cols-1 gap-2">
              <a href="#dentists" className="text-sm text-gray-600 hover:text-primary transition-colors p-2 rounded hover:bg-primary/5">
                Our Team
              </a>
              <a href="#patient-info" className="text-sm text-gray-600 hover:text-primary transition-colors p-2 rounded hover:bg-primary/5">
                Patient Info
              </a>
              <a href="#clinical-summary" className="text-sm text-gray-600 hover:text-primary transition-colors p-2 rounded hover:bg-primary/5">
                Clinical Summary
              </a>
              <a href="#clinical-details" className="text-sm text-gray-600 hover:text-primary transition-colors p-2 rounded hover:bg-primary/5">
                Treatment Details
              </a>
            </div>
          </div>
        </div>

        {/* Footer bottom */}
        <div className="pt-8 border-t border-border/40">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>© {new Date().getFullYear()} {clinic.name}. All rights reserved.</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Modern Dental Care</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <button className="text-primary hover:text-accent transition-colors">
                Privacy Policy
              </button>
              <button className="text-primary hover:text-accent transition-colors">
                Terms of Service
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Top wave decoration */}
      <div className="absolute top-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" className="w-full h-auto">
          <path
            fill="var(--background)"
            d="M0,64L48,58.7C96,53,192,43,288,48C384,53,480,75,576,80C672,85,768,75,864,69.3C960,64,1056,53,1152,58.7C1248,64,1344,85,1392,90.7L1440,96L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z"
          />
        </svg>
      </div>
    </section>
  )
}
