"use client"

import * as React from "react"
import { getToothName } from "./utils"

interface DentalArcViewProps {
  toothStatusMap: Record<number, any>
}

export function DentalArcView({ toothStatusMap }: DentalArcViewProps) {
  // Standard FDI numbering for adult dentition arranged in arch formation
  const upperArch = [18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28]
  const lowerArch = [48, 47, 46, 45, 44, 43, 42, 41, 31, 32, 33, 34, 35, 36, 37, 38]

  const getStatusBadges = (toothNumber: number, isUpper: boolean = false) => {
    const status = toothStatusMap[toothNumber]
    if (!status?.hasFindings) return null

    const badges = []
    if (status.hasFindings) badges.push({ color: 'bg-red-500', key: 'findings' })
    if (status.hasPending) badges.push({ color: 'bg-amber-500', key: 'pending' })
    if (status.hasCompleted) badges.push({ color: 'bg-emerald-500', key: 'completed' })

    return (
      <div className="flex items-center justify-center gap-0.5 mt-1">
        {badges.map((badge, index) => (
          <span key={badge.key} className={`h-2 w-2 rounded-full ${badge.color}`} />
        ))}
      </div>
    )
  }

  const ToothImage = ({ toothNumber, style = {}, isUpper = false }: { toothNumber: number; style?: React.CSSProperties; isUpper?: boolean }) => {
    const src = `/teeth/${toothNumber}.svg`
    const toothName = getToothName(toothNumber)

    // Extract rotation from style to counter-rotate the number
    const transform = style.transform || ''
    const rotationMatch = transform.match(/rotate\(([^)]+)\)/)
    const rotation = rotationMatch ? parseFloat(rotationMatch[1]) : 0
    const counterRotation = -rotation // Counter-rotate to keep text readable

    return (
      <div
        className="relative flex flex-col items-center"
        style={style}
        title={`Tooth ${toothNumber} - ${toothName}`}
      >
        <div className="relative w-10 h-12 sm:w-12 sm:h-16 md:w-14 md:h-18 lg:w-16 lg:h-20 flex items-center justify-center">
          <img
            src={src}
            alt={toothName}
            className="w-full h-full object-contain"
          />
        </div>
        <div
          className="text-[8px] sm:text-[9px] md:text-[10px] lg:text-[11px] text-gray-500 mt-1 sm:mt-2"
          style={{ transform: `rotate(${counterRotation}deg)` }}
        >
          {toothNumber}
        </div>
        {getStatusBadges(toothNumber, isUpper)}
      </div>
    )
  }

  // Calculate positions for arch layout
  const getUpperArchPosition = (index: number, total: number) => {
    const centerIndex = (total - 1) / 2
    const offset = index - centerIndex
    const angle = (offset * 11.25) * (Math.PI / 180) // 11.25 degrees between teeth (180/16 = perfect semicircle)
    const radius = 160 // Responsive radius - will be scaled by CSS

    const x = Math.sin(angle) * radius
    const y = -Math.cos(angle) * radius // Perfect semicircle - center at bottom
    const rotation = offset * 11.25 + 180 // Rotate outward + flip teeth

    return {
      transform: `translate(${x}px, ${y}px) rotate(${rotation}deg)`,
      position: 'absolute' as const,
      left: '50%',
      top: '100%', // Position at bottom of container for upper arch
      transformOrigin: 'center center',
    }
  }

  const getLowerArchPosition = (index: number, total: number) => {
    const centerIndex = (total - 1) / 2
    const offset = index - centerIndex
    const angle = (offset * 11.25) * (Math.PI / 180) // 11.25 degrees between teeth (perfect semicircle)
    const radius = 140 // Responsive radius - will be scaled by CSS

    const x = Math.sin(angle) * radius
    const y = Math.cos(angle) * radius // Perfect semicircle - center at top
    const rotation = -offset * 11.25 + 180 // Rotate outward + flip teeth

    return {
      transform: `translate(${x}px, ${y}px) rotate(${rotation}deg)`,
      position: 'absolute' as const,
      left: '50%',
      top: '0%', // Position at top of container for lower arch
      transformOrigin: 'center center',
    }
  }

  return (
    <div className="flex flex-col items-center py-6 sm:py-8 md:py-10 lg:py-12 overflow-x-auto">
      {/* Upper Arch */}
      <div className="relative mb-3 sm:mb-4 md:mb-5 lg:mb-6">
        <div
          className="relative mx-auto scale-75 sm:scale-90 md:scale-100 lg:scale-110"
          style={{ width: '320px', height: '160px' }}
        >
          {upperArch.map((toothNumber, index) => (
            <ToothImage
              key={toothNumber}
              toothNumber={toothNumber}
              isUpper={true}
              style={getUpperArchPosition(index, upperArch.length)}
            />
          ))}
        </div>
      </div>

      {/* Lower Arch */}
      <div className="relative mt-3 sm:mt-4 md:mt-5 lg:mt-6 mb-12 sm:mb-16 md:mb-20 lg:mb-24">
        <div
          className="relative mx-auto scale-75 sm:scale-90 md:scale-100 lg:scale-110"
          style={{ width: '320px', height: '160px' }}
        >
          {lowerArch.map((toothNumber, index) => (
            <ToothImage
              key={toothNumber}
              toothNumber={toothNumber}
              isUpper={false}
              style={getLowerArchPosition(index, lowerArch.length)}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
