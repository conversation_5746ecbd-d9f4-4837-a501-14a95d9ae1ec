"use client"

import * as React from "react"
import Image from "next/image"
import { PatientData } from "./types"
import { GridPattern } from "@/components/ui/grid-pattern"

interface ClinicInformationProps {
  clinic: PatientData['clinic']
}

export function ClinicInformation({ clinic }: ClinicInformationProps) {
  const [logoError, setLogoError] = React.useState(false)

  return (
    <section className="relative w-full isolate overflow-hidden border-b border-border/40">
      {/* Enhanced gradient background with floating animation */}
      <div
        className="absolute inset-0 -z-10 animate-float"
        style={{
          background:
            "linear-gradient(135deg, var(--muted) 0%, var(--secondary) 25%, var(--primary)/10 50%, var(--card) 100%)",
        }}
      />

      {/* Animated grid pattern */}
      <GridPattern
        width={64}
        height={64}
        x={-1}
        y={-1}
        className="z-0 opacity-30 [mask-image:radial-gradient(1200px_circle_at_center,transparent,white)] animate-pulse"
        style={{ stroke: 'var(--primary)', fill: 'var(--primary)' }}
      />

      {/* Subtle overlay gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 -z-10" />

      <div className="px-4 sm:px-6 lg:px-8 mt-24 sm:mt-28 md:mt-32">
        <div className="relative py-10 sm:py-14 md:py-16">
          <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 items-center gap-10 lg:gap-16">
            {/* Left copy + CTAs */}
            <div className="order-2 md:order-1 text-left space-y-6">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium animate-scale-in">
                Let us help you
              </div>
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-semibold tracking-tight font-serif gradient-text animate-slide-up">
                Reconnect with your smile
              </h1>
              <p className="mt-4 text-muted-foreground max-w-xl text-lg leading-relaxed animate-fade-in-delayed">
                {clinic.name} brings you a modern, calming experience that leaves your teeth healthy and your soul at ease.
              </p>
              <div className="mt-8 flex flex-wrap items-center gap-4 animate-slide-up-delayed">
                <a href="#dentists">
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-full bg-gradient-to-r from-primary to-accent px-8 py-3 text-sm font-medium text-primary-foreground shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 glow-effect">
                    Book Appointment
                  </button>
                </a>
                {clinic.phoneNumber && (
                  <a href={`tel:${clinic.phoneNumber}`} className="inline-block">
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-full border-2 border-primary/20 bg-background/80 backdrop-blur-sm px-8 py-3 text-sm font-medium shadow-sm transition-all duration-300 hover:bg-primary hover:text-primary-foreground hover:border-primary hover:scale-105 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50">
                      Call Now
                    </button>
                  </a>
                )}
              </div>

              {/* Trust indicators */}
              <div className="mt-8 pt-6 border-t border-border/20">
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Modern Facilities</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span>Expert Care</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                    <span>Patient First</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right visual */}
            <div className="order-1 md:order-2 flex items-center justify-center animate-scale-in">
              {clinic.logoImage && !logoError ? (
                <div className="relative w-80 h-80 sm:w-[28rem] sm:h-[28rem] md:w-[32rem] md:h-[32rem] lg:w-[36rem] lg:h-[36rem] rounded-full overflow-hidden border-4 border-primary/20 bg-card/50 backdrop-blur-sm card-hover glow-effect">
                  <Image
                    src={clinic.logoImage}
                    alt={`${clinic.name} logo`}
                    fill
                    className="object-contain p-6 hover:scale-110 transition-transform duration-500"
                    sizes="288px"
                    onError={() => setLogoError(true)}
                    onLoad={() => setLogoError(false)}
                  />
                  {/* Floating elements */}
                  <div className="absolute -top-3 -right-3 w-6 h-6 bg-primary rounded-full animate-bounce"></div>
                  <div className="absolute -bottom-3 -left-3 w-5 h-5 bg-accent rounded-full animate-bounce" style={{ animationDelay: '0.5s' }}></div>
                </div>
              ) : (
                <div className="relative w-72 h-72 sm:w-96 sm:h-96 md:w-[28rem] md:h-[28rem] lg:w-[32rem] lg:h-[32rem] rounded-full border-4 border-primary/20 flex items-center justify-center card-hover glow-effect">
                  <div
                    className="w-full h-full rounded-full flex items-center justify-center"
                    style={{
                      background: "linear-gradient(135deg, var(--primary) 0%, var(--accent) 50%, var(--primary) 100%)",
                    }}
                  >
                    <span className="text-4xl sm:text-5xl md:text-6xl font-bold text-primary-foreground animate-pulse">
                      {clinic.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  {/* Decorative elements */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/20 to-transparent"></div>
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary/20 rounded-full animate-float"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
