"use client"

import * as React from "react"
import Image from "next/image"
import { PatientData } from "./types"
import { GridPattern } from "@/components/ui/grid-pattern"

interface ClinicInformationProps {
  clinic: PatientData['clinic']
}

export function ClinicInformation({ clinic }: ClinicInformationProps) {
  const [logoError, setLogoError] = React.useState(false)

  return (
    <section className="relative w-full isolate overflow-hidden">
      {/* Luxury gradient background with floating animation */}
      <div
        className="absolute inset-0 -z-10 animate-luxury-float"
        style={{
          background:
            "linear-gradient(135deg, var(--background) 0%, var(--card)/50 25%, var(--primary)/5 50%, var(--accent)/5 75%, var(--background) 100%)",
        }}
      />

      {/* Animated grid pattern */}
      <GridPattern
        width={80}
        height={80}
        x={-1}
        y={-1}
        className="z-0 opacity-10 [mask-image:radial-gradient(1400px_circle_at_center,transparent,white)] animate-luxury-float"
        style={{ stroke: 'var(--primary)', fill: 'var(--primary)' }}
      />

      {/* Luxury overlay gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-primary/3 to-accent/3 -z-10" />

      <div className="px-4 sm:px-6 lg:px-8 mt-32 sm:mt-36 md:mt-40">
        <div className="relative py-16 sm:py-20 md:py-24">
          <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 items-center gap-12 lg:gap-20">
            {/* Left copy + CTAs */}
            <div className="order-2 md:order-1 text-left space-y-8">
              <div className="inline-flex items-center px-4 py-2 rounded-full luxury-badge text-primary text-sm font-medium animate-luxury-scale-in">
                Let us help you
              </div>
              <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold tracking-tight font-serif luxury-gradient-text animate-luxury-slide-up">
                Reconnect with your smile
              </h1>
              <p className="mt-6 text-muted-foreground max-w-xl text-xl leading-relaxed animate-luxury-fade-in-delayed">
                {clinic.name} brings you a modern, calming experience that leaves your teeth healthy and your soul at ease.
              </p>
              <div className="mt-10 flex flex-wrap items-center gap-6 animate-luxury-slide-up-delayed">
                <a href="#dentists">
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-full bg-gradient-to-r from-primary via-accent to-primary bg-size-200 bg-pos-0 px-10 py-4 text-base font-semibold text-primary-foreground transition-all duration-700 ease-out hover:bg-pos-100 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 luxury-card">
                    Book Appointment
                  </button>
                </a>
                {clinic.phoneNumber && (
                  <a href={`tel:${clinic.phoneNumber}`} className="inline-block">
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-full luxury-glass-panel px-10 py-4 text-base font-semibold transition-all duration-700 ease-out focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50">
                      Call Now
                    </button>
                  </a>
                )}
              </div>

              {/* Trust indicators */}
              <div className="mt-12 pt-8 luxury-separator">
                <div className="flex items-center gap-8 text-base text-muted-foreground">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-luxury-pulse"></div>
                    <span className="font-medium">Modern Facilities</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-full animate-luxury-pulse"></div>
                    <span className="font-medium">Expert Care</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full animate-luxury-pulse"></div>
                    <span className="font-medium">Patient First</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right visual */}
            <div className="order-1 md:order-2 flex items-center justify-center animate-luxury-scale-in">
              {clinic.logoImage && !logoError ? (
                <div className="relative w-96 h-96 sm:w-[32rem] sm:h-[32rem] md:w-[36rem] md:h-[36rem] lg:w-[40rem] lg:h-[40rem] rounded-full overflow-hidden luxury-avatar luxury-glass-panel">
                  <Image
                    src={clinic.logoImage}
                    alt={`${clinic.name} logo`}
                    fill
                    className="object-contain p-8 transition-transform duration-1000 ease-out"
                    sizes="320px"
                    onError={() => setLogoError(true)}
                    onLoad={() => setLogoError(false)}
                  />
                  {/* Floating elements */}
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full animate-luxury-float"></div>
                  <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-gradient-to-r from-accent to-primary rounded-full animate-luxury-float-delayed"></div>
                </div>
              ) : (
                <div className="relative w-80 h-80 sm:w-[28rem] sm:h-[28rem] md:w-[32rem] md:h-[32rem] lg:w-[36rem] lg:h-[36rem] rounded-full flex items-center justify-center luxury-avatar luxury-glass-panel">
                  <div
                    className="w-full h-full rounded-full flex items-center justify-center"
                    style={{
                      background: "linear-gradient(135deg, var(--primary) 0%, var(--accent) 50%, var(--primary) 100%)",
                    }}
                  >
                    <span className="text-5xl sm:text-6xl md:text-7xl font-bold text-primary-foreground animate-luxury-pulse">
                      {clinic.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  {/* Decorative elements */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/10 to-transparent"></div>
                  <div className="absolute -top-6 -right-6 w-12 h-12 bg-primary/10 rounded-full animate-luxury-float"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
