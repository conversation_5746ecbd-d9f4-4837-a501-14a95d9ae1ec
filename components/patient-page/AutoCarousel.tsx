"use client"

import * as React from "react"
import { Carousel, CarouselContent, CarouselItem, CarouselApi } from "@/components/ui/carousel"

interface AutoCarouselProps {
  children: React.ReactNode
  className?: string
}

export function AutoCarousel({ children, className }: AutoCarouselProps) {
  const [api, setApi] = React.useState<CarouselApi>()

  React.useEffect(() => {
    if (!api) return

    const interval = setInterval(() => {
      api.scrollNext()
    }, 5000)

    return () => clearInterval(interval)
  }, [api])

  return (
    <Carousel className={className} opts={{ loop: true }} setApi={setApi}>
      {children}
    </Carousel>
  )
}
