"use client"

import * as React from "react"
import { ConfirmDialog } from "@/components/ui/ConfirmDialog"

export interface DeleteFindingConfirmProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => Promise<void> | void
}

export function DeleteFindingConfirm({ open, onOpenChange, onConfirm }: DeleteFindingConfirmProps) {
  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Delete finding?"
      description="This will remove the finding and may affect associated treatments. This action cannot be undone."
      confirmText="Delete"
      cancelText="Cancel"
      destructive
      onConfirm={onConfirm}
    />
  )
}

export default DeleteFindingConfirm


