"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  She<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"

export interface FindingsPanelProps {
  open: boolean
  onClose: () => void
  tooth: { number: number; name?: string }
  findingsCount?: number
  onAddClick?: () => void
  className?: string
  children?: React.ReactNode
  onDeleteFinding?: () => void
}

export function FindingsPanel({
  open,
  onClose,
  tooth,
  findingsCount,
  onAddClick,
  className,
  children,
  onDeleteFinding,
}: FindingsPanelProps) {
  const title = tooth?.name ? `${tooth.name} (Tooth ${tooth.number})` : `Tooth ${tooth.number}`
  const firstFocusRef = React.useRef<HTMLElement>(null)

  return (
    <Sheet open={open} onOpenChange={(next) => (!next ? onClose() : undefined)}>
      <SheetContent side="right" className={cn("p-0", className)} aria-label="Findings panel" initialFocusRef={firstFocusRef}>
        <SheetHeader className="border-b border-border/60">
          <div className="flex items-center justify-between">
            <div>
              <SheetTitle>{title}</SheetTitle>
              <SheetDescription>Clinical findings and treatments</SheetDescription>
            </div>
            {typeof findingsCount === "number" && (
              <div className="text-xs text-muted-foreground">
                {findingsCount} {findingsCount === 1 ? "finding" : "findings"}
              </div>
            )}
          </div>
        </SheetHeader>

        <div className="flex flex-1 flex-col overflow-auto p-4">
          {children ? (
            children
          ) : (
            <div className="text-sm text-muted-foreground">
              No content provided. Pass findings list as children.
            </div>
          )}
        </div>

        <Separator className="mx-4" />

        <SheetFooter className="p-4">
          <div className="flex w-full items-center justify-between gap-2">
            <Button ref={firstFocusRef as React.RefObject<HTMLButtonElement>} variant="ghost" onClick={onClose} className="min-w-[88px]">
              Close
            </Button>
            <div className="flex items-center gap-2">
              {onDeleteFinding && (
                <Button variant="destructive" onClick={onDeleteFinding} className="min-w-[120px]">
                  Delete Finding
                </Button>
              )}
              <Button onClick={onAddClick} className="min-w-[120px]" disabled={!onAddClick}>
              Add Finding
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}

export default FindingsPanel


