"use client"

import * as React from "react"
import { ConfirmDialog } from "@/components/ui/ConfirmDialog"

export interface DeleteTreatmentConfirmProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => Promise<void> | void
}

export function DeleteTreatmentConfirm({ open, onOpenChange, onConfirm }: DeleteTreatmentConfirmProps) {
  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Delete treatment?"
      description="This will remove the treatment and update related totals and invoices. This action cannot be undone."
      confirmText="Delete"
      cancelText="Cancel"
      destructive
      onConfirm={onConfirm}
    />
  )
}

export default DeleteTreatmentConfirm


