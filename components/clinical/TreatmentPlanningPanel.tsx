"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"

import { formatCurrency } from "@/lib/currency"
import { getCurrentTimestamp } from "@/lib/date-utils"

export type TreatmentStatus = "pending" | "in-progress" | "completed"

export interface TreatmentItem {
  id: number | string
  procedureName: string
  status: TreatmentStatus
  cost?: number
  notes?: string
  completedAt?: string | Date
  completedBy?: string
}

export interface FindingSummary {
  id: number | string
  description: string
}

export interface TreatmentPlanningPanelProps {
  open: boolean
  onClose: () => void
  finding: FindingSummary
  treatments: TreatmentItem[]
  onAddTreatment?: () => void
  onEditTreatment?: (treatment: TreatmentItem) => void
  onDeleteTreatment?: (treatment: TreatmentItem) => void
  className?: string
  renderTreatmentActions?: (t: TreatmentItem) => React.ReactNode
  showTotals?: boolean
  patient?: { id: number | string; name: string }
}

export function TreatmentPlanningPanel({
  open,
  onClose,
  finding,
  treatments,
  onAddTreatment,
  onEditTreatment,
  onDeleteTreatment,
  className,
  renderTreatmentActions,
  showTotals = true,
  patient,
}: TreatmentPlanningPanelProps) {
  const router = useRouter()
  const firstFocusRef = React.useRef<HTMLElement>(null)
  return (
    <Sheet open={open} onOpenChange={(next) => (!next ? onClose() : undefined)}>
      <SheetContent side="right" className={cn("p-0", className)} aria-label="Treatment planning panel" initialFocusRef={firstFocusRef}>
        <SheetHeader className="border-b border-border/60">
          <div className="flex items-center justify-between">
            <div>
              <SheetTitle>Treatments</SheetTitle>
              <SheetDescription>Grouped by finding</SheetDescription>
            </div>
          </div>
        </SheetHeader>

        <div className="flex flex-1 flex-col overflow-auto p-4">
          <FindingGroup
            finding={finding}
            treatments={treatments}
            patient={patient}
            renderTreatmentActions={
              renderTreatmentActions ??
              ((t) => (
                <div className="flex items-center gap-2">
                  <button
                    className="inline-flex items-center rounded-md border px-2 py-1 text-[11px] hover:bg-muted"
                    onClick={() => onEditTreatment?.(t)}
                  >
                    Edit
                  </button>
                  <button
                    className="inline-flex items-center rounded-md border border-destructive/40 text-destructive px-2 py-1 text-[11px] hover:bg-destructive/10"
                    onClick={() => onDeleteTreatment?.(t)}
                  >
                    Delete
                  </button>
                  <button
                    className={cn(
                      "inline-flex items-center rounded-md border px-2 py-1 text-[11px]",
                      t.status === "completed"
                        ? "border-amber-400 text-amber-700 hover:bg-amber-50"
                        : "border-emerald-400 text-emerald-700 hover:bg-emerald-50"
                    )}
                    onClick={() => onEditTreatment?.({ ...t, status: t.status === "completed" ? ("pending" as const) : ("completed" as const) })}
                  >
                    {t.status === "completed" ? "Mark pending" : "Mark complete"}
                  </button>
                </div>
              ))
            }
            showTotals={showTotals}
          />
        </div>

        <Separator className="mx-4" />

        <SheetFooter className="p-4">
          <div className="flex w-full items-center justify-between gap-2">
            <Button variant="ghost" onClick={onClose} className="min-w-[88px]">
              Cancel
            </Button>
            <Button
              variant="outline"
              className="min-w-[160px] border-emerald-500 text-emerald-700 hover:bg-emerald-50 hover:text-emerald-700"
              onClick={async () => {
                try {
                  const res = await fetch(`/api/billing/invoices`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      patientId: Number(patient?.id ?? 0),
                      treatmentIds: treatments.map((t) => Number(t.id ?? 0)).filter((n) => Number.isFinite(n) && n > 0),
                    }),
                  })
                  if (!res.ok) return
                  const json = await res.json()
                  const invId = json?.invoice?.id
                  if (invId) {
                    router.push(`/dashboard/billing/invoices/${invId}`)
                  }
                } catch {}
              }}
            >
              Generate Invoice
            </Button>
            <Button ref={firstFocusRef as React.RefObject<HTMLButtonElement>} onClick={onAddTreatment} disabled={!onAddTreatment} className="min-w-[140px]">
              Add Treatment
            </Button>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}

function FindingGroup({
  finding,
  treatments,
  patient,
  renderTreatmentActions,
  showTotals,
}: {
  finding: FindingSummary
  treatments: TreatmentItem[]
  patient?: { id: number | string; name: string }
  renderTreatmentActions?: (t: TreatmentItem) => React.ReactNode
  showTotals?: boolean
}) {
  const subtotal = React.useMemo(() => {
    return treatments.reduce((sum, t) => {
      const costNumber = Number(t.cost ?? 0)
      return sum + (Number.isFinite(costNumber) ? costNumber : 0)
    }, 0)
  }, [treatments])

  return (
    <section className="space-y-3">
      <header>
        <h3 className="text-sm font-semibold">Finding</h3>
        <p className="text-muted-foreground text-sm">{finding.description}</p>
      </header>
          <ul className="space-y-2">
            {treatments.length === 0 ? (
              <li className="rounded-md border border-dashed p-4 text-center text-sm text-muted-foreground">
                No treatments added yet.
              </li>
            ) : (
              treatments.map((t) => (
                <li key={t.id} className="rounded-md border p-3">
                  <div className="space-y-3">
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <p className="truncate text-sm font-medium">{t.procedureName}</p>
                        <TreatmentStatusBadge status={t.status} />
                      </div>
                      <div className="mt-1 text-[11px] text-muted-foreground">
                        {(() => {
                          const costNumber = Number(t.cost ?? 0)
                          return Number.isFinite(costNumber) ? `Cost: ${formatCurrency(costNumber)}` : null
                        })()}
                        {t.notes ? (
                          <span className="ml-2 truncate align-middle">• {t.notes}</span>
                        ) : null}
                      </div>
                    </div>
                    {renderTreatmentActions ? (
                      <div className="flex flex-wrap items-center gap-2 border-t pt-2">
                        {renderTreatmentActions(t)}
                      </div>
                    ) : null}
                  </div>
                </li>
              ))
            )}
          </ul>
       {showTotals ? (
        <div className="flex flex-col items-end gap-2 text-sm">
          <div className="rounded-md border bg-muted/30 px-2 py-1">
             Subtotal: <span className="font-medium">{formatCurrency(subtotal)}</span>
          </div>
        </div>
      ) : null}
    </section>
  )
}

function TreatmentStatusBadge({ status }: { status: TreatmentStatus }) {
  const color =
    status === "completed"
      ? "bg-emerald-600 text-white"
      : status === "in-progress"
      ? "bg-sky-600 text-white"
      : "bg-amber-600 text-white"

  const dot =
    status === "completed"
      ? "bg-emerald-500"
      : status === "in-progress"
      ? "bg-sky-500"
      : "bg-amber-500"

  const label =
    status === "completed" ? "Completed" : status === "in-progress" ? "In progress" : "Pending"

  return (
    <span className={cn("inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[11px] font-medium", color)}>
      <span className={cn("h-1.5 w-1.5 rounded-full", dot)} />
      {label}
    </span>
  )
}

// currency handled by shared helper

export default TreatmentPlanningPanel


