"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface ToothTooltipProps {
  toothNumber: number
  toothName: string
  findings?: Array<{
    id?: number | string
    description?: string
    severity?: "low" | "medium" | "high"
    createdAt?: string | Date
  }>
  treatments?: Array<{
    id?: number | string
    procedureName?: string
    status?: "pending" | "in-progress" | "completed"
    cost?: number
  }>
  className?: string
}

export function ToothTooltip({
  toothNumber,
  toothName,
  findings = [],
  treatments = [],
  className,
}: ToothTooltipProps) {
  const totalFindings = findings.length
  const pendingTreatments = treatments.filter((t) => t.status === "pending").length
  const inProgressTreatments = treatments.filter((t) => t.status === "in-progress").length
  const completedTreatments = treatments.filter((t) => t.status === "completed").length

  return (
    <div className={cn("flex min-w-56 max-w-64 flex-col gap-1.5", className)}>
      <div className="flex items-center justify-between gap-2">
        <div className="text-xs/5 font-semibold">{toothName}</div>
        <div className="text-[10px] opacity-90">FDI {toothNumber}</div>
      </div>

      <div className="h-px w-full bg-primary-foreground/20" />

      <div className="space-y-1">
        <div className="text-[11px]">
          <span className="opacity-90">Findings:</span> <span className="font-medium">{totalFindings}</span>
        </div>
        <div className="text-[11px]">
          <span className="opacity-90">Treatments:</span>
          <span className="ml-1">
            <span className="font-medium">{pendingTreatments}</span>
            <span className="opacity-90"> pending</span>
            {inProgressTreatments > 0 && (
              <>
                <span className="opacity-60"> • </span>
                <span className="font-medium">{inProgressTreatments}</span>
                <span className="opacity-90"> in-progress</span>
              </>
            )}
            {completedTreatments > 0 && (
              <>
                <span className="opacity-60"> • </span>
                <span className="font-medium">{completedTreatments}</span>
                <span className="opacity-90"> completed</span>
              </>
            )}
          </span>
        </div>
      </div>

      {totalFindings > 0 && findings[0]?.description && (
        <div className="mt-1 rounded-sm bg-primary-foreground/10 px-2 py-1 text-[10px] text-primary-foreground/90">
          {findings[0].description}
        </div>
      )}
    </div>
  )
}

export default ToothTooltip


