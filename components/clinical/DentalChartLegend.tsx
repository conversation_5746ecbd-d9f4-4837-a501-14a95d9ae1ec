"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface DentalChartLegendProps {
  className?: string
}

export function DentalChartLegend({ className }: DentalChartLegendProps) {
  return (
    <div
      className={cn(
        "flex w-full flex-wrap items-center gap-3 rounded-md border border-border bg-card/50 px-3 py-2 text-xs text-muted-foreground",
        className
      )}
      aria-label="Dental chart legend"
    >
      <LegendItem
        colorClass="bg-red-500"
        label="Active findings"
        ariaLabel="Red dot indicates a tooth with active findings"
      />
      <LegendItem
        colorClass="bg-amber-500"
        label="Pending treatments"
        ariaLabel="Amber dot indicates a tooth with pending treatments"
      />
      <LegendItem
        colorClass="bg-emerald-500"
        label="Completed treatments"
        ariaLabel="Green dot indicates a tooth with completed treatments"
      />
    </div>
  )
}

function LegendItem({
  colorClass,
  label,
  ariaLabel,
}: {
  colorClass: string
  label: string
  ariaLabel: string
}) {
  return (
    <div className="flex items-center gap-1.5" aria-label={ariaLabel}>
      <span className={cn("h-2.5 w-2.5 rounded-full border border-background", colorClass)} />
      <span>{label}</span>
    </div>
  )
}

function LegendMissing({ label }: { label: string }) {
  return (
    <div className="flex items-center gap-1.5" aria-label="Muted square indicates a missing tooth">
      <span className="relative inline-flex h-3.5 w-3.5 items-center justify-center rounded-[3px] ring-2 ring-muted-foreground/40">
        <span className="text-[8px] leading-none text-muted-foreground">M</span>
      </span>
      <span>{label}</span>
    </div>
  )
}

export default DentalChartLegend


