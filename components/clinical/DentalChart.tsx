"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { ToothComponent, type ToothStatus } from "./ToothComponent"
import { DentalChartLegend } from "./DentalChartLegend"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { FindingForm } from "@/components/clinical/FindingForm"
import { TreatmentForm } from "@/components/clinical/TreatmentForm"
import { type FindingFormData } from "@/lib/types/finding-schemas"
import { type TreatmentFormData } from "@/lib/types/treatment-schemas"

export interface DentalChartProps {
  className?: string
  toothSize?: number
  statusByTooth?: Partial<Record<number, ToothStatus>>
  selectedTooth?: number
  onToothClick?: (toothNumber: number) => void
  onSelectionChange?: (toothNumber: number) => void
  tooltipDataByTooth?: Partial<Record<number, { findings?: Array<{ id?: number | string; description?: string; severity?: "low" | "medium" | "high"; createdAt?: string | Date }>; treatments?: Array<{ id?: number | string; procedureName?: string; status?: "pending" | "in-progress" | "completed"; cost?: number }> }>>
  // Mapping from FDI tooth number (e.g., 11, 12, ..., 48) to database tooth ID
  toothIdByNumber?: Partial<Record<number, number>>
  // Optional callback fired after a successful bulk creation (use to refresh data)
  onAfterBulkApply?: () => void
  // Patient context for invoice creation
  patientId?: number
  // Optional callback fired after invoice creation
  onAfterInvoiceCreate?: () => void
}

// FDI quadrants
const Q1_UPPER_RIGHT = [11, 12, 13, 14, 15, 16, 17, 18]
const Q2_UPPER_LEFT = [21, 22, 23, 24, 25, 26, 27, 28]
const Q3_LOWER_LEFT = [31, 32, 33, 34, 35, 36, 37, 38]
const Q4_LOWER_RIGHT = [41, 42, 43, 44, 45, 46, 47, 48]

function getQuadrantName(number: number): string {
  const quadrant = Math.floor(number / 10)
  return quadrant === 1
    ? "Upper Right"
    : quadrant === 2
    ? "Upper Left"
    : quadrant === 3
    ? "Lower Left"
    : "Lower Right"
}

function QuadrantRow({
  numbers,
  size,
  statusByTooth,
  selectedTooth,
  onToothClick,
  reverse,
  tooltipDataByTooth,
  selectedTeeth,
  onToggleTooth,
  toothIdByNumber,
}: {
  numbers: number[]
  size: number
  statusByTooth?: Partial<Record<number, ToothStatus>>
  selectedTooth?: number
  onToothClick?: (toothNumber: number) => void
  reverse?: boolean
  tooltipDataByTooth?: DentalChartProps["tooltipDataByTooth"]
  selectedTeeth: Set<number>
  onToggleTooth: (toothNumber: number, nextChecked: boolean) => void
  toothIdByNumber?: DentalChartProps["toothIdByNumber"]
}) {
  const title = getQuadrantName(numbers[0])
  const sequence = reverse ? [...numbers].reverse() : numbers
  return (
    <div className="flex flex-col items-center gap-2">
      <div className="text-xs font-medium text-muted-foreground">{title}</div>
      <div className={cn("flex gap-2")}>
        {sequence.map((n) => {
          const status = statusByTooth?.[n]
          const isSelected = selectedTooth === n
          const tooltipData = tooltipDataByTooth?.[n]
          return (
            <div key={n} className="relative pt-8">
              {/* Switch above the tooth card */}
              <Switch
                checked={selectedTeeth.has(n)}
                onCheckedChange={(checked) => onToggleTooth(n, Boolean(checked))}
                aria-label={`Select tooth ${n}`}
                className="absolute left-1/2 top-0 -translate-x-1/2 z-30 bg-background shadow-md shadow-black/20 dark:shadow-black/40"
              />
              <ToothComponent
                number={n}
                size={size}
                status={status}
                selected={isSelected}
                onClick={onToothClick ? () => onToothClick(n) : undefined}
                tooltipFindings={tooltipData?.findings}
                tooltipTreatments={tooltipData?.treatments}
                dbId={toothIdByNumber?.[n]}
              />
            </div>
          )
        })}
      </div>
    </div>
  )
}

export function DentalChart({
  className,
  toothSize = 40,
  statusByTooth,
  selectedTooth,
  onToothClick,
  onSelectionChange,
  tooltipDataByTooth,
  toothIdByNumber,
  onAfterBulkApply,
  patientId,
  onAfterInvoiceCreate,
}: DentalChartProps) {
  // Responsive sizing: compute tooth size from available width and breakpoint
  const containerRef = React.useRef<HTMLDivElement | null>(null)
  const [isMdUp, setIsMdUp] = React.useState<boolean>(
    typeof window !== "undefined" ? window.matchMedia("(min-width: 768px)").matches : false
  )
  const [computedToothSize, setComputedToothSize] = React.useState<number>(toothSize)

  React.useEffect(() => {
    if (typeof window === "undefined") return
    const mql = window.matchMedia("(min-width: 768px)")
    const handler = (e: MediaQueryListEvent) => setIsMdUp(e.matches)
    // Initial sync (for SSR hydration safety)
    setIsMdUp(mql.matches)
    mql.addEventListener("change", handler)
    return () => mql.removeEventListener("change", handler)
  }, [])

  const recalcToothSize = React.useCallback(() => {
    const gridEl = containerRef.current
    if (!gridEl) return
    const gridWidth = gridEl.clientWidth
    if (gridWidth <= 0) return

    const numColumns = isMdUp ? 2 : 1
    // Tailwind: gap-8 (32px) on base, gap-12 (48px) on md+
    const gridGapPx = isMdUp ? 48 : 32
    const columnWidth =
      numColumns === 1 ? gridWidth : Math.max(0, (gridWidth - gridGapPx) / numColumns)

    // Each quadrant row has 8 teeth, with gap-2 (8px) between items
    const teethPerRow = 8
    const toothGapPx = 8
    // Card adds extra horizontal chrome beyond "size": +18px in ToothComponent
    const toothCardExtraPx = 18
    const maxSizeByWidth = Math.floor(
      (columnWidth - (teethPerRow - 1) * toothGapPx) / teethPerRow - toothCardExtraPx
    )

    const minSize = 24
    const capSize = toothSize ?? 56
    const nextSize = Math.max(minSize, Math.min(maxSizeByWidth, capSize))
    if (Number.isFinite(nextSize) && nextSize > 0) {
      setComputedToothSize(nextSize)
    }
  }, [isMdUp, toothSize])

  React.useLayoutEffect(() => {
    recalcToothSize()
  }, [recalcToothSize])

  React.useEffect(() => {
    const gridEl = containerRef.current
    if (!gridEl) return
    const observer = new ResizeObserver(() => recalcToothSize())
    observer.observe(gridEl)
    window.addEventListener("resize", recalcToothSize)
    return () => {
      observer.disconnect()
      window.removeEventListener("resize", recalcToothSize)
    }
  }, [recalcToothSize])
  const [internalSelectedTooth, setInternalSelectedTooth] = React.useState<number | undefined>(
    undefined
  )

  const effectiveSelectedTooth =
    typeof selectedTooth === "number" ? selectedTooth : internalSelectedTooth

  const handleToothClick = React.useCallback(
    (toothNumber: number) => {
      if (typeof selectedTooth !== "number") {
        setInternalSelectedTooth(toothNumber)
      }
      onSelectionChange?.(toothNumber)
      onToothClick?.(toothNumber)
    },
    [onToothClick, onSelectionChange, selectedTooth]
  )


  // Bulk selection state for checkboxes
  const ALL_TEETH = React.useMemo(() => {
    const all = [
      ...Q2_UPPER_LEFT,
      ...Q1_UPPER_RIGHT,
      ...Q3_LOWER_LEFT,
      ...Q4_LOWER_RIGHT,
    ]
    // Only include teeth that have a valid DB id mapping when selection is by DB id
    if (!toothIdByNumber) return all
    return all.filter((fdi) => Number.isInteger(Number(toothIdByNumber[fdi])))
  }, [toothIdByNumber])
  const [selectedTeeth, setSelectedTeeth] = React.useState<Set<number>>(new Set())
  const numSelected = selectedTeeth.size
  const allCount = ALL_TEETH.length
  const allChecked = numSelected === allCount

  const toggleAll = React.useCallback((next: boolean) => {
    setSelectedTeeth(() => (next ? new Set(ALL_TEETH) : new Set()))
  }, [ALL_TEETH])

  const handleToggleTooth = React.useCallback((toothNumber: number, nextChecked: boolean) => {
    setSelectedTeeth((prev) => {
      const nextSet = new Set(prev)
      if (nextChecked) nextSet.add(toothNumber)
      else nextSet.delete(toothNumber)
      return nextSet
    })
  }, [])

  // Dialog state and form aggregation
  const [isDialogOpen, setIsDialogOpen] = React.useState(false)
  const [findingValues, setFindingValues] = React.useState<FindingFormData | null>(null)
  const [findingValid, setFindingValid] = React.useState(false)
  const [treatmentValues, setTreatmentValues] = React.useState<TreatmentFormData | null>(null)
  const [treatmentValid, setTreatmentValid] = React.useState(false)
  const [submitting, setSubmitting] = React.useState(false)
  const [submitError, setSubmitError] = React.useState<string | null>(null)
  const [invoiceSubmitting, setInvoiceSubmitting] = React.useState(false)
  const [invoiceError, setInvoiceError] = React.useState<string | null>(null)
  const router = useRouter()

  const canOpenDialog = numSelected > 0
  const canConfirm = canOpenDialog && findingValid && treatmentValid && !submitting

  // Stable callbacks to avoid re-subscribing watchers in child forms and infinite update loops
  const handleFindingValuesChange = React.useCallback((v: FindingFormData, valid: boolean) => {
    setFindingValues(v)
    setFindingValid(valid)
  }, [])

  const handleTreatmentValuesChange = React.useCallback((v: TreatmentFormData, valid: boolean) => {
    setTreatmentValues(v)
    setTreatmentValid(valid)
  }, [])

  const handleConfirm = React.useCallback(async () => {
    if (!findingValues || !treatmentValues) return
    setSubmitting(true)
    setSubmitError(null)
    try {
      const payloadBase: {
        description: string;
        severity?: "low" | "medium" | "high";
        procedureName: string;
        cost: number;
      } = {
        description: findingValues.description,
        severity: findingValues.severity,
        procedureName: treatmentValues.procedureName,
        cost: Number(treatmentValues.cost),
      }
      // Map FDI numbers to DB tooth IDs
      const ids: number[] = Array.from(selectedTeeth)
        .map((fdi) => Number(toothIdByNumber?.[fdi]))
        .filter((v) => Number.isInteger(v) && v > 0) as number[]

      if (ids.length !== selectedTeeth.size) {
        throw new Error("Missing tooth ID mapping for one or more selected teeth")
      }

      const requests = ids.map((dbToothId) =>
        fetch(`/api/clinical/teeth/${dbToothId}/findings`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payloadBase),
        }).then(async (res) => {
          if (!res.ok) {
            const data = await res.json().catch(() => ({}))
            const msg = data?.message || data?.errors?.join?.(", ") || `Request failed (${res.status})`
            throw new Error(msg)
          }
          return res.json()
        })
      )
      await Promise.all(requests)
      setIsDialogOpen(false)
      setSelectedTeeth(new Set())
      onAfterBulkApply?.()
    } catch (err) {
      const message = err instanceof Error ? err.message : "Failed to save to all selected teeth"
      setSubmitError(message)
    } finally {
      setSubmitting(false)
    }
  }, [findingValues, treatmentValues, selectedTeeth, toothIdByNumber, onAfterBulkApply])

  const selectedTreatmentIds = React.useMemo(() => {
    const ids: number[] = []
    if (!tooltipDataByTooth) return ids
    for (const fdi of selectedTeeth) {
      const tr = tooltipDataByTooth[fdi]?.treatments ?? []
      for (const t of tr ?? []) {
        const idNum = Number(t?.id)
        if (Number.isInteger(idNum) && idNum > 0) ids.push(idNum)
      }
    }
    // dedupe
    return Array.from(new Set(ids))
  }, [selectedTeeth, tooltipDataByTooth])

  const canCreateInvoice = numSelected > 0 && selectedTreatmentIds.length > 0 && !invoiceSubmitting && Number.isInteger(Number(patientId))

  const handleCreateInvoice = React.useCallback(async () => {
    if (!Number.isInteger(Number(patientId))) return
    if (selectedTreatmentIds.length === 0) return
    setInvoiceSubmitting(true)
    setInvoiceError(null)
    try {
      const res = await fetch(`/api/billing/invoices`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ patientId: Number(patientId), treatmentIds: selectedTreatmentIds }),
      })
      if (!res.ok) {
        const data = await res.json().catch(() => ({}))
        const msg = data?.message || `Request failed (${res.status})`
        throw new Error(msg)
      }
      const json = await res.json().catch(() => ({}))
      const invId = Number(json?.invoice?.id)
      setSelectedTeeth(new Set())
      onAfterInvoiceCreate?.()
      if (Number.isFinite(invId) && invId > 0) {
        router.push(`/dashboard/billing/invoices/${invId}`)
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : "Failed to create invoice"
      setInvoiceError(message)
    } finally {
      setInvoiceSubmitting(false)
    }
  }, [patientId, selectedTreatmentIds, onAfterInvoiceCreate, router])

  return (
    <div className={cn("w-full space-y-6", className)}>
      <DentalChartLegend />
      {/* Bulk selection toolbar */}
      <div className="flex items-center justify-between gap-3 px-2 md:px-6">
        <div className="flex items-center gap-2">
          <Switch
            checked={allChecked}
            onCheckedChange={(v) => toggleAll(Boolean(v))}
            aria-label="Select all teeth"
            className="shadow-md shadow-black/20 dark:shadow-black/40"
          />
          <span className="text-sm text-muted-foreground">Select all</span>
          <span className="ml-3 text-sm text-muted-foreground">{numSelected} selected</span>
        </div>
        <div className="flex items-center gap-2">
          {invoiceError ? (
            <span className="text-xs text-destructive/80 mr-2">{invoiceError}</span>
          ) : null}
          <Button
            type="button"
            variant="outline"
            className="min-w-[160px] border-emerald-500 text-emerald-700 hover:bg-emerald-50 hover:text-emerald-700"
            disabled={!canCreateInvoice}
            onClick={handleCreateInvoice}
          >
            {invoiceSubmitting ? "Generating..." : "Generate Invoice"}
          </Button>
          <Button type="button" disabled={!canOpenDialog} onClick={() => setIsDialogOpen(true)}>
            Add finding & treatment
          </Button>
        </div>
      </div>

      <div className="relative select-none px-2 md:px-6">
        {/* Cross divider */}
        <div className="pointer-events-none absolute inset-0 hidden md:block">
          <div className="absolute left-1/2 top-4 -ml-px h-[calc(100%-2rem)] w-px bg-border/70" />
          <div className="absolute left-4 top-1/2 -mt-px h-px w-[calc(100%-2rem)] bg-border/70" />
        </div>

        {/* 2x2 quadrant grid */}
        <div ref={containerRef} className="w-full">
          <div className="grid grid-cols-1 gap-4 xs:gap-6 sm:gap-8 md:grid-cols-2 md:gap-12 place-items-center">
          {/* Top-left: Upper Left (Q2) */}
          <QuadrantRow
            numbers={Q2_UPPER_LEFT}
            size={computedToothSize}
            statusByTooth={statusByTooth}
            selectedTooth={effectiveSelectedTooth}
            onToothClick={handleToothClick}
            tooltipDataByTooth={tooltipDataByTooth}
            selectedTeeth={selectedTeeth}
            onToggleTooth={handleToggleTooth}
            toothIdByNumber={toothIdByNumber}
            reverse
          />
          {/* Top-right: Upper Right (Q1) */}
          <QuadrantRow
            numbers={Q1_UPPER_RIGHT}
            size={computedToothSize}
            statusByTooth={statusByTooth}
            selectedTooth={effectiveSelectedTooth}
            onToothClick={handleToothClick}
            tooltipDataByTooth={tooltipDataByTooth}
            selectedTeeth={selectedTeeth}
            onToggleTooth={handleToggleTooth}
            toothIdByNumber={toothIdByNumber}
          />
          {/* Bottom-left: Lower Left (Q3) */}
          <QuadrantRow
            numbers={Q3_LOWER_LEFT}
            size={computedToothSize}
            statusByTooth={statusByTooth}
            selectedTooth={effectiveSelectedTooth}
            onToothClick={handleToothClick}
            tooltipDataByTooth={tooltipDataByTooth}
            selectedTeeth={selectedTeeth}
            onToggleTooth={handleToggleTooth}
            toothIdByNumber={toothIdByNumber}
            reverse
          />
          {/* Bottom-right: Lower Right (Q4) */}
          <QuadrantRow
            numbers={Q4_LOWER_RIGHT}
            size={computedToothSize}
            statusByTooth={statusByTooth}
            selectedTooth={effectiveSelectedTooth}
            onToothClick={handleToothClick}
            tooltipDataByTooth={tooltipDataByTooth}
            selectedTeeth={selectedTeeth}
            onToggleTooth={handleToggleTooth}
            toothIdByNumber={toothIdByNumber}
          />
          </div>
        </div>
      </div>
      {/* Dialog for bulk add */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add finding and treatment to {numSelected} teeth</DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            {submitError ? (
              <div className="rounded-md bg-destructive/10 p-3 text-sm text-destructive">
                {submitError}
              </div>
            ) : null}
            <div>
              <h3 className="mb-2 text-sm font-medium text-muted-foreground">Finding</h3>
              <FindingForm
                renderActions={false}
                onValuesChange={handleFindingValuesChange}
                onSubmit={async () => {}}
              />
            </div>
            <Separator />
            <div>
              <h3 className="mb-2 text-sm font-medium text-muted-foreground">Treatment</h3>
              <TreatmentForm
                renderActions={false}
                onValuesChange={handleTreatmentValuesChange}
                onSubmit={async () => {}}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="ghost" onClick={() => setIsDialogOpen(false)} disabled={submitting}>
              Cancel
            </Button>
            <Button type="button" onClick={handleConfirm} disabled={!canConfirm}>
              {submitting ? "Applying..." : "Confirm"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
