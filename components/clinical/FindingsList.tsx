"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { formatDate, formatDateLocale } from "@/lib/date-utils"
import { Skeleton } from "@/components/ui/skeleton"
import { EmptyState } from "@/components/ui/EmptyState"
import { Separator } from "@/components/ui/separator"

export interface FindingItem {
  id: number | string
  description: string
  recordedBy: string
  createdAt: string | Date
  severity?: "low" | "medium" | "high"
}

export interface FindingsListProps {
  findings: FindingItem[]
  loading?: boolean
  onSelect?: (findingId: FindingItem["id"]) => void
  className?: string
  renderActions?: (finding: FindingItem) => React.ReactNode
}

export function FindingsList({ findings, loading = false, onSelect, className, renderActions }: FindingsListProps) {
  if (loading) {
    return (
      <div className={cn("space-y-3", className)}>
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="rounded-md border p-3">
            <Skeleton className="h-4 w-4/5" />
            <div className="mt-2 flex items-center gap-2 text-[11px] text-muted-foreground">
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (!findings || findings.length === 0) {
    return (
      <EmptyState
        title="No findings recorded"
        description="Click Add Finding to document a new clinical finding for this tooth."
        className={cn("border-dashed", className)}
      />
    )
  }

  return (
    <ul className={cn("space-y-3", className)}>
      {findings.map((finding, index) => (
        <li key={finding.id} className="rounded-md border p-3">
          <div className="space-y-3">
            <button
              type="button"
              onClick={onSelect ? () => onSelect(finding.id) : undefined}
              className={cn(
                "w-full text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background",
                onSelect ? "cursor-pointer" : "cursor-default"
              )}
            >
              <div className="flex items-center gap-2">
                <p className="text-sm">{finding.description}</p>
                {finding.severity ? <SeverityBadge level={finding.severity} /> : null}
              </div>
              <div className="mt-2 flex flex-wrap items-center gap-2 text-[11px] text-muted-foreground">
                <span>{formatDate(finding.createdAt)}</span>
                <Separator orientation="vertical" className="h-3" />
                <span>Recorded by {finding.recordedBy}</span>
              </div>
            </button>
            {renderActions ? (
              <div className="flex flex-wrap items-center gap-2 border-t pt-2">
                {renderActions(finding)}
              </div>
            ) : null}
          </div>
        </li>
      ))}
    </ul>
  )
}

function SeverityBadge({ level }: { level: "low" | "medium" | "high" }) {
  const style =
    level === "high"
      ? "bg-red-500/15 text-red-600 border-red-500/30"
      : level === "medium"
      ? "bg-amber-500/15 text-amber-700 border-amber-500/30"
      : "bg-emerald-500/15 text-emerald-700 border-emerald-500/30"
  const label = level === "high" ? "High" : level === "medium" ? "Medium" : "Low"
  return (
    <span className={cn("inline-flex items-center rounded-full border px-2 py-0.5 text-[10px] font-medium", style)}>
      {label}
    </span>
  )
}



export default FindingsList


