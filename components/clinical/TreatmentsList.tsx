"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { EmptyState } from "@/components/ui/EmptyState"
import { Separator } from "@/components/ui/separator"
import { formatCurrency } from "@/lib/currency"
import { getCurrentTimestamp } from "@/lib/date-utils"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export type TreatmentStatus = "pending" | "in-progress" | "completed"

export interface TreatmentItem {
  id: number | string
  procedureName: string
  status: TreatmentStatus
  cost?: number
  notes?: string
  completedAt?: string | Date
  completedBy?: string
}

export interface FindingSummary {
  id: number | string
  description: string
}

export interface TreatmentGroup {
  finding: FindingSummary
  treatments: TreatmentItem[]
}

export interface TreatmentsListProps {
  groups: TreatmentGroup[]
  loading?: boolean
  className?: string
  onStatusChange?: (treatmentId: TreatmentItem["id"], status: TreatmentStatus) => void
  onStatusChangeDetailed?: (
    treatmentId: TreatmentItem["id"],
    status: TreatmentStatus,
    meta?: { completedAt?: string; completedBy?: string }
  ) => void
  currentUserName?: string
  showTotals?: boolean
  onTotalsChange?: (data: {
    groupTotals: { findingId: FindingSummary["id"]; total: number }[]
    grandTotal: number
  }) => void
  renderTreatmentActions?: (t: TreatmentItem) => React.ReactNode
}

export function TreatmentsList({ groups, loading = false, className, onStatusChange, onStatusChangeDetailed, currentUserName, showTotals = true, onTotalsChange, renderTreatmentActions }: TreatmentsListProps) {
  const totalTreatments = groups.reduce((acc, g) => acc + g.treatments.length, 0)
  
  const groupTotals = groups.map((g) => ({
    findingId: g.finding.id,
    total: g.treatments.reduce((sum, t) => {
      const costNumber = Number(t.cost ?? 0)
      return sum + (Number.isFinite(costNumber) ? costNumber : 0)
    }, 0),
  }))
  const grandTotal = groupTotals.reduce((sum, g) => sum + g.total, 0)

  React.useEffect(() => {
    onTotalsChange?.({ groupTotals, grandTotal })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [grandTotal, groups.length])

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        {Array.from({ length: 2 }).map((_, gi) => (
          <div key={gi} className="space-y-3">
            <Skeleton className="h-4 w-1/3" />
            {Array.from({ length: 3 }).map((__, ti) => (
              <div key={ti} className="rounded-md border p-3">
                <Skeleton className="h-4 w-3/5" />
                <div className="mt-2 flex items-center gap-2">
                  <Skeleton className="h-6 w-28" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    )
  }

  if (totalTreatments === 0) {
    return (
      <EmptyState
        title="No treatments yet"
        description="Add treatments from the planning panel or via the Add Treatment button."
        className={cn("border-dashed", className)}
      />
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {groups.map((group) => (
        <section key={group.finding.id} className="space-y-3">
          <header>
            <h3 className="text-sm font-semibold">Finding</h3>
            <p className="text-muted-foreground text-sm">{group.finding.description}</p>
          </header>
          <ul className="space-y-2">
            {group.treatments.map((t) => (
              <li key={t.id} className="rounded-md border p-3">
                <div className="flex items-start justify-between gap-3">
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-3">
                      <p className="truncate text-sm font-medium">{t.procedureName}</p>
                      <TreatmentStatusSelect
                        value={t.status}
                        onChange={(nextStatus) => {
                          const wasCompleted = t.status === "completed"
                          const willBeCompleted = nextStatus === "completed"
                          if (!wasCompleted && willBeCompleted) {
                            onStatusChangeDetailed?.(t.id, nextStatus, {
                              completedAt: getCurrentTimestamp(),
                              completedBy: currentUserName,
                            })
                          } else {
                            onStatusChangeDetailed?.(t.id, nextStatus)
                          }
                          onStatusChange?.(t.id, nextStatus)
                        }}
                      />
                    </div>
                    <div className="mt-1 text-[11px] text-muted-foreground">
                      {(() => {
                        const costNumber = Number(t.cost ?? 0)
                        return Number.isFinite(costNumber) ? `Cost: ${formatCurrency(costNumber)}` : null
                      })()}
                      {t.notes ? <span className="ml-2 truncate align-middle">• {t.notes}</span> : null}
                    </div>
                  </div>
                  {renderTreatmentActions ? (
                    <div className="shrink-0">{renderTreatmentActions(t)}</div>
                  ) : null}
                </div>
              </li>
            ))}
          </ul>
          {showTotals ? (
            <div className="flex items-center justify-end text-sm">
              <div className="rounded-md border bg-muted/30 px-2 py-1">
                Subtotal: <span className="font-medium">{formatCurrency(groupTotals.find((g) => g.findingId === group.finding.id)?.total ?? 0)}</span>
              </div>
            </div>
          ) : null}
          <Separator />
        </section>
      ))}
      {showTotals ? (
        <div className="flex items-center justify-end text-sm">
          <div className="rounded-md border bg-muted/50 px-3 py-1.5">
            Total: <span className="font-semibold">{formatCurrency(grandTotal)}</span>
          </div>
        </div>
      ) : null}
    </div>
  )
}

function TreatmentStatusSelect({ value, onChange }: { value: TreatmentStatus; onChange?: (s: TreatmentStatus) => void }) {
  return (
    <Select value={value} onValueChange={(v) => onChange?.(v as TreatmentStatus)}>
      <SelectTrigger className="h-7 w-36 text-xs">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="pending">Pending</SelectItem>
        <SelectItem value="in-progress">In progress</SelectItem>
        <SelectItem value="completed">Completed</SelectItem>
      </SelectContent>
    </Select>
  )
}

// currency handled by shared helper

export default TreatmentsList


