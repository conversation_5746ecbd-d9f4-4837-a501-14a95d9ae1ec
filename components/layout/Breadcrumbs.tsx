import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

export interface CrumbItem {
  label: string
  href?: string
}

export interface BreadcrumbsProps {
  items: CrumbItem[]
  className?: string
}

export function Breadcrumbs({ items, className }: BreadcrumbsProps) {
  if (!items || items.length === 0) return null

  const lastIndex = items.length - 1

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {items.map((item, index) => {
          const isLast = index === lastIndex
          return (
            <React.Fragment key={`${item.label}-${index}`}>
              <BreadcrumbItem>
                {isLast ? (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                ) : item.href ? (
                  <BreadcrumbLink asChild>
                    <Link href={item.href}>{item.label}</Link>
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbLink>{item.label}</BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!isLast && <BreadcrumbSeparator />}
            </React.Fragment>
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}

function toTitleCase(segment: string): string {
  return segment
    .replace(/[-_]+/g, " ")
    .replace(/\b\w/g, (c) => c.toUpperCase())
}

function mapSegmentLabel(segment: string): string {
  const map: Record<string, string> = {
    dashboard: "Dashboard",
    patients: "Patients",
    billing: "Billing",
    reports: "Reports",
    invoices: "Invoices",
  }
  return map[segment] ?? toTitleCase(segment)
}

export function generateBreadcrumbsFromPathname(pathname: string): CrumbItem[] {
  if (!pathname || pathname === "/") return []

  const segments = pathname.split("/").filter(Boolean)
  const crumbs: CrumbItem[] = []
  let hrefAcc = ""

  segments.forEach((seg, index) => {
    hrefAcc += `/${seg}`
    const isLast = index === segments.length - 1
    let label = mapSegmentLabel(seg)

    // Special-case invoice detail label: /dashboard/billing/invoices/[id]
    if (
      segments[index - 2] === "billing" &&
      segments[index - 1] === "invoices" &&
      index === segments.length - 1
    ) {
      label = `Invoice #${seg}`
    }

    // Special-case patient detail fallback label: /dashboard/patients/[id]
    if (
      segments[index - 1] === "patients" &&
      index === segments.length - 1
    ) {
      // If page-level code wants to override with patient name, it can render a custom crumb before children
      label = /^\d+$/.test(seg) ? `Patient #${seg}` : toTitleCase(seg)
    }

    crumbs.push({
      label,
      href: isLast ? undefined : hrefAcc,
    })
  })

  return crumbs
}

export function RouteBreadcrumbs({ className }: { className?: string }) {
  const pathname = usePathname()
  const items = React.useMemo(() => generateBreadcrumbsFromPathname(pathname), [pathname])
  return <Breadcrumbs items={items} className={className} />
}
