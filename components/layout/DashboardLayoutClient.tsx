"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { SubscriptionGuard } from "@/components/auth/SubscriptionGuard"
import { useAuth } from "@/components/auth/AuthGuard"
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/layout/AppSidebar"
import { RouteBreadcrumbs } from "@/components/layout/Breadcrumbs"
import { Toaster } from "@/components/ui/sonner"

interface DashboardLayoutClientProps {
  children: React.ReactNode
}

export function DashboardLayoutClient({ children }: DashboardLayoutClientProps) {
  const authState = useAuth()

  return (
    <SubscriptionGuard tenantId={authState.user?.tenantId}>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset className="min-h-screen">
          <DashboardHeader />
          <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="mb-4">
              <RouteBreadcrumbs />
            </div>
            {children}
          </main>
          <Toaster richColors closeButton />
        </SidebarInset>
      </SidebarProvider>
    </SubscriptionGuard>
  )
}

/**
 * Dashboard Header Component
 * Common header for all dashboard pages
 */
function DashboardHeader() {
  return (
    <header className="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center space-x-4">
            <SidebarTrigger className="md:size-7 size-9" />
            <h1 className="text-xl font-semibold tracking-tight">
              Dental and Diagnosis
            </h1>
          </div>
          <DashboardUserMenu />
        </div>
      </div>
    </header>
  )
}

/**
 * Dashboard User Menu Component
 * User profile and logout functionality
 */
function DashboardUserMenu() {
  return (
    <div className="flex items-center space-x-4">
      <LogoutButton />
    </div>
  )
}

/**
 * Logout Button Component
 * Handles the logout process by calling the API and redirecting
 */
function LogoutButton() {
  const router = useRouter()
  const [isLoggingOut, setIsLoggingOut] = React.useState(false)

  const handleLogout = async () => {
    if (isLoggingOut) return // Prevent double-clicks
    
    setIsLoggingOut(true)
    
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      })
      
      const result = await response.json()
      
      if (result.success) {
        router.push(result.redirectUrl || '/auth/login')
      } else {
        console.error('Logout failed:', result.message)
        router.push('/auth/login')
      }
    } catch (error) {
      console.error('Logout error:', error)
      router.push('/auth/login')
    }
  }

  return (
    <button 
      onClick={handleLogout}
      disabled={isLoggingOut}
      className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors disabled:opacity-50"
    >
      {isLoggingOut ? 'Logging out...' : 'Logout'}
    </button>
  )
}
