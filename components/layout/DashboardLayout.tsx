"use client"

import * as React from "react"

import { AppSidebar } from "@/components/layout/AppSidebar"
import { Breadcrumbs, CrumbItem } from "@/components/layout/Breadcrumbs"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"

export interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  className?: string
  breadcrumbs?: CrumbItem[]
}

export function DashboardLayout({ children, title, className, breadcrumbs }: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className={className}>
        <header className="flex h-14 items-center gap-3 border-b px-4">
          <SidebarTrigger className="md:size-7 size-9" />
          {breadcrumbs && breadcrumbs.length > 0 ? (
            <Breadcrumbs items={breadcrumbs} />
          ) : null}
          {title ? (
            <h1 className="text-base font-semibold leading-none tracking-tight">
              {title}
            </h1>
          ) : null}
        </header>
        <div className="flex-1 p-4">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
