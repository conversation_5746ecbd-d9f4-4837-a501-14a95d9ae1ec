"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Users, Calendar, CreditCard, BarChart3, User, Setting<PERSON> } from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarFooter,
} from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"
import { SubscriptionStatus } from "./SubscriptionStatus"
import { useAuth } from "@/components/auth/AuthGuard"

export interface AppSidebarProps {
  className?: string
  notificationCounts?: {
    dashboard?: number
    patients?: number
    appointments?: number
    billing?: number
    reports?: number
  }
}

export interface SidebarItem {
  title: string
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>
  href: string
  badge?: string | number
}

const sidebarItems: SidebarItem[] = [
  { title: "Dashboard", icon: Home, href: "/dashboard" },
  { title: "Patients", icon: Users, href: "/dashboard/patients" },
  { title: "Appointments", icon: Calendar, href: "/dashboard/appointments" },
  { title: "Billing", icon: CreditCard, href: "/dashboard/billing" },
  { title: "Reports", icon: BarChart3, href: "/dashboard/reports" },
  { title: "Profile", icon: User, href: "/dashboard/profile" },
  { title: "Promotion", icon: Settings, href: "/dashboard/promotion" },
  { title: "Settings", icon: Settings, href: "/dashboard/settings" },
]

export function AppSidebar({ className, notificationCounts }: AppSidebarProps) {
  const pathname = usePathname()
  const authState = useAuth()
  
  return (
    <Sidebar className={className}>
      <SidebarHeader />
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {sidebarItems.map((item) => {
                const isActive = item.href === "/dashboard"
                  ? pathname === "/dashboard"
                  : pathname.startsWith(item.href)
                const count =
                  item.title === "Dashboard"
                    ? notificationCounts?.dashboard
                    : item.title === "Patients"
                    ? notificationCounts?.patients
                    : item.title === "Appointments"
                    ? notificationCounts?.appointments
                    : item.title === "Billing"
                    ? notificationCounts?.billing
                    : item.title === "Reports"
                    ? notificationCounts?.reports
                    : undefined
                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton asChild size="lg" isActive={isActive}>
                      <Link href={item.href}>
                        <item.icon className="h-5 w-5" />
                        <span className={cn("flex items-center gap-2", "md:text-sm text-base")}> 
                          {item.title}
                          {typeof count === "number" && count > 0 ? (
                            <span className="inline-flex min-w-[20px] items-center justify-center rounded-full bg-primary px-1 text-[10px] font-medium text-primary-foreground">
                              {count > 99 ? "99+" : count}
                            </span>
                          ) : null}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <div className="flex flex-col items-center justify-center p-2">
          <SubscriptionStatus 
            tenantId={authState.user?.tenantId} 
            className="w-full"
          />
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
