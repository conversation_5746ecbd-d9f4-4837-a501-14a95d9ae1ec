"use client"

import React, { useEffect, useState, useCallback } from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Clock, XCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { formatDate } from "@/lib/date-utils"

interface SubscriptionStatusData {
  status: 'active' | 'inactive' | 'overdue' | 'grace_period'
  message: string
  endDate?: Date
  daysRemaining?: number
  daysOverdue?: number
}

interface SubscriptionStatusProps {
  tenantId?: string
  className?: string
}

export function SubscriptionStatus({ tenantId, className }: SubscriptionStatusProps) {
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionStatusData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchSubscriptionStatus = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/tenant/subscription-status?tenantId=${tenantId}`, {
        method: 'GET',
        credentials: 'include',
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          setSubscriptionData(result.data)
        }
      }
    } catch (error) {
      console.error('Error fetching subscription status:', error)
    } finally {
      setIsLoading(false)
    }
  }, [tenantId])

  useEffect(() => {
    if (!tenantId) {
      setIsLoading(false)
      return
    }

    fetchSubscriptionStatus()
  }, [tenantId, fetchSubscriptionStatus])

  if (isLoading) {
    return (
      <div className={cn("p-3 border rounded-lg bg-muted/20", className)}>
        <div className="flex items-center gap-2">
          <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-primary"></div>
          <div className="h-3 w-24 bg-muted animate-pulse rounded"></div>
        </div>
      </div>
    )
  }

  if (!subscriptionData) {
    return null
  }

  const getStatusIcon = () => {
    switch (subscriptionData.status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'grace_period':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'inactive':
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <XCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColors = () => {
    switch (subscriptionData.status) {
      case 'active':
        return {
          border: 'border-green-200',
          bg: 'bg-green-50',
          text: 'text-green-800'
        }
      case 'grace_period':
        return {
          border: 'border-yellow-200',
          bg: 'bg-yellow-50',
          text: 'text-yellow-800'
        }
      case 'overdue':
        return {
          border: 'border-red-200',
          bg: 'bg-red-50',
          text: 'text-red-800'
        }
      case 'inactive':
        return {
          border: 'border-gray-200',
          bg: 'bg-gray-50',
          text: 'text-gray-600'
        }
      default:
        return {
          border: 'border-gray-200',
          bg: 'bg-gray-50',
          text: 'text-gray-600'
        }
    }
  }

  const statusColors = getStatusColors()

  return (
    <div className={cn(
      "p-3 border rounded-lg transition-colors",
      statusColors.border,
      statusColors.bg,
      className
    )}>
      <div className="flex items-center gap-2 mb-1">
        {getStatusIcon()}
        <span className={cn("text-xs font-medium", statusColors.text)}>
          Subscription
        </span>
      </div>
      <p className={cn("text-xs", statusColors.text)}>
        {subscriptionData.message}
      </p>
      {subscriptionData.endDate && (
        <p className={cn("text-xs opacity-75 mt-1", statusColors.text)}>
          {subscriptionData.status === 'active' 
            ? `Until ${formatDate(subscriptionData.endDate)}`
            : `Ended ${formatDate(subscriptionData.endDate)}`
          }
        </p>
      )}
    </div>
  )
}
