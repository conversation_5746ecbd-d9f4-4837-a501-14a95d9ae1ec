"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { <PERSON>, CheckCircle2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>r, Credit<PERSON>ard } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

export type NotificationType = "overdue_treatment" | "unpaid_invoice" | "system"

export interface NotificationItem {
  id: string
  type: NotificationType
  title: string
  description?: string
  createdAt: string | Date
  read?: boolean
  href?: string
}

export interface NotificationCenterProps {
  notifications: NotificationItem[]
  onMarkRead?: (id: string) => void | Promise<void>
  onMarkAllRead?: () => void | Promise<void>
  onNavigate?: (href: string) => void
  className?: string
}

function TypeIcon({ type }: { type: NotificationType }) {
  if (type === "overdue_treatment") return <Timer className="h-4 w-4 text-amber-500" />
  if (type === "unpaid_invoice") return <CreditCard className="h-4 w-4 text-red-500" />
  return <AlertTriangle className="h-4 w-4 text-blue-500" />
}

export default function NotificationCenter({
  notifications,
  onMarkRead,
  onMarkAllRead,
  onNavigate,
  className,
}: NotificationCenterProps) {
  const router = useRouter()
  const [items, setItems] = React.useState<NotificationItem[]>(notifications)

  React.useEffect(() => {
    setItems(notifications)
  }, [notifications])

  const markReadLocal = async (id: string) => {
    setItems((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)))
    try {
      await onMarkRead?.(id)
    } catch {
      // revert on error
      setItems((prev) => prev.map((n) => (n.id === id ? { ...n, read: false } : n)))
    }
  }

  const markAllReadLocal = async () => {
    const prev = items
    setItems((curr) => curr.map((n) => ({ ...n, read: true })))
    try {
      await onMarkAllRead?.()
    } catch {
      setItems(prev)
    }
  }

  const navigate = (href?: string) => {
    if (!href) return
    if (onNavigate) return onNavigate(href)
    router.push(href)
  }

  const unreadCount = items.filter((n) => !n.read).length

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          <CardTitle>Notifications</CardTitle>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground">{unreadCount} unread</span>
          <Button variant="outline" size="sm" onClick={markAllReadLocal} disabled={unreadCount === 0}>
            Mark all read
          </Button>
        </div>
      </CardHeader>
      <Separator />
      <CardContent className="p-0">
        {items.length === 0 ? (
          <div className="p-4 text-sm text-muted-foreground">No notifications</div>
        ) : (
          <ul className="divide-y">
            {items.map((n) => (
              <li key={n.id} className="flex items-start gap-3 p-4">
                <div className="mt-0.5">
                  <TypeIcon type={n.type} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">
                      <button
                        type="button"
                        className="text-left hover:underline"
                        onClick={() => navigate(n.href)}
                      >
                        {n.title}
                      </button>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(n.createdAt).toLocaleString()}
                    </div>
                  </div>
                  {n.description ? (
                    <div className="mt-1 text-sm text-muted-foreground">{n.description}</div>
                  ) : null}
                  {!n.read ? (
                    <div className="mt-2">
                      <Button variant="ghost" size="sm" onClick={() => markReadLocal(n.id)}>
                        <CheckCircle2 className="mr-1 h-4 w-4" /> Mark read
                      </Button>
                    </div>
                  ) : null}
                </div>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  )
}
