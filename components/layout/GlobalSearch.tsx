"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export type GlobalSearchType = "phone" | "name" | "id"

export interface GlobalSearchCriteria {
  query: string
  searchType: GlobalSearchType
}

export interface GlobalSearchResult {
  id: string | number
  label: string
  subLabel?: string
  href: string
}

export interface GlobalSearchProps {
  onSearch: (criteria: GlobalSearchCriteria) => void | Promise<void>
  loading?: boolean
  defaultType?: GlobalSearchType
  defaultQuery?: string
  className?: string
  fetchResults?: (criteria: GlobalSearchCriteria) => Promise<GlobalSearchResult[]>
  onNavigate?: (href: string) => void
  debounceMs?: number
}

function GlobalSearchInner({
  onSearch,
  loading,
  defaultType = "phone",
  defaultQuery = "",
  className,
  fetchResults,
  onNavigate,
  debounceMs = 250,
}: GlobalSearchProps) {
  const router = useRouter()
  const [type, setType] = React.useState<GlobalSearchType>(defaultType)
  const [query, setQuery] = React.useState<string>(defaultQuery)
  const [open, setOpen] = React.useState<boolean>(false)
  const [results, setResults] = React.useState<GlobalSearchResult[]>([])
  const [highlightedIndex, setHighlightedIndex] = React.useState<number>(-1)
  const [searching, setSearching] = React.useState<boolean>(false)
  const inputRef = React.useRef<HTMLInputElement | null>(null)
  const containerRef = React.useRef<HTMLDivElement | null>(null)

  const navigateTo = React.useCallback(
    (href: string) => {
      if (onNavigate) {
        onNavigate(href)
      } else {
        router.push(href)
      }
    },
    [onNavigate, router]
  )

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault()
    const trimmed = query.trim()
    if (!trimmed) return
    void onSearch({ query: trimmed, searchType: type })
    if (fetchResults) setOpen(true)
  }

  // Debounced fetching of results when query changes
  React.useEffect(() => {
    if (!fetchResults) return
    const trimmed = query.trim()
    if (!trimmed) {
      setResults([])
      setOpen(false)
      setHighlightedIndex(-1)
      return
    }
    const t = setTimeout(async () => {
      setSearching(true)
      try {
        const data = await fetchResults({ query: trimmed, searchType: type })
        setResults(Array.isArray(data) ? data : [])
        setOpen(true)
        setHighlightedIndex(data && data.length > 0 ? 0 : -1)
      } finally {
        setSearching(false)
      }
    }, debounceMs)
    return () => clearTimeout(t)
  }, [query, type, fetchResults, debounceMs])

  // Close dropdown on outside click
  React.useEffect(() => {
    function onDocumentClick(e: MouseEvent) {
      if (!open) return
      if (containerRef.current && e.target instanceof Node && !containerRef.current.contains(e.target)) {
        setOpen(false)
      }
    }
    document.addEventListener("mousedown", onDocumentClick)
    return () => document.removeEventListener("mousedown", onDocumentClick)
  }, [open])

  // Keyboard shortcuts to focus search
  React.useEffect(() => {
    function onKeydown(e: KeyboardEvent) {
      const tag = (e.target as HTMLElement)?.tagName
      const isTyping = tag === "INPUT" || tag === "TEXTAREA" || (e.target as HTMLElement)?.isContentEditable
      // Cmd+K / Ctrl+K
      if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === "k") {
        e.preventDefault()
        inputRef.current?.focus()
        setOpen(true)
      }
      // Quick focus with '/'
      if (!isTyping && e.key === "/") {
        e.preventDefault()
        inputRef.current?.focus()
        setOpen(true)
      }
    }
    window.addEventListener("keydown", onKeydown)
    return () => window.removeEventListener("keydown", onKeydown)
  }, [])

  // Keyboard navigation within dropdown
  const onInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!open || results.length === 0) return
    if (e.key === "ArrowDown") {
      e.preventDefault()
      setHighlightedIndex((prev) => (prev + 1) % results.length)
    } else if (e.key === "ArrowUp") {
      e.preventDefault()
      setHighlightedIndex((prev) => (prev - 1 + results.length) % results.length)
    } else if (e.key === "Enter") {
      const item = results[highlightedIndex]
      if (item) {
        e.preventDefault()
        navigateTo(item.href)
        setOpen(false)
      }
    } else if (e.key === "Escape") {
      setOpen(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className={className}>
      <div ref={containerRef} className="relative flex items-center gap-2">
        <Select value={type} onValueChange={(v) => setType(v as GlobalSearchType)}>
          <SelectTrigger className="h-9 w-[120px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="phone">By Phone</SelectItem>
            <SelectItem value="name">By Name</SelectItem>
            <SelectItem value="id">By ID</SelectItem>
          </SelectContent>
        </Select>

        <Input
          ref={inputRef}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => {
            if (results.length > 0) setOpen(true)
          }}
          onKeyDown={onInputKeyDown}
          placeholder={
            type === "phone"
              ? "Search phone number"
              : type === "name"
              ? "Search name"
              : "Search patient ID"
          }
          className="h-9 w-[240px]"
          aria-label="Global patient search"
        />

        <Button type="submit" disabled={loading} className="h-9">
          {loading || searching ? "Searching..." : "Search"}
        </Button>

        {open && results.length > 0 ? (
          <div
            role="listbox"
            aria-label="Search results"
            className="absolute left-[120px] top-[42px] z-50 w-[240px] rounded-md border bg-popover text-popover-foreground shadow-md"
          >
            <ul className="max-h-72 overflow-auto py-1 text-sm">
              {results.map((item, index) => (
                <li key={String(item.id)}>
                  <button
                    type="button"
                    role="option"
                    aria-selected={highlightedIndex === index}
                    onMouseEnter={() => setHighlightedIndex(index)}
                    onClick={() => {
                      navigateTo(item.href)
                      setOpen(false)
                    }}
                    className={`flex w-full items-start gap-2 px-3 py-2 text-left hover:bg-accent hover:text-accent-foreground ${
                      highlightedIndex === index ? "bg-accent text-accent-foreground" : ""
                    }`}
                  >
                    <div className="flex flex-col">
                      <span className="font-medium leading-tight">{item.label}</span>
                      {item.subLabel ? (
                        <span className="text-xs text-muted-foreground">{item.subLabel}</span>
                      ) : null}
                    </div>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        ) : null}
      </div>
    </form>
  )
}

const GlobalSearch = React.memo(GlobalSearchInner)
export default GlobalSearch
