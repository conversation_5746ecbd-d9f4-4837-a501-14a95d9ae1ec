"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Calendar as CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { DateRange } from "react-day-picker"

export type FilterType = "text" | "select" | "date" | "dateRange" | "number"

export type FilterValue = string | number | Date | { from?: Date; to?: Date } | undefined

export interface FilterConfig {
  key: string
  label: string
  type: FilterType
  options?: { label: string; value: string | number }[]
  placeholder?: string
}

export interface TableFiltersProps {
  filters: FilterConfig[]
  values: Record<string, FilterValue>
  onChange: (values: Record<string, FilterValue>) => void
  onReset: () => void
  className?: string
}

export function TableFilters({ filters, values, onChange, onReset, className }: TableFiltersProps) {
  const setValue = (key: string, value: FilterValue) => {
    onChange({ ...values, [key]: value })
  }

  const renderFilter = (filter: FilterConfig) => {
    const value = values[filter.key]
    switch (filter.type) {
      case "text":
        return (
          <Input
            placeholder={filter.placeholder ?? filter.label}
            value={value as string}
            onChange={(e) => setValue(filter.key, e.target.value)}
            className="h-9 w-48"
          />
        )
      case "number":
        return (
          <Input
            type="number"
            placeholder={filter.placeholder ?? filter.label}
              value={value as string}
            onChange={(e) => setValue(filter.key, e.target.value === "" ? undefined : Number(e.target.value))}
            className="h-9 w-40"
          />
        )
      case "select":
        return (
          <Select value={value as string} onValueChange={(v) => setValue(filter.key, v)}>
            <SelectTrigger className="h-9 w-48">
              <SelectValue placeholder={filter.placeholder ?? filter.label} />
            </SelectTrigger>
            <SelectContent>
              {filter.options?.map((opt) => (
                <SelectItem key={String(opt.value)} value={String(opt.value)}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      case "date":
        return (
          <DatePicker
            value={value as Date}
            onChange={(d) => setValue(filter.key, d)}
            placeholder={filter.placeholder ?? filter.label}
          />
        )
      case "dateRange":
        return (
          <DateRangePicker
            value={value as { from?: Date; to?: Date }}
            onChange={(r) => setValue(filter.key, r)}
            placeholder={filter.placeholder ?? filter.label}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className={cn("flex flex-wrap items-center gap-2", className)}>
      {filters.map((f) => (
        <div key={f.key} className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground">{f.label}</span>
          {renderFilter(f)}
        </div>
      ))}
      <Button variant="outline" size="sm" onClick={onReset} className="ml-2 h-8">
        Reset
      </Button>
    </div>
  )
}

interface DatePickerProps {
  value?: Date | undefined
  onChange: (value?: Date) => void
  placeholder?: string
}

function DatePicker({ value, onChange, placeholder }: DatePickerProps) {
  const [open, setOpen] = React.useState(false)
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="h-9 w-48 justify-start text-left font-normal">
          <CalendarIcon className="mr-2 h-4 w-4" />
          {value ? format(value, "PPP") : <span className="text-muted-foreground">{placeholder ?? "Pick a date"}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="p-0">
        <Calendar
          mode="single"
          selected={value}
          onSelect={(d) => {
            onChange(d)
            setOpen(false)
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}

interface DateRangePickerProps {
  value?: { from?: Date; to?: Date }
  onChange: (value?: { from?: Date; to?: Date }) => void
  placeholder?: string
}

function DateRangePicker({ value, onChange, placeholder }: DateRangePickerProps) {
  const [open, setOpen] = React.useState(false)
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="h-9 w-64 justify-start text-left font-normal">
          <CalendarIcon className="mr-2 h-4 w-4" />
          {value?.from && value?.to ? (
            <span>
              {format(value.from, "LLL dd, y")} - {format(value.to, "LLL dd, y")}
            </span>
          ) : (
            <span className="text-muted-foreground">{placeholder ?? "Pick a date range"}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="p-0">
        <Calendar
          mode="range"
          selected={value as DateRange | undefined}
          onSelect={(range) => {
            onChange(range)
          }}
          numberOfMonths={2}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}
