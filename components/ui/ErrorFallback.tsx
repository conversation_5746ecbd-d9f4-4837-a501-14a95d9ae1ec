"use client"

import * as React from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"

export interface ErrorFallbackProps {
  error: Error
  reset: () => void
  homeHref?: string
  showHomeLink?: boolean
  showBackButton?: boolean
  extraActions?: React.ReactNode
}

export function ErrorFallback({ error, reset, homeHref = "/dashboard", showHomeLink = true, showBackButton = true, extraActions }: ErrorFallbackProps) {
  const router = useRouter()
  return (
    <div role="alert" aria-live="assertive" className="bg-card text-card-foreground mx-auto my-6 max-w-xl rounded-lg border p-6 shadow-sm">
      <div className="mb-2 text-lg font-semibold">Something went wrong</div>
      <p className="mb-4 text-sm text-muted-foreground">
        {error.message || "An unexpected error occurred."}
      </p>
      <div className="flex flex-wrap items-center gap-2">
        <Button onClick={reset}>Try again</Button>
        {showBackButton ? (
          <Button variant="outline" onClick={() => router.back()}>Go back</Button>
        ) : null}
        {showHomeLink ? (
          <Button variant="outline" asChild>
            <Link href={homeHref}>Dashboard</Link>
          </Button>
        ) : null}
        {extraActions}
      </div>
    </div>
  )
}

export default ErrorFallback


