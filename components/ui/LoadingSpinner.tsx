"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export type LoadingSpinnerSize = "sm" | "md" | "lg"

export interface LoadingSpinnerProps {
  size?: LoadingSpinnerSize
  label?: string
  centered?: boolean
  className?: string
}

const sizeToClass: Record<LoadingSpinnerSize, string> = {
  sm: "h-4 w-4",
  md: "h-6 w-6",
  lg: "h-8 w-8",
}

export const LoadingSpinner = React.memo(function LoadingSpinner({
  size = "md",
  label = "Loading",
  centered = false,
  className,
}: LoadingSpinnerProps) {
  return (
    <div
      role="status"
      aria-busy="true"
      aria-label={label}
      className={cn("inline-flex items-center justify-center", centered && "w-full", className)}
    >
      <span
        className={cn(
          "inline-block rounded-full border-2 border-muted-foreground/30 border-t-primary animate-spin",
          sizeToClass[size]
        )}
      />
      <span className="sr-only">{label}</span>
    </div>
  )
})
