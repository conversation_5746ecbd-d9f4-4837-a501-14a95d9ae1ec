"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"

export interface ErrorFallbackProps {
  error: Error
  reset: () => void
}

export interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, info: React.ErrorInfo) => void
}

interface ErrorBoundaryState {
  error: Error | null
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  state: ErrorBoundaryState = { error: null }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { error }
  }

  componentDidCatch(error: Error, info: React.ErrorInfo) {
    this.props.onError?.(error, info)
    // Optionally log to an error reporting service here
  }

  private reset = () => {
    this.setState({ error: null })
  }

  render() {
    const { error } = this.state
    const { children, fallback: Fallback } = this.props

    if (error) {
      if (Fallback) {
        return <Fallback error={error} reset={this.reset} />
      }

      return (
        <div className="bg-card text-card-foreground mx-auto my-6 max-w-xl rounded-lg border p-6 shadow-sm">
          <div className="mb-2 text-lg font-semibold">Something went wrong</div>
          <p className="mb-4 text-sm text-muted-foreground">
            {error.message || "An unexpected error occurred."}
          </p>
          <Button onClick={this.reset} variant="default">
            Try again
          </Button>
        </div>
      )
    }

    return children
  }
}
