"use client"

import * as React from "react"
import { ChevronsUpDown, Check } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"

export type ComboItem = { value: string; label: string }
export type ComboGroup = { label: string; items: ComboItem[] }

export interface ComboboxProps {
  value: string | null
  onChange: (value: string) => void
  groups: ComboGroup[]
  placeholder?: string
  emptyMessage?: string
  disabled?: boolean
  className?: string
}

export function Combobox({ value, onChange, groups, placeholder = "Search...", emptyMessage = "No results. Press Enter to use input.", disabled, className, }: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [query, setQuery] = React.useState("")

  const flatItems = React.useMemo(() => groups.flatMap((g) => g.items), [groups])

  const selectedLabel = value ?? ""

  function handleSelect(val: string) {
    onChange(val)
    setOpen(false)
  }

  function handleEnterFreeText(e: React.KeyboardEvent<HTMLInputElement>) {
    if (e.key === "Enter") {
      e.preventDefault()
      const text = query.trim()
      if (text.length > 0) {
        onChange(text)
        setOpen(false)
      }
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          disabled={disabled}
        >
          <span className={cn("truncate text-left", !selectedLabel && "text-muted-foreground")}>{selectedLabel || placeholder}</span>
          <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
        <Command>
          <CommandInput
            placeholder={placeholder}
            value={query}
            onValueChange={setQuery}
            onKeyDown={handleEnterFreeText}
          />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            {groups.map((group) => (
              <CommandGroup key={group.label} heading={group.label}>
                {group.items.map((item) => (
                  <CommandItem key={item.value} value={item.label} onSelect={() => handleSelect(item.label)}>
                    <Check className={cn("mr-2 size-4", item.label === selectedLabel ? "opacity-100" : "opacity-0")} />
                    <span className="truncate">{item.label}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

export default Combobox


