"use client"

import * as React from "react"
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"

export interface PaginationState {
  pageIndex: number
  pageSize: number
  totalItems?: number
}

export interface TablePaginationProps {
  state: PaginationState
  onChange: (state: PaginationState) => void
  className?: string
  pageSizeOptions?: number[]
}

export function TablePagination({ state, onChange, className, pageSizeOptions = [10, 25, 50, 100] }: TablePaginationProps) {
  const totalPages = React.useMemo(() => {
    if (!state.totalItems || state.totalItems <= 0) return undefined
    return Math.max(1, Math.ceil(state.totalItems / state.pageSize))
  }, [state.totalItems, state.pageSize])

  const canPrev = state.pageIndex > 0
  const canNext = totalPages ? state.pageIndex < totalPages - 1 : true

  const goTo = (pageIndex: number) => {
    if (totalPages !== undefined) {
      pageIndex = Math.min(Math.max(0, pageIndex), totalPages - 1)
    } else {
      pageIndex = Math.max(0, pageIndex)
    }
    onChange({ ...state, pageIndex })
  }

  return (
    <div className={cn("flex w-full items-center justify-between gap-3", className)}>
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">Rows per page</span>
        <Select
          value={String(state.pageSize)}
          onValueChange={(v) => onChange({ ...state, pageSize: Number(v), pageIndex: 0 })}
        >
          <SelectTrigger className="h-8 w-[90px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {pageSizeOptions.map((size) => (
              <SelectItem key={size} value={String(size)}>
                {size}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious onClick={() => canPrev && goTo(state.pageIndex - 1)} aria-disabled={!canPrev} />
          </PaginationItem>
          {totalPages !== undefined && (
            <>
              {Array.from({ length: totalPages }).map((_, i) => (
                <PaginationItem key={i}>
                  <PaginationLink isActive={i === state.pageIndex} onClick={() => goTo(i)}>
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
            </>
          )}
          <PaginationItem>
            <PaginationNext onClick={() => canNext && goTo(state.pageIndex + 1)} aria-disabled={!canNext} />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  )
}
