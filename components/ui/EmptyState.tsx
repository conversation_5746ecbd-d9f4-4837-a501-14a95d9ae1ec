"use client"

import * as React from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

export interface EmptyStateAction {
  label: string
  href?: string
  onClick?: () => void
  variant?: "default" | "outline" | "secondary" | "ghost" | "destructive"
}

export interface EmptyStateProps {
  title: string
  description?: string
  icon?: React.ReactNode
  actions?: EmptyStateAction[]
  className?: string
}

export function EmptyState({ title, description, icon, actions = [], className }: EmptyStateProps) {
  return (
    <div
      className={cn(
        "bg-card text-card-foreground mx-auto my-10 max-w-md rounded-lg border p-8 text-center shadow-sm",
        className
      )}
      role="status"
      aria-live="polite"
    >
      {icon ? <div className="mx-auto mb-4 text-muted-foreground [&>svg]:mx-auto [&>svg]:h-10 [&>svg]:w-10">{icon}</div> : null}
      <h2 className="text-lg font-semibold">{title}</h2>
      {description ? (
        <p className="text-muted-foreground mt-2 text-sm">{description}</p>
      ) : null}
      {actions.length > 0 ? (
        <div className="mt-6 flex flex-wrap items-center justify-center gap-2">
          {actions.map((action, idx) => {
            const btn = (
              <Button key={`${action.label}-${idx}`} variant={action.variant || (idx === 0 ? "default" : "outline")} onClick={action.onClick}>
                {action.label}
              </Button>
            )
            return action.href ? (
              <Link key={`${action.label}-${idx}`} href={action.href} passHref legacyBehavior>
                <a>{btn}</a>
              </Link>
            ) : (
              btn
            )
          })}
        </div>
      ) : null}
    </div>
  )
}
