"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export type InvoiceStatus = "draft" | "sent" | "partially_paid" | "paid" | "overdue"

export function InvoiceStatusBadge({ status, className }: { status: InvoiceStatus; className?: string }) {
  const label =
    status === "paid"
      ? "PAID"
      : status === "partially_paid"
      ? "PARTIALLY PAID"
      : status === "sent"
      ? "SENT"
      : status === "overdue"
      ? "OVERDUE"
      : "DRAFT"
  const cls =
    status === "paid"
      ? "bg-emerald-600 text-white"
      : status === "partially_paid"
      ? "bg-amber-600 text-white"
      : status === "sent"
      ? "bg-sky-600 text-white"
      : status === "overdue"
      ? "bg-red-600 text-white"
      : "bg-muted text-foreground"
  return (
    <span className={cn("inline-flex items-center rounded-full px-2 py-0.5 text-[11px] font-semibold", cls, className)}>
      {label}
    </span>
  )
}

export default InvoiceStatusBadge


