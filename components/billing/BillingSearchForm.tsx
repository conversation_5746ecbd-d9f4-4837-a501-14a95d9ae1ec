"use client";

import * as React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { formatDateLocale } from "@/lib/date-utils";
import type { InvoiceStatus } from "./InvoiceTable";
import type { DateRange } from "react-day-picker";

export interface BillingSearchFilters {
  patient?: string;
  status?: InvoiceStatus | "all";
  dateRange?: DateRange;
}

export interface BillingSearchFormProps {
  filters: BillingSearchFilters;
  onFiltersChange: (filters: BillingSearchFilters) => void;
  className?: string;
}

export function BillingSearchForm({
  filters,
  onFiltersChange,
  className,
}: BillingSearchFormProps) {
  return (
    <div className={cn("flex flex-wrap items-end gap-2", className)}>
      <div className="flex flex-col">
        <label className="text-xs text-muted-foreground">Patient</label>
        <Input
          value={filters.patient ?? ""}
          onChange={(e) => onFiltersChange({ ...filters, patient: e.target.value })}
          placeholder="Search by name"
          className="h-9 w-56"
        />
      </div>

      <div className="flex flex-col">
        <label className="text-xs text-muted-foreground">Status</label>
        <Select
          value={filters.status ?? "all"}
          onValueChange={(v) =>
            onFiltersChange({ ...filters, status: v as BillingSearchFilters["status"] })
          }
        >
          <SelectTrigger className="h-9 w-40">
            <SelectValue placeholder="All" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="partially_paid">Partially Paid</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <DateRangePicker
        label="Date range"
        dateRange={filters.dateRange}
        onChange={(dateRange) =>
          onFiltersChange({ ...filters, dateRange })
        }
      />

      <Button
        variant="outline"
        className="ml-auto"
        onClick={() => onFiltersChange({})}
      >
        Reset
      </Button>
    </div>
  );
}

function DateRangePicker({
  label,
  dateRange,
  onChange,
}: {
  label: string;
  dateRange?: DateRange;
  onChange: (dateRange?: DateRange) => void;
}) {
  const [open, setOpen] = React.useState(false);

  const handleSelect = (range: DateRange | undefined) => {
    onChange(range);
    // Don't auto-close, let user explicitly apply or clear
  };

  const handleClear = () => {
    onChange(undefined);
    setOpen(false);
  };

  const displayText = React.useMemo(() => {
    if (!dateRange?.from) return "Pick a date range";
    if (!dateRange.to) return `${formatDateLocale(dateRange.from)} - Select end date`;
    return `${formatDateLocale(dateRange.from)} - ${formatDateLocale(dateRange.to)}`;
  }, [dateRange]);

  return (
    <div className="flex flex-col">
      <label className="text-xs text-muted-foreground">{label}</label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            className={cn(
              "h-9 w-64 justify-start text-left font-normal",
              !dateRange?.from && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            <span className="truncate text-xs">
              {displayText}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange?.from}
            selected={dateRange}
            onSelect={handleSelect}
            numberOfMonths={2}
            className="rounded-md"
          />
          <div className="p-3 border-t flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {dateRange?.from && dateRange?.to 
                ? `${formatDateLocale(dateRange.from)} - ${formatDateLocale(dateRange.to)}`
                : dateRange?.from 
                ? `${formatDateLocale(dateRange.from)} - Select end date`
                : "Select date range"
              }
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
              >
                Clear
              </Button>
              <Button
                size="sm"
                onClick={() => setOpen(false)}
              >
                Done
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

export default BillingSearchForm;
