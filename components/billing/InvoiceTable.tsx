"use client";

import * as React from "react";
import { DataTable, type ColumnDef } from "@/components/ui/DataTable";
import { Button } from "@/components/ui/button";
import InvoiceStatusBadge from "@/components/billing/InvoiceStatusBadge";
import BillingSearchForm, { type BillingSearchFilters } from "@/components/billing/BillingSearchForm";
import { formatCurrency } from "@/lib/currency"
import { formatDate } from "@/lib/date-utils";
import Link from "next/link";

export type InvoiceStatus = "draft" | "sent" | "partially_paid" | "paid" | "overdue";

export interface InvoiceRow {
  id: number | string;
  serial: string;
  date: string | Date;
  patientName: string;
  status: InvoiceStatus;
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
}

// Re-export BillingSearchFilters as InvoiceTableFilters for backward compatibility
export type InvoiceTableFilters = BillingSearchFilters;

export interface InvoiceTableProps {
  invoices: InvoiceRow[];
  filters: InvoiceTableFilters;
  onFiltersChange: (filters: InvoiceTableFilters) => void;
  onRowClick?: (invoice: InvoiceRow) => void;
  onSendInvoice?: (invoice: InvoiceRow) => void;
  onEditInvoice?: (invoice: InvoiceRow) => void;
  onDeleteInvoice?: (invoice: InvoiceRow) => void;
}

export function InvoiceTable({
  invoices,
  filters,
  onFiltersChange,
  onRowClick,
  onSendInvoice,
  onEditInvoice,
  onDeleteInvoice,
}: InvoiceTableProps) {
  const columns: ColumnDef<InvoiceRow>[] = [
    {
      id: "number",
      header: "Invoice #",
      accessor: (r) => <Link href={`/dashboard/billing/invoices/${r.id}`} className="underline font-mono">{r.serial}</Link>,
      sortable: true,
      sortAccessor: (r) => parseNumber(r.serial),
    },
    {
      id: "date",
      header: "Date",
      accessor: (r) => <span className="font-mono">{formatDate(r.date)}</span>,
      sortable: true,
      sortAccessor: (r) => new Date(r.date).getTime(),
    },
    {
      id: "patient",
      header: "Patient",
      accessor: (r) => r.patientName,
      sortable: true,
    },
    {
      id: "status",
      header: "Status",
      accessor: (r) => (
        <InvoiceStatusBadge status={r.status} />
      ),
      sortable: true,
      sortAccessor: (r) => r.status,
    },
    {
      id: "total",
      header: "Total",
      accessor: (r) => <span className="font-mono text-blue-700">{formatCurrency(r.totalAmount)}</span>,
      sortable: true,
      sortAccessor: (r) => r.totalAmount,
    },
    {
      id: "paid",
      header: "Paid",
      accessor: (r) => <span className="font-mono text-green-700">{formatCurrency(r.amountPaid)}</span>,
      sortable: true,
      sortAccessor: (r) => r.amountPaid,
    },
    {
      id: "balance",
      header: "Balance",
      accessor: (r) => <span className="font-mono text-red-700">{formatCurrency(r.balanceDue)}</span>,
      sortable: true,
      sortAccessor: (r) => r.balanceDue,
    },
    {
      id: "actions",
      header: "",
      cell: (row) => (
        <div className="flex items-center justify-end gap-2">
          <Button
            size="sm"
            variant="destructive"
            onClick={() => onDeleteInvoice?.(row)}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const filtered = React.useMemo(
    () => applyFilters(invoices, filters),
    [invoices, filters]
  );
  const [selected, setSelected] = React.useState<React.Key[]>([]);

  return (
    <div className="space-y-3">
      <BillingSearchForm filters={filters} onFiltersChange={onFiltersChange} />
      <DataTable
        data={filtered}
        columns={columns}
        defaultSort={{ columnId: "date", direction: "desc" }}
        onSortChange={() => {}}
        rowKey={(row) => row.id}
        showColumnControls
        enableBulkSelection
        selectedRowKeys={selected}
        onSelectedRowKeysChange={setSelected}
        emptyState={
          <div className="rounded-md border border-dashed py-10 text-center">
            <div className="text-sm text-muted-foreground">
              No invoices found. Adjust filters or create a new invoice.
            </div>
            <div className="mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onFiltersChange({})}
              >
                Reset filters
              </Button>
            </div>
          </div>
        }
        bulkActions={
          selected.length > 0 ? (
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">
                {selected.length} selected
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  /* placeholder for export */
                }}
              >
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelected([])}
              >
                Clear
              </Button>
            </div>
          ) : null
        }
        className="w-full"
      />
    </div>
  );
}



function applyFilters(
  invoices: InvoiceRow[],
  f: InvoiceTableFilters
): InvoiceRow[] {
  return invoices.filter((inv) => {
    if (
      f.patient &&
      !inv.patientName.toLowerCase().includes(f.patient.toLowerCase())
    )
      return false;
    if (f.status && f.status !== "all" && inv.status !== f.status) return false;
    
    // Date range filtering
    if (f.dateRange?.from || f.dateRange?.to) {
      const invoiceDate = new Date(inv.date);
      // Reset time to start of day for proper comparison
      const invoiceDateOnly = new Date(invoiceDate.getFullYear(), invoiceDate.getMonth(), invoiceDate.getDate());
      
      if (f.dateRange.from) {
        const fromDateOnly = new Date(f.dateRange.from.getFullYear(), f.dateRange.from.getMonth(), f.dateRange.from.getDate());
        if (invoiceDateOnly < fromDateOnly) return false;
      }
      
      if (f.dateRange.to) {
        const toDateOnly = new Date(f.dateRange.to.getFullYear(), f.dateRange.to.getMonth(), f.dateRange.to.getDate());
        if (invoiceDateOnly > toDateOnly) return false;
      }
    }
    
    return true;
  });
}

function parseNumber(v: string): number {
  const digits = v.replace(/[^0-9]/g, "");
  return Number(digits || 0);
}



// currency handled by shared helper

export default InvoiceTable;
