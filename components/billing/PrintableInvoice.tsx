"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/lib/currency"
import { formatDateLocale } from "@/lib/date-utils"

export type InvoiceStatus = "draft" | "sent" | "paid" | "overdue"

export interface PrintableInvoiceTreatmentItem {
  id: number | string
  procedureName: string
  status: "pending" | "in-progress" | "completed"
  cost: number
  notes?: string
}

export interface PrintableInvoicePaymentItem {
  id: number | string
  amount: number
  date: string | Date
  method?: string
}

export interface PrintableInvoiceData {
  id: number | string
  serial: string
  date: string | Date
  status: InvoiceStatus
  patient: { id: number | string; name: string }
  clinic?: { name?: string; address?: string; phone?: string; email?: string; logoUrl?: string }
  treatments: PrintableInvoiceTreatmentItem[]
  payments?: PrintableInvoicePaymentItem[]
  totalAmount: number
  amountPaid: number
  balanceDue: number
}

export interface PrintableInvoiceProps {
  invoice: PrintableInvoiceData
  className?: string
}

export function PrintableInvoice({ invoice, className }: PrintableInvoiceProps) {
  const subtotal = invoice.treatments.reduce((sum, t) => sum + (t.cost ?? 0), 0)
  const total = formatCurrency(invoice.totalAmount ?? subtotal)
  const paid = formatCurrency(invoice.amountPaid ?? 0)
  const balance = formatCurrency(invoice.balanceDue ?? Math.max(0, (invoice.totalAmount ?? subtotal) - (invoice.amountPaid ?? 0)))

  return (
    <div className={cn("bg-white text-black mx-auto w-full max-w-[900px] p-6 print:p-0", className)}>
      {/* Header */}
      <div className="flex items-start justify-between gap-4 border-b pb-4">
        <div className="flex items-center gap-3">
          {invoice.clinic?.logoUrl ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img src={invoice.clinic.logoUrl} alt="Clinic logo" className="h-12 w-12 object-contain" />
          ) : null}
          <div>
            <div className="text-xl font-semibold">{invoice.clinic?.name ?? "Clinic"}</div>
            {invoice.clinic?.address ? (
              <div className="text-xs text-gray-600">{invoice.clinic.address}</div>
            ) : null}
            {(invoice.clinic?.phone || invoice.clinic?.email) ? (
              <div className="text-xs text-gray-600 font-mono">
                {invoice.clinic?.phone ? `Phone: ${invoice.clinic.phone}` : null}
                {invoice.clinic?.phone && invoice.clinic?.email ? " • " : ""}
                {invoice.clinic?.email ? `Email: ${invoice.clinic.email}` : null}
              </div>
            ) : null}
          </div>
        </div>
        <div className="text-right text-sm">
          <div className="text-2xl font-bold tracking-tight">Invoice</div>
          <div className="mt-1">No: <span className="font-medium font-mono">{invoice.serial}</span></div>
          <div>Date: <span className="font-medium font-mono">{formatDateLocale(invoice.date)}</span></div>
          <div>Status: <span className="font-medium font-mono capitalize">{invoice.status}</span></div>
        </div>
      </div>

      {/* Bill To */}
      <div className="mt-4 flex items-start justify-between gap-6">
        <div className="text-sm">
          <div className="text-gray-600">Bill To</div>
          <div className="text-base font-medium">{invoice.patient.name}</div>
        </div>
      </div>

      {/* Items */}
      <div className="mt-6">
        <table className="w-full border-collapse text-sm">
          <thead>
            <tr className="border-b">
              <th className="px-2 py-2 text-left">Procedure</th>
              <th className="px-2 py-2 text-left">Status</th>
              <th className="px-2 py-2 text-right">Cost</th>
            </tr>
          </thead>
          <tbody>
            {invoice.treatments.map((t) => (
              <tr key={t.id} className="border-b last:border-0">
                <td className="px-2 py-2 align-top">
                  <div className="flex min-w-0 flex-col">
                    <span className="truncate font-medium">{t.procedureName}</span>
                    {t.notes ? (
                      <span className="text-gray-600 truncate text-xs">{t.notes}</span>
                    ) : null}
                  </div>
                </td>
                <td className="px-2 py-2 align-top capitalize">{t.status.replace("-", " ")}</td>
                <td className="px-2 py-2 align-top text-right font-mono">{formatCurrency(t.cost)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="mt-6 flex flex-col items-end gap-1 text-sm">
        <div className="flex w-full max-w-xs items-center justify-between">
          <span className="text-gray-600">Subtotal</span>
          <span className="font-medium font-mono text-blue-700">{formatCurrency(subtotal)}</span>
        </div>
        <div className="flex w-full max-w-xs items-center justify-between">
          <span className="text-gray-600">Total</span>
          <span className="font-medium font-mono text-blue-700">{total}</span>
        </div>
        <div className="flex w-full max-w-xs items-center justify-between">
          <span className="text-gray-600">Paid</span>
          <span className="font-medium font-mono text-green-700">{paid}</span>
        </div>
        <div className="flex w-full max-w-xs items-center justify-between">
          <span className="text-gray-600">Balance</span>
          <span className="text-base font-semibold font-mono text-red-700">{balance}</span>
        </div>
      </div>

      {/* Payments */}
      {invoice.payments && invoice.payments.length > 0 ? (
        <div className="mt-6">
          <div className="mb-2 text-sm font-medium">Payments</div>
          <table className="w-full border-collapse text-sm">
            <thead>
              <tr className="border-b">
                <th className="px-2 py-2 text-left">Date</th>
                <th className="px-2 py-2 text-left">Method</th>
                <th className="px-2 py-2 text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
              {invoice.payments.map((p) => (
                <tr key={p.id} className="border-b last:border-0">
                  <td className="px-2 py-2 align-top">{formatDateLocale(p.date)}</td>
                  <td className="px-2 py-2 align-top">{p.method ?? "—"}</td>
                  <td className="px-2 py-2 align-top text-right font-mono text-green-700">{formatCurrency(p.amount)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : null}

      {/* Footer Note */}
      <div className="mt-10 text-center text-xs text-gray-600">
        Thank you for your business.
      </div>

      {/* Print styles */}
      <style jsx>{`
        @media print {
          @page { size: A4; margin: 16mm; }
          .no-print { display: none !important; }
          body, html { background: white; }
        }
      `}</style>
    </div>
  )
}

export function printInvoice(elementRef?: { current: HTMLElement | null }) {
  if (elementRef?.current) {
    // Open a new window with the HTML and print
    const printWindow = window.open("", "_blank")
    if (!printWindow) return
    printWindow.document.write(`<!doctype html><html><head><title>Invoice</title></head><body>${elementRef.current.outerHTML}</body></html>`)
    printWindow.document.close()
    // Give the browser a tick to render before printing
    setTimeout(() => {
      printWindow.focus()
      printWindow.print()
      printWindow.close()
    }, 100)
  } else {
    window.print()
  }
}



// currency handled by shared helper

export default PrintableInvoice


