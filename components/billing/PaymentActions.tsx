"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

export interface PaymentActionsProps {
  invoiceId: number | string
  balanceDue: number
  onProcessPayment: () => Promise<void> | void
  onAfterPayment?: (result?: unknown) => void
  className?: string
}

export function PaymentActions({ invoiceId, balanceDue, onProcessPayment, onAfterPayment, className }: PaymentActionsProps) {
  const [loading, setLoading] = React.useState(false)

  const handleClick = async () => {
    setLoading(true)
    try {
      const res = await onProcessPayment()
      onAfterPayment?.(res)
    } finally {
      setLoading(false)
    }
  }

  if (balanceDue <= 0) return null

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Button onClick={handleClick} disabled={loading}>
        {loading ? "Processing..." : "Process Payment"}
      </Button>
    </div>
  )
}

export default PaymentActions


