"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { cn } from "@/lib/utils"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { paymentFormSchema, type PaymentFormData } from "@/lib/types/payment-schemas"
import { formatCurrency } from "@/lib/currency"

export type PaymentMethod = "cash" | "card" | "bank_transfer" | "insurance" | "other"

export interface PaymentFormProps {
  defaultValues?: Partial<PaymentFormData>
  loading?: boolean
  onSubmit: (data: PaymentFormData) => Promise<void> | void
  onCancel?: () => void
  className?: string
  outstandingBalance?: number
  allowOverpayment?: boolean
  onOverpaymentWarning?: (overBy: number) => void
}

export function PaymentForm({
  defaultValues,
  loading = false,
  onSubmit,
  onCancel,
  className,
  outstandingBalance = undefined,
  allowOverpayment = false,
  onOverpaymentWarning,
}: PaymentFormProps) {
  const todayLocal = React.useMemo(() => {
    const d = new Date()
    const y = d.getFullYear()
    const m = String(d.getMonth() + 1).padStart(2, "0")
    const day = String(d.getDate()).padStart(2, "0")
    return `${y}-${m}-${day}`
  }, [])

  const initialAmount = React.useMemo(() => {
    return typeof outstandingBalance === "number" && Number.isFinite(outstandingBalance) && outstandingBalance > 0
      ? String(outstandingBalance)
      : ""
  }, [outstandingBalance])

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      amount: initialAmount,
      paymentMethod: 'cash',
      paymentDate: todayLocal,
      notes: "",
      ...defaultValues,
    },
    mode: "onChange",
  })

  React.useEffect(() => {
    if (!form.formState.dirtyFields?.amount) {
      if (typeof outstandingBalance === "number" && Number.isFinite(outstandingBalance) && outstandingBalance > 0) {
        form.setValue("amount", String(outstandingBalance), { shouldValidate: true })
      }
    }
  }, [outstandingBalance, form])

  const handleSubmit = form.handleSubmit(async (values) => {
    try {
      await onSubmit(values)
    } catch (err) {
      const message = err instanceof Error ? err.message : "Failed to record payment. Please try again."
      form.setError("root", { type: "server", message })
    }
  })

  const amountValue = form.watch("amount")
  const parsedAmount = React.useMemo(() => {
    const n = Number(amountValue)
    return Number.isFinite(n) ? n : NaN
  }, [amountValue])

  const overBy = React.useMemo(() => {
    if (typeof outstandingBalance !== "number") return 0
    if (!Number.isFinite(parsedAmount)) return 0
    return Math.max(0, parsedAmount - outstandingBalance)
  }, [outstandingBalance, parsedAmount])

  React.useEffect(() => {
    if (typeof outstandingBalance !== "number") return
    // Clear any previous validation error first
    form.clearErrors("amount")
    if (Number.isFinite(parsedAmount)) {
      if (parsedAmount <= 0) return
      if (parsedAmount > outstandingBalance) {
        if (allowOverpayment) {
          onOverpaymentWarning?.(parsedAmount - outstandingBalance)
        } else {
          form.setError("amount", { type: "validate", message: "Amount exceeds outstanding balance" })
        }
      }
    }
  }, [allowOverpayment, form, onOverpaymentWarning, outstandingBalance, parsedAmount])

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className={cn("flex flex-col gap-4", className)}>
        {form.formState.errors.root?.message ? (
          <Alert variant="destructive">
            <AlertDescription>{form.formState.errors.root.message}</AlertDescription>
          </Alert>
        ) : null}
        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Amount</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  inputMode="decimal"
                  placeholder="0.00"
                  enterKeyHint="done"
                  className="h-11 md:h-9"
                  {...field}
                />
              </FormControl>
              <FormDescription>Enter the payment amount.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="paymentMethod"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Payment method</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={(v) => field.onChange(v as PaymentMethod)} defaultValue="cash">
                  <SelectTrigger className="w-full h-11 md:h-9">
                    <SelectValue placeholder="Select a method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="card">Card</SelectItem>
                    <SelectItem value="bank_transfer">Bank transfer</SelectItem>
                    <SelectItem value="insurance">Insurance</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="paymentDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Payment date</FormLabel>
              <FormControl>
                <Input type="date" className="h-11 md:h-9" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes (optional)</FormLabel>
              <FormControl>
                <Textarea rows={3} placeholder="Additional details..." className="min-h-[44px]" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="mt-2 flex items-center justify-end gap-2">
          {onCancel && (
            <Button type="button" variant="ghost" onClick={onCancel} className="min-w-[80px]">
              Cancel
            </Button>
          )}
          <Button type="submit" className="min-w-[140px]" disabled={loading || (!!form.formState.errors.amount && !allowOverpayment)}>
            {loading ? "Saving..." : "Record Payment"}
          </Button>
        </div>

        {typeof outstandingBalance === "number" && Number.isFinite(parsedAmount) && parsedAmount > 0 ? (
          <div className="mt-2 text-[12px] text-muted-foreground">
            Remaining balance after payment: {formatCurrency(Math.max(0, outstandingBalance - parsedAmount))}
          </div>
        ) : null}

        {overBy > 0 && allowOverpayment ? (
          <Alert className="mt-2" variant="default">
            <AlertDescription>
              This payment exceeds the outstanding balance by {formatCurrency(overBy)}. A credit will be created.
            </AlertDescription>
          </Alert>
        ) : null}
      </form>
    </Form>
  )
}

export default PaymentForm


