"use client"

import * as React from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/lib/currency"
import { formatDateLocale } from "@/lib/date-utils"

export type InvoiceStatus = "draft" | "sent" | "paid" | "overdue"

export interface InvoiceEditorTreatmentItem {
  id: number | string
  procedureName: string
  status: "pending" | "in-progress" | "completed"
  cost: number
  notes?: string
}

export interface InvoiceEditorData {
  id: number | string
  serial: string
  date: string | Date
  status: InvoiceStatus
  patient: { id: number | string; name: string }
  treatments: InvoiceEditorTreatmentItem[]
  totalAmount: number
  amountPaid: number
}

export interface InvoiceEditorProps {
  invoice: InvoiceEditorData
  className?: string
  onSave?: (updated: { treatments: InvoiceEditorTreatmentItem[]; totalAmount: number }) => Promise<void> | void
  onCancel?: () => void
  onTotalsChange?: (totals: { subtotal: number; total: number; paid: number; balance: number }) => void
}

export function InvoiceEditor({ invoice, className, onSave, onCancel, onTotalsChange }: InvoiceEditorProps) {
  const [rows, setRows] = React.useState<InvoiceEditorTreatmentItem[]>(invoice.treatments)
  const subtotal = rows.reduce((sum, t) => sum + (Number.isFinite(t.cost) ? t.cost : 0), 0)
  const total = subtotal
  const paid = invoice.amountPaid ?? 0
  const balance = Math.max(0, total - paid)

  React.useEffect(() => {
    onTotalsChange?.({ subtotal, total, paid, balance })
  }, [subtotal, total, paid, balance, onTotalsChange])

  const updateRowCost = (id: InvoiceEditorTreatmentItem["id"], value: string) => {
    const next = rows.map((r) => (r.id === id ? { ...r, cost: value === "" ? 0 : Number(value) } : r))
    setRows(next)
  }

  const handleSave = async () => {
    await onSave?.({ treatments: rows, totalAmount: total })
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Edit Invoice {invoice.serial}</CardTitle>
            <CardDescription>{formatDateLocale(invoice.date)} • {invoice.patient.name}</CardDescription>
          </div>
          <div className="text-right text-sm">
            <div>
              <span className="text-muted-foreground">Total</span> <span className="font-medium">{formatCurrency(total)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Paid</span> <span className="font-medium">{formatCurrency(paid)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Balance</span> <span className="font-semibold">{formatCurrency(balance)}</span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="py-5">
        <div className="mb-2 text-sm font-medium">Adjust Treatment Costs</div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Procedure</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Cost</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((t) => (
              <TableRow key={t.id}>
                <TableCell>
                  <div className="flex min-w-0 flex-col">
                    <span className="truncate text-sm font-medium">{t.procedureName}</span>
                    {t.notes ? <span className="text-muted-foreground truncate text-xs">{t.notes}</span> : null}
                  </div>
                </TableCell>
                <TableCell className="text-sm capitalize">{t.status.replace("-", " ")}</TableCell>
                <TableCell className="text-right">
                  <Input
                    type="number"
                    inputMode="decimal"
                    step="0.01"
                    min="0"
                    className="h-9 w-32 justify-self-end"
                    value={String(t.cost ?? "")}
                    onChange={(e) => updateRowCost(t.id, e.target.value)}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <div className="mt-4 flex flex-col items-end gap-1 text-sm">
          <div className="flex w-full max-w-xs items-center justify-between">
            <span className="text-muted-foreground">Subtotal</span>
            <span className="font-medium">{formatCurrency(subtotal)}</span>
          </div>
          <div className="flex w-full max-w-xs items-center justify-between">
            <span className="text-muted-foreground">Total</span>
            <span className="font-medium">{formatCurrency(total)}</span>
          </div>
          <div className="flex w-full max-w-xs items-center justify-between">
            <span className="text-muted-foreground">Balance</span>
            <span className="text-base font-semibold">{formatCurrency(balance)}</span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="justify-end border-t">
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" onClick={onCancel} disabled={!onCancel}>Cancel</Button>
          <Button onClick={handleSave} disabled={!onSave}>Save Changes</Button>
        </div>
      </CardFooter>
    </Card>
  )
}



// currency handled by shared helper

export default InvoiceEditor


