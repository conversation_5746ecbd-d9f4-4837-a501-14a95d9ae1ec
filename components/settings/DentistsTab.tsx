"use client"

import * as React from "react"
import { Users, Plus, Search } from "lucide-react"
import { formatDate } from "@/lib/date-utils"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DataTable, type ColumnDef } from "@/components/ui/DataTable"
import { EmptyState } from "@/components/ui/EmptyState"
import { toast } from "sonner"
import { DentistForm } from "@/components/settings/DentistForm"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmDialog } from "@/components/ui/ConfirmDialog"

interface Dentist {
  id: number
  firstName: string
  lastName: string
  email?: string | null
  phoneNumber?: string | null
  username: string
  isActive: boolean
  createdAt: string
}

export function DentistsTab() {
  const [dentists, setDentists] = React.useState<Dentist[]>([])
  const [loading, setLoading] = React.useState(true)
  const [search, setSearch] = React.useState("")
  const [formOpen, setFormOpen] = React.useState(false)
  const [editingDentist, setEditingDentist] = React.useState<Dentist | null>(null)
  const [confirmOpen, setConfirmOpen] = React.useState(false)
  const [deletingId, setDeletingId] = React.useState<number | null>(null)

  const loadDentists = React.useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (search) params.append('search', search)
      
      const response = await fetch(`/api/users/dentists?${params}`, {
        credentials: 'include',
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch dentists')
      }
      
      const result = await response.json()
      
      if (result.success) {
        setDentists(result.data)
      } else {
        throw new Error(result.message || 'Failed to load dentists')
      }
    } catch (error) {
      console.error('Error loading dentists:', error)
      toast.error('Failed to load dentists')
    } finally {
      setLoading(false)
    }
  }, [search])

  React.useEffect(() => {
    loadDentists()
  }, [loadDentists])

  React.useEffect(() => {
    const timer = setTimeout(() => {
      loadDentists()
    }, 300)
    return () => clearTimeout(timer)
  }, [search, loadDentists])

  const handleAdd = () => {
    setEditingDentist(null)
    setFormOpen(true)
  }

  const handleEdit = (dentist: Dentist) => {
    setEditingDentist(dentist)
    setFormOpen(true)
  }

  const handleDelete = (id: number) => {
    setDeletingId(id)
    setConfirmOpen(true)
  }

  const confirmDelete = async () => {
    if (!deletingId) return
    
    try {
      const response = await fetch(`/api/users/dentists/${deletingId}`, {
        method: 'DELETE',
        credentials: 'include',
      })
      
      const result = await response.json()
      
      if (result.success) {
        toast.success('Dentist deleted successfully')
        loadDentists()
      } else {
        throw new Error(result.message || 'Failed to delete dentist')
      }
    } catch (error) {
      console.error('Error deleting dentist:', error)
      toast.error('Failed to delete dentist')
    } finally {
      setConfirmOpen(false)
      setDeletingId(null)
    }
  }

  const columns = React.useMemo<ColumnDef<Dentist>[]>(
    () => [
      {
        id: "name",
        header: "Name",
        accessor: (row) => (
          <div className="font-medium">{`${row.firstName ?? ""} ${row.lastName ?? ""}`.trim()}</div>
        ),
        sortable: true,
        sortAccessor: (row) => `${row.lastName ?? ""} ${row.firstName ?? ""}`.toLowerCase(),
      },
      {
        id: "username",
        header: "Username",
        accessor: (row) => (
          <div className="text-muted-foreground">{row.username || "—"}</div>
        ),
        sortable: true,
        sortAccessor: (row) => (row.username ? row.username.toLowerCase() : ""),
      },
      {
        id: "email",
        header: "Email",
        accessor: (row) => (
          <div className="text-muted-foreground">{row.email || "—"}</div>
        ),
        sortable: true,
        sortAccessor: (row) => (row.email ? row.email.toLowerCase() : ""),
      },
      {
        id: "phoneNumber",
        header: "Phone",
        accessor: (row) => (
          <div className="text-muted-foreground">{row.phoneNumber || "—"}</div>
        ),
        sortable: true,
        sortAccessor: (row) => (row.phoneNumber ? row.phoneNumber.toLowerCase() : ""),
      },
      {
        id: "createdAt",
        header: "Created",
        accessor: (row) => (
          <div className="text-muted-foreground">{formatDate(row.createdAt)}</div>
        ),
        sortable: true,
        sortAccessor: (row) => {
          const t = new Date(row.createdAt).getTime()
          return isNaN(t) ? 0 : t
        },
      },
      {
        id: "actions",
        header: () => <div className="flex items-center gap-2 justify-center"></div>,
        cell: (row) => (
          <div className="flex items-center gap-2 justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleEdit(row)}
            >
              Edit
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDelete(row.id)}
            >
              Delete
            </Button>
          </div>
        ),
      },
    ],
    []
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Dentists Management
        </CardTitle>
        <CardDescription>
          Manage dentist accounts and permissions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search dentists..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button onClick={handleAdd}>
            <Plus className="h-4 w-4 mr-2" />
            Add Dentist
          </Button>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading dentists...</div>
          </div>
        ) : dentists.length === 0 ? (
          <EmptyState
            icon={<Users />}
            title="No dentists found"
            description={search ? "No dentists match your search criteria." : "Get started by adding your first dentist."}
            actions={!search ? [{ label: "Add Dentist", onClick: handleAdd }] : []}
          />
        ) : (
          <DataTable columns={columns} data={dentists || []} />
        )}

        <Dialog open={formOpen} onOpenChange={setFormOpen}>
          <DialogContent className="max-w-none w-[95vw] min-w-[1200px]">
            <DialogHeader>
              <DialogTitle>
                {editingDentist ? 'Edit Dentist' : 'Add Dentist'}
              </DialogTitle>
            </DialogHeader>
            <DentistForm
              dentist={editingDentist}
              onSuccess={() => { setFormOpen(false); setEditingDentist(null); loadDentists() }}
              onCancel={() => setFormOpen(false)}
            />
          </DialogContent>
        </Dialog>

        <ConfirmDialog
          open={confirmOpen}
          onOpenChange={setConfirmOpen}
          title="Delete Dentist"
          description="Are you sure you want to delete this dentist? This action cannot be undone."
          confirmText="Delete"
          destructive
          onConfirm={confirmDelete}
          onCancel={() => { setConfirmOpen(false); setDeletingId(null) }}
        />
      </CardContent>
    </Card>
  )
}


