"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { Loader2, Eye, EyeOff } from "lucide-react"
import { passwordSchema } from "@/lib/auth/schemas"

// Generate 24-hour time options
const generateTimeOptions = () => {
  const times = []
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      const displayTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      times.push({ value: timeString, label: displayTime })
    }
  }
  return times
}

const timeOptions = generateTimeOptions()

const baseDentistSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  phoneNumber: z.string().optional(),
  // Working hours fields
  sat: z.boolean().default(true),
  satstart: z.string().optional(),
  satend: z.string().optional(),
  sun: z.boolean().default(true),
  sunstart: z.string().optional(),
  sunend: z.string().optional(),
  mon: z.boolean().default(true),
  monstart: z.string().optional(),
  monend: z.string().optional(),
  tue: z.boolean().default(true),
  tuestart: z.string().optional(),
  tueend: z.string().optional(),
  wed: z.boolean().default(true),
  wedstart: z.string().optional(),
  wedend: z.string().optional(),
  thu: z.boolean().default(true),
  thustart: z.string().optional(),
  thuend: z.string().optional(),
  fri: z.boolean().default(true),
  fristart: z.string().optional(),
  friend: z.string().optional(),
})

const createDentistSchema = baseDentistSchema
  .extend({
    password: passwordSchema,
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    path: ["confirmPassword"],
    message: "Passwords don't match",
  })

const editDentistSchema = baseDentistSchema
  .extend({
    password: z.union([z.literal(""), passwordSchema]).optional(),
    confirmPassword: z.string().optional().or(z.literal("")),
  })
  .superRefine((data, ctx) => {
    if (data.password && data.password !== "") {
      if (!data.confirmPassword || data.confirmPassword !== data.password) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ["confirmPassword"], message: "Passwords don't match" })
      }
    }
  })

type CreateDentistFormData = z.infer<typeof createDentistSchema>
type EditDentistFormData = z.infer<typeof editDentistSchema>
type DentistFormData = CreateDentistFormData | EditDentistFormData

interface Dentist {
  id: number
  firstName: string
  lastName: string
  email?: string | null
  phoneNumber?: string | null
  username: string
  isActive: boolean
  createdAt: string
  // Working hours fields
  sat?: boolean
  satstart?: string | null
  satend?: string | null
  sun?: boolean
  sunstart?: string | null
  sunend?: string | null
  mon?: boolean
  monstart?: string | null
  monend?: string | null
  tue?: boolean
  tuestart?: string | null
  tueend?: string | null
  wed?: boolean
  wedstart?: string | null
  wedend?: string | null
  thu?: boolean
  thustart?: string | null
  thuend?: string | null
  fri?: boolean
  fristart?: string | null
  friend?: string | null
}

interface DentistFormProps {
  dentist?: Dentist | null
  onSuccess: () => void
  onCancel: () => void
}

export function DentistForm({ dentist, onSuccess, onCancel }: DentistFormProps) {
  const [saving, setSaving] = React.useState(false)
  const [showPassword, setShowPassword] = React.useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false)
  
  const isEditing = !!dentist

  const schema = React.useMemo(() => (isEditing ? editDentistSchema : createDentistSchema), [isEditing])

  const form = useForm<DentistFormData>({
    resolver: zodResolver(schema) as any,
    defaultValues: {
      firstName: dentist?.firstName || "",
      lastName: dentist?.lastName || "",
      username: dentist?.username || "",
      email: dentist?.email || "",
      phoneNumber: dentist?.phoneNumber || "",
      password: "",
      confirmPassword: "",
      // Working hours defaults
      sat: dentist?.sat ?? true,
      satstart: dentist?.satstart || "",
      satend: dentist?.satend || "",
      sun: dentist?.sun ?? true,
      sunstart: dentist?.sunstart || "",
      sunend: dentist?.sunend || "",
      mon: dentist?.mon ?? true,
      monstart: dentist?.monstart || "",
      monend: dentist?.monend || "",
      tue: dentist?.tue ?? true,
      tuestart: dentist?.tuestart || "",
      tueend: dentist?.tueend || "",
      wed: dentist?.wed ?? true,
      wedstart: dentist?.wedstart || "",
      wedend: dentist?.wedend || "",
      thu: dentist?.thu ?? true,
      thustart: dentist?.thustart || "",
      thuend: dentist?.thuend || "",
      fri: dentist?.fri ?? true,
      fristart: dentist?.fristart || "",
      friend: dentist?.friend || "",
    },
  })

  const onSubmit = async (data: DentistFormData) => {
    setSaving(true)
    try {
      const url = isEditing 
        ? `/api/users/dentists/${dentist!.id}`
        : '/api/users/dentists'
      
      const method = isEditing ? 'PUT' : 'POST'
      
      const submitData: {
        firstName: string
        lastName: string
        username: string
        email: string | null
        phoneNumber: string | null
        password?: string
        confirmPassword?: string
        sat: boolean
        satstart: string | null
        satend: string | null
        sun: boolean
        sunstart: string | null
        sunend: string | null
        mon: boolean
        monstart: string | null
        monend: string | null
        tue: boolean
        tuestart: string | null
        tueend: string | null
        wed: boolean
        wedstart: string | null
        wedend: string | null
        thu: boolean
        thustart: string | null
        thuend: string | null
        fri: boolean
        fristart: string | null
        friend: string | null
      } = {
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
        username: data.username.trim(),
        email: data.email?.trim() || null,
        phoneNumber: data.phoneNumber?.trim() || null,
        password: data.password || undefined,
        confirmPassword: data.confirmPassword || undefined,
        // Working hours data
        sat: data.sat ?? true,
        satstart: data.satstart?.trim() || null,
        satend: data.satend?.trim() || null,
        sun: data.sun ?? true,
        sunstart: data.sunstart?.trim() || null,
        sunend: data.sunend?.trim() || null,
        mon: data.mon ?? true,
        monstart: data.monstart?.trim() || null,
        monend: data.monend?.trim() || null,
        tue: data.tue ?? true,
        tuestart: data.tuestart?.trim() || null,
        tueend: data.tueend?.trim() || null,
        wed: data.wed ?? true,
        wedstart: data.wedstart?.trim() || null,
        wedend: data.wedend?.trim() || null,
        thu: data.thu ?? true,
        thustart: data.thustart?.trim() || null,
        thuend: data.thuend?.trim() || null,
        fri: data.fri ?? true,
        fristart: data.fristart?.trim() || null,
        friend: data.friend?.trim() || null,
      }

      if (isEditing && !submitData.password) {
        delete submitData.password
        delete submitData.confirmPassword
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submitData),
      })
      
      const result = await response.json()
      
      if (result.success) {
        toast.success(isEditing ? 'Dentist updated successfully' : 'Dentist created successfully')
        onSuccess()
      } else {
        throw new Error(result.message || 'Failed to save dentist')
      }
    } catch (error) {
      console.error('Error saving dentist:', error)
      toast.error('Failed to save dentist')
    } finally {
      setSaving(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-6">
          {/* Left column - Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter first name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter last name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
            </div>

            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Username *</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter username" {...field} />
                  </FormControl>
                  <FormDescription>
                    Used for login authentication
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="Enter email address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter phone number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Password {isEditing ? "(leave blank to keep current)" : "*"}
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder={isEditing ? "Enter new password" : "Enter password"}
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    {isEditing ? "Only enter a password if you want to change it" : "Must be at least 8 characters and include upper, lower, number, and special character"}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirm Password {isEditing ? "(required if changing password)" : "*"}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder={isEditing ? "Confirm new password" : "Confirm password"}
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Right column - Working Hours */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Working Hours</h3>
            
            {/* Saturday */}
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="sat"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Saturday</FormLabel>
                  </FormItem>
                )}
              />
              
              {form.watch("sat") && (
                <div className="grid grid-cols-2 gap-2 ml-6">
                  <FormField
                    control={form.control}
                    name="satstart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Start Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="satend"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">End Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Sunday */}
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="sun"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Sunday</FormLabel>
                  </FormItem>
                )}
              />
              
              {form.watch("sun") && (
                <div className="grid grid-cols-2 gap-2 ml-6">
                  <FormField
                    control={form.control}
                    name="sunstart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Start Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="sunend"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">End Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Monday */}
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="mon"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Monday</FormLabel>
                  </FormItem>
                )}
              />
              
              {form.watch("mon") && (
                <div className="grid grid-cols-2 gap-2 ml-6">
                  <FormField
                    control={form.control}
                    name="monstart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Start Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="monend"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">End Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Tuesday */}
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="tue"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Tuesday</FormLabel>
                  </FormItem>
                )}
              />
              
              {form.watch("tue") && (
                <div className="grid grid-cols-2 gap-2 ml-6">
                  <FormField
                    control={form.control}
                    name="tuestart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Start Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="tueend"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">End Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Wednesday */}
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="wed"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Wednesday</FormLabel>
                  </FormItem>
                )}
              />
              
              {form.watch("wed") && (
                <div className="grid grid-cols-2 gap-2 ml-6">
                  <FormField
                    control={form.control}
                    name="wedstart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Start Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="wedend"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">End Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Thursday */}
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="thu"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Thursday</FormLabel>
                  </FormItem>
                )}
              />
              
              {form.watch("thu") && (
                <div className="grid grid-cols-2 gap-2 ml-6">
                  <FormField
                    control={form.control}
                    name="thustart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Start Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="thuend"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">End Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Friday */}
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="fri"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Friday</FormLabel>
                  </FormItem>
                )}
              />
              
              {form.watch("fri") && (
                <div className="grid grid-cols-2 gap-2 ml-6">
                  <FormField
                    control={form.control}
                    name="fristart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Start Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="friend"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">End Time</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select time" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time.value} value={time.value}>
                                  {time.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button type="button" variant="outline" onClick={onCancel} disabled={saving}>
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditing ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              isEditing ? 'Update Dentist' : 'Create Dentist'
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}


