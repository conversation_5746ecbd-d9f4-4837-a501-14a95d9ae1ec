"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { toast } from "sonner"
import { Loader2, Eye, EyeOff } from "lucide-react"
import { passwordSchema } from "@/lib/auth/schemas"

const baseReceptionistSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  phoneNumber: z.string().optional(),
})

const createReceptionistSchema = baseReceptionistSchema
  .extend({
    password: passwordSchema,
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    path: ["confirmPassword"],
    message: "Passwords don't match",
  })

const editReceptionistSchema = baseReceptionistSchema
  .extend({
    password: z.union([z.literal(""), passwordSchema]).optional(),
    confirmPassword: z.string().optional().or(z.literal("")),
  })
  .superRefine((data, ctx) => {
    if (data.password && data.password !== "") {
      if (!data.confirmPassword || data.confirmPassword !== data.password) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ["confirmPassword"], message: "Passwords don't match" })
      }
    }
  })

type CreateReceptionistFormData = z.infer<typeof createReceptionistSchema>
type EditReceptionistFormData = z.infer<typeof editReceptionistSchema>
type ReceptionistFormData = CreateReceptionistFormData | EditReceptionistFormData

interface Receptionist {
  id: number
  firstName: string
  lastName: string
  email?: string | null
  phoneNumber?: string | null
  username: string
  isActive: boolean
  createdAt: string
}

interface ReceptionistFormProps {
  receptionist?: Receptionist | null
  onSuccess: () => void
  onCancel: () => void
}

export function ReceptionistForm({ receptionist, onSuccess, onCancel }: ReceptionistFormProps) {
  const [saving, setSaving] = React.useState(false)
  const [showPassword, setShowPassword] = React.useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false)
  
  const isEditing = !!receptionist

  const schema = React.useMemo(() => (isEditing ? editReceptionistSchema : createReceptionistSchema), [isEditing])

  const form = useForm<ReceptionistFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: receptionist?.firstName || "",
      lastName: receptionist?.lastName || "",
      username: receptionist?.username || "",
      email: receptionist?.email || "",
      phoneNumber: receptionist?.phoneNumber || "",
      password: "",
      confirmPassword: "",
    },
  })

  const onSubmit = async (data: ReceptionistFormData) => {
    setSaving(true)
    try {
      const url = isEditing 
        ? `/api/users/receptionists/${receptionist!.id}`
        : '/api/users/receptionists'
      
      const method = isEditing ? 'PUT' : 'POST'
      
      // Clean up data
      const submitData: {
        firstName: string
        lastName: string
        username: string
        email: string | null
        phoneNumber: string | null
        password?: string
        confirmPassword?: string
      } = {
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
        username: data.username.trim(),
        email: data.email?.trim() || null,
        phoneNumber: data.phoneNumber?.trim() || null,
        password: data.password || undefined,
        confirmPassword: data.confirmPassword || undefined,
      }

      // For editing, only send password if it's provided
      if (isEditing && !submitData.password) {
        delete submitData.password
        delete submitData.confirmPassword
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submitData),
      })
      
      const result = await response.json()
      
      if (result.success) {
        toast.success(isEditing ? 'Receptionist updated successfully' : 'Receptionist created successfully')
        onSuccess()
      } else {
        throw new Error(result.message || 'Failed to save receptionist')
      }
    } catch (error) {
      console.error('Error saving receptionist:', error)
      toast.error('Failed to save receptionist')
    } finally {
      setSaving(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter first name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter last name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username *</FormLabel>
              <FormControl>
                <Input placeholder="Enter username" {...field} />
              </FormControl>
              <FormDescription>
                Used for login authentication
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="Enter email address" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input placeholder="Enter phone number" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Password {isEditing ? "(leave blank to keep current)" : "*"}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder={isEditing ? "Enter new password" : "Enter password"}
                    {...field}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormDescription>
                {isEditing ? "Only enter a password if you want to change it" : "Must be at least 8 characters and include upper, lower, number, and special character"}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm Password {isEditing ? "(required if changing password)" : "*"}</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder={isEditing ? "Confirm new password" : "Confirm password"}
                    {...field}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-3 pt-4">
          <Button type="button" variant="outline" onClick={onCancel} disabled={saving}>
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditing ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              isEditing ? 'Update Receptionist' : 'Create Receptionist'
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}
