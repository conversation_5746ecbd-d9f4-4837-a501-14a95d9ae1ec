"use client"

import * as React from "react"
import { Users, Plus, Search } from "lucide-react"
import { formatDate } from "@/lib/date-utils"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DataTable, type ColumnDef } from "@/components/ui/DataTable"
import { EmptyState } from "@/components/ui/EmptyState"
import { toast } from "sonner"
import { ReceptionistForm } from "@/components/settings/ReceptionistForm"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmDialog } from "@/components/ui/ConfirmDialog"

interface Receptionist {
  id: number
  firstName: string
  lastName: string
  email?: string | null
  phoneNumber?: string | null
  username: string
  isActive: boolean
  createdAt: string
}

export function ReceptionistsTab() {
  const [receptionists, setReceptionists] = React.useState<Receptionist[]>([])
  const [loading, setLoading] = React.useState(true)
  const [search, setSearch] = React.useState("")
  const [formOpen, setFormOpen] = React.useState(false)
  const [editingReceptionist, setEditingReceptionist] = React.useState<Receptionist | null>(null)
  const [confirmOpen, setConfirmOpen] = React.useState(false)
  const [deletingId, setDeletingId] = React.useState<number | null>(null)

  // Load receptionists
  const loadReceptionists = React.useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (search) params.append('search', search)
      
      const response = await fetch(`/api/users/receptionists?${params}`, {
        credentials: 'include',
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch receptionists')
      }
      
      const result = await response.json()
      
      if (result.success) {
        setReceptionists(result.data)
      } else {
        throw new Error(result.message || 'Failed to load receptionists')
      }
    } catch (error) {
      console.error('Error loading receptionists:', error)
      toast.error('Failed to load receptionists')
    } finally {
      setLoading(false)
    }
  }, [search])

  React.useEffect(() => {
    loadReceptionists()
  }, [loadReceptionists])

  // Handle search with debounce
  React.useEffect(() => {
    const timer = setTimeout(() => {
      loadReceptionists()
    }, 300)
    return () => clearTimeout(timer)
  }, [search, loadReceptionists])

  const handleAdd = () => {
    setEditingReceptionist(null)
    setFormOpen(true)
  }

  const handleEdit = (receptionist: Receptionist) => {
    setEditingReceptionist(receptionist)
    setFormOpen(true)
  }

  const handleDelete = (id: number) => {
    setDeletingId(id)
    setConfirmOpen(true)
  }

  const confirmDelete = async () => {
    if (!deletingId) return
    
    try {
      const response = await fetch(`/api/users/receptionists/${deletingId}`, {
        method: 'DELETE',
        credentials: 'include',
      })
      
      const result = await response.json()
      
      if (result.success) {
        toast.success('Receptionist deleted successfully')
        loadReceptionists()
      } else {
        throw new Error(result.message || 'Failed to delete receptionist')
      }
    } catch (error) {
      console.error('Error deleting receptionist:', error)
      toast.error('Failed to delete receptionist')
    } finally {
      setConfirmOpen(false)
      setDeletingId(null)
    }
  }

  const handleFormSuccess = () => {
    setFormOpen(false)
    setEditingReceptionist(null)
    loadReceptionists()
  }

  const columns = React.useMemo<ColumnDef<Receptionist>[]>(
    () => [
      {
        id: "name",
        header: "Name",
        accessor: (row) => (
          <div className="font-medium">{`${row.firstName ?? ""} ${row.lastName ?? ""}`.trim()}</div>
        ),
        sortable: true,
        sortAccessor: (row) => `${row.lastName ?? ""} ${row.firstName ?? ""}`.toLowerCase(),
      },
      {
        id: "username",
        header: "Username",
        accessor: (row) => (
          <div className="text-muted-foreground">{row.username || "—"}</div>
        ),
        sortable: true,
        sortAccessor: (row) => (row.username ? row.username.toLowerCase() : ""),
      },
      {
        id: "email",
        header: "Email",
        accessor: (row) => (
          <div className="text-muted-foreground">{row.email || "—"}</div>
        ),
        sortable: true,
        sortAccessor: (row) => (row.email ? row.email.toLowerCase() : ""),
      },
      {
        id: "phoneNumber",
        header: "Phone",
        accessor: (row) => (
          <div className="text-muted-foreground">{row.phoneNumber || "—"}</div>
        ),
        sortable: true,
        sortAccessor: (row) => (row.phoneNumber ? row.phoneNumber.toLowerCase() : ""),
      },
      {
        id: "createdAt",
        header: "Created",
        accessor: (row) => (
          <div className="text-muted-foreground">{formatDate(row.createdAt)}</div>
        ),
        sortable: true,
        sortAccessor: (row) => {
          const t = new Date(row.createdAt).getTime()
          return isNaN(t) ? 0 : t
        },
      },
      {
        id: "actions",
        header: () => <div className="flex items-center gap-2 justify-center"></div>,
        cell: (row) => (
          <div className="flex items-center gap-2 justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleEdit(row)}
            >
              Edit
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDelete(row.id)}
            >
              Delete
            </Button>
          </div>
        ),
      },
    ],
    []
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Receptionists Management
        </CardTitle>
        <CardDescription>
          Manage receptionist accounts and permissions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Search and Add */}
        <div className="flex items-center justify-between gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search receptionists..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button onClick={handleAdd}>
            <Plus className="h-4 w-4 mr-2" />
            Add Receptionist
          </Button>
        </div>

        {/* Table */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading receptionists...</div>
          </div>
        ) : receptionists.length === 0 ? (
          <EmptyState
            icon={<Users />}
            title="No receptionists found"
            description={search ? "No receptionists match your search criteria." : "Get started by adding your first receptionist."}
            actions={
              !search
                ? [
                    {
                      label: "Add Receptionist",
                      onClick: handleAdd,
                    },
                  ]
                : []
            }
          />
        ) : (
          <DataTable columns={columns} data={receptionists || []} />
        )}

        {/* Form Dialog */}
        <Dialog open={formOpen} onOpenChange={setFormOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingReceptionist ? 'Edit Receptionist' : 'Add Receptionist'}
              </DialogTitle>
            </DialogHeader>
            <ReceptionistForm
              receptionist={editingReceptionist}
              onSuccess={handleFormSuccess}
              onCancel={() => setFormOpen(false)}
            />
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation */}
        <ConfirmDialog
          open={confirmOpen}
          onOpenChange={setConfirmOpen}
          title="Delete Receptionist"
          description="Are you sure you want to delete this receptionist? This action cannot be undone."
          confirmText="Delete"
          destructive
          onConfirm={confirmDelete}
          onCancel={() => { setConfirmOpen(false); setDeletingId(null) }}
        />
      </CardContent>
    </Card>
  )
}
