"use client"

import * as React from "react"
import { Upload, X, Image as ImageIcon, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { toast } from "sonner"
import Image from "next/image"
import { useMemo } from "react"

interface LogoUploadProps {
  currentLogo?: string | null
  onLogoChange: (logoUrl: string | null) => void
  disabled?: boolean
}

export function LogoUpload({ currentLogo, onLogoChange, disabled = false }: LogoUploadProps) {
  const [uploading, setUploading] = React.useState(false)
  const [removing, setRemoving] = React.useState(false)
  const [dragOver, setDragOver] = React.useState(false)
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    const maxSize = 2 * 1024 * 1024 // 2MB

    if (!allowedTypes.includes(file.type)) {
      return "Invalid file type. Please upload PNG, JPG, or WebP images."
    }

    if (file.size > maxSize) {
      return "File too large. Maximum size is 2MB."
    }

    return null
  }

  const handleFileUpload = async (file: File) => {
    const validationError = validateFile(file)
    if (validationError) {
      toast.error(validationError)
      return
    }

    setUploading(true)
    try {
      const formData = new FormData()
      formData.append('logo', file)

      const response = await fetch('/api/tenant/logo', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      })

      if (!response.ok) {
        const errorResult = await response.json()
        throw new Error(errorResult.message || 'Failed to upload logo')
      }

      const result = await response.json()

      if (result.success && result.data?.logoUrl) {
        onLogoChange(result.data.logoUrl)
        toast.success('Logo uploaded successfully')
      } else {
        throw new Error(result.message || 'Failed to upload logo')
      }
    } catch (error) {
      console.error('Error uploading logo:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to upload logo')
    } finally {
      setUploading(false)
    }
  }

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileUpload(file)
    }
    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setDragOver(false)

    const file = event.dataTransfer.files[0]
    if (file) {
      handleFileUpload(file)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = () => {
    setDragOver(false)
  }

  const handleRemoveLogo = async () => {
    setRemoving(true)
    try {
      const response = await fetch('/api/tenant/logo', {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorResult = await response.json()
        throw new Error(errorResult.message || 'Failed to remove logo')
      }

      const result = await response.json()

      if (result.success) {
        onLogoChange(null)
        toast.success('Logo removed successfully')
      } else {
        throw new Error(result.message || 'Failed to remove logo')
      }
    } catch (error) {
      console.error('Error removing logo:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to remove logo')
    } finally {
      setRemoving(false)
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const normalizedSrc = useMemo(() => {
    if (!currentLogo) return null
    try {
      // Vercel Blob URLs are absolute URLs - use them directly
      if (/^https?:\/\//i.test(currentLogo)) return currentLogo
      // Legacy local files - ensure path starts with a single leading slash for next/image
      // Also repair common db corruption like 'uploadslogos' -> '/uploads/logos'
      const repaired = currentLogo.replace(/^uploadslogos/,'/uploads/logos/').replace(/uploadslogos/,'uploads/logos')
      const withSlash = repaired.startsWith('/') ? repaired : `/${repaired}`
      // Collapse duplicate slashes
      return withSlash.replace(/\/{2,}/g, '/')
    } catch {
      return currentLogo
    }
  }, [currentLogo])

  if (normalizedSrc) {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Current Logo</h4>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleRemoveLogo}
              disabled={disabled || removing}
            >
              {removing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Removing...
                </>
              ) : (
                <>
                  <X className="mr-2 h-4 w-4" />
                  Remove
                </>
              )}
            </Button>
          </div>
          
          <div className="relative w-full h-32 bg-muted rounded-lg overflow-hidden">
            <Image
              src={normalizedSrc}
              alt="Clinic Logo"
              fill
              className="object-contain"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>

          <Button
            type="button"
            variant="outline"
            onClick={openFileDialog}
            disabled={disabled || uploading}
            className="w-full"
          >
            {uploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Change Logo
              </>
            )}
          </Button>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/png,image/jpeg,image/jpg,image/webp"
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled}
          />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardContent className="p-6">
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center space-y-4 transition-colors ${
            dragOver 
              ? 'border-primary bg-primary/5' 
              : 'border-border hover:border-primary/50'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={disabled ? undefined : openFileDialog}
        >
          {uploading ? (
            <Loader2 className="h-8 w-8 mx-auto animate-spin text-muted-foreground" />
          ) : (
            <div className="space-y-4">
              <div className="flex justify-center">
                {dragOver ? (
                  <Upload className="h-8 w-8 text-primary" />
                ) : (
                  <ImageIcon className="h-8 w-8 text-muted-foreground" />
                )}
              </div>
              
              <div className="space-y-2">
                <p className="text-sm font-medium">
                  {dragOver ? 'Drop your logo here' : 'Upload clinic logo'}
                </p>
                <p className="text-xs text-muted-foreground">
                  Drag & drop or click to select
                </p>
                <p className="text-xs text-muted-foreground">
                  PNG, JPG, WebP up to 2MB
                </p>
              </div>

              {!dragOver && (
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  disabled={disabled || uploading}
                  onClick={(e) => {
                    e.stopPropagation()
                    openFileDialog()
                  }}
                >
                  {uploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Choose File
                    </>
                  )}
                </Button>
              )}
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/png,image/jpeg,image/jpg,image/webp"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />
      </CardContent>
    </Card>
  )
}
