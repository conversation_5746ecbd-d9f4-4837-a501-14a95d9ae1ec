"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Building2, Phone, MapPin, Save, Loader2 } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { LogoUpload } from "./LogoUpload"

const clinicInfoSchema = z.object({
  name: z.string().min(2, "Clinic name must be at least 2 characters long"),
  address: z.string().optional(),
  phoneNumber: z.string().optional(),
})

type ClinicInfoFormData = z.infer<typeof clinicInfoSchema>

interface ClinicInfo extends ClinicInfoFormData {
  id: string
  logoImage?: string | null
  createdAt: Date
}

export function ClinicInformationTab() {
  const [clinicInfo, setClinicInfo] = React.useState<ClinicInfo | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [saving, setSaving] = React.useState(false)

  const form = useForm<ClinicInfoFormData>({
    resolver: zodResolver(clinicInfoSchema),
    defaultValues: {
      name: "",
      address: "",
      phoneNumber: "",
    },
  })

  // Load clinic information
  const loadClinicInfo = React.useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/tenant/info', {
        credentials: 'include',
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch clinic information')
      }
      
      const result = await response.json()
      
      if (result.success && result.data) {
        const info = result.data
        setClinicInfo(info)
        form.reset({
          name: info.name || "",
          address: info.address || "",
          phoneNumber: info.phoneNumber || "",
        })
      } else {
        throw new Error(result.message || 'Failed to load clinic information')
      }
    } catch (error) {
      console.error('Error loading clinic info:', error)
      toast.error('Failed to load clinic information')
    } finally {
      setLoading(false)
    }
  }, [form])

  // Save clinic information
  const onSubmit = async (data: ClinicInfoFormData) => {
    setSaving(true)
    try {
      const response = await fetch('/api/tenant/info', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name: data.name,
          address: data.address,
          phone: data.phoneNumber,
        }),
      })
      
      const result = await response.json()
      
      if (result.success) {
        setClinicInfo(result.data)
        toast.success('Clinic information updated successfully')
      } else {
        throw new Error(result.message || 'Failed to update clinic information')
      }
    } catch (error) {
      console.error('Error saving clinic info:', error)
      toast.error('Failed to update clinic information')
    } finally {
      setSaving(false)
    }
  }

  React.useEffect(() => {
    loadClinicInfo()
  }, [loadClinicInfo])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Clinic Information
          </CardTitle>
          <CardDescription>
            Loading clinic information...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Clinic Information
        </CardTitle>
        <CardDescription>
          Manage your clinic&apos;s basic information and branding
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Clinic Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      Clinic Name *
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter clinic name" {...field} />
                    </FormControl>
                    <FormDescription>
                      This name will appear on invoices and other documents
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Phone Number */}
              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone Number
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter phone number" {...field} />
                    </FormControl>
                    <FormDescription>
                      Main contact number for the clinic
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Address */}
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Address
                  </FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter clinic address" 
                      className="resize-none" 
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Full address of the clinic including city and postal code
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Logo Upload Section */}
            <div className="space-y-4">
              <label className="text-sm font-medium">
                Clinic Logo
              </label>
              <LogoUpload
                currentLogo={clinicInfo?.logoImage}
                onLogoChange={(logoUrl) => {
                  if (clinicInfo) {
                    setClinicInfo({ ...clinicInfo, logoImage: logoUrl })
                  }
                }}
                disabled={saving}
              />
            </div>

            {/* Clinic Information Display */}
            {clinicInfo && (
              <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                <h4 className="text-sm font-medium">Current Information</h4>
                <div className="grid gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">Clinic ID:</span> {clinicInfo.id}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Created:</span> {new Date(clinicInfo.createdAt).toLocaleDateString('en-IQ')}
                  </div>
                </div>
              </div>
            )}

            {/* Save Button */}
            <div className="flex justify-end">
              <Button type="submit" disabled={saving}>
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
