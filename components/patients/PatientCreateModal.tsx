"use client"

import * as React from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { PatientForm } from "@/components/patients/PatientForm"
import type { PatientFormData } from "@/lib/types/patient-schemas"
import { LoadingSpinner } from "@/components/ui/LoadingSpinner"

export interface PatientCreateModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  prefilledUid?: string | null
}

export function PatientCreateModal({ open, onOpenChange, prefilledUid }: PatientCreateModalProps) {
  const router = useRouter()
  const [submitting, setSubmitting] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  const handleSubmit = async (data: PatientFormData) => {
    setSubmitting(true)
    setError(null)
    try {
      const tenantId = typeof window !== "undefined" ? localStorage.getItem("tenantId") : null
      const res = await fetch("/api/patients/create-with-workflow", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        // Server derives tenantId from auth or headers; send body fallback too for safety
        body: JSON.stringify({ ...data, ...(tenantId ? { tenantId } : {}) }),
      })
      const json = await res.json()
      if (!res.ok || !json.success) {
        setError(json?.message ?? "Failed to create patient")
        return
      }
      onOpenChange(false)
      router.push(`/dashboard/patients/${json.patient.id}`)
    } catch {
      setError("Failed to create patient")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create Patient</DialogTitle>
        </DialogHeader>
        {submitting && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <LoadingSpinner size="sm" />
            Creating patient...
          </div>
        )}
        {error && <div className="text-destructive text-sm">{error}</div>}
        <PatientForm 
          patient={prefilledUid ? { uid: prefilledUid } : undefined}
          onSubmit={handleSubmit} 
          onCancel={() => onOpenChange(false)} 
          loading={submitting}
          hideUid={!!prefilledUid}
        />
      </DialogContent>
    </Dialog>
  )
}
