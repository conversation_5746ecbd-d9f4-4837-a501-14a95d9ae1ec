"use client"

import * as React from "react"
import { formatDateLocale } from "@/lib/date-utils"

export interface PatientHeaderProps {
  name: string
  phoneNumber?: string | null
  email?: string | null
  lastVisitDate?: string | Date | null
}

export function PatientHeader({ name, phoneNumber, email, lastVisitDate }: PatientHeaderProps) {
  const formattedLastVisit = React.useMemo(() => {
    if (!lastVisitDate) return "—"
    const d = lastVisitDate instanceof Date ? lastVisitDate : new Date(lastVisitDate)
    return isNaN(d.getTime()) ? "—" : formatDateLocale(d)
  }, [lastVisitDate])

  return (
    <div className="rounded-lg border p-4">
      <div className="flex flex-col gap-1 md:flex-row md:items-center md:justify-between">
        <h2 className="text-xl font-semibold">{name}</h2>
        <div className="text-sm text-muted-foreground">
          Last visit: {formattedLastVisit}
        </div>
      </div>
      <div className="mt-2 flex flex-wrap items-center gap-4 text-sm">
        {phoneNumber ? <div>Phone: {phoneNumber}</div> : null}
        {email ? <div>Email: {email}</div> : null}
      </div>
    </div>
  )
}
