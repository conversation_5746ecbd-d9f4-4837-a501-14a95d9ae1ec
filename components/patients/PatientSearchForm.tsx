"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

export interface SearchCriteria {
  searchTerm: string
}

export interface PatientSearchFormProps {
  onSearch: (criteria: SearchCriteria) => void
  onCreateNew: () => void
  loading?: boolean
  defaultQuery?: string
  className?: string
  showSearchButton?: boolean
  showCreateButton?: boolean
  showResetButton?: boolean
  onReset?: () => void
  // When provided, this will be called on reset to reload the default table data
  onResetToDefaultList?: () => void
  autoFocus?: boolean
}

export function PatientSearchForm({
  onSearch,
  onCreateNew,
  loading,
  defaultQuery = "",
  className,
  showSearchButton = true,
  showCreateButton = true,
  showResetButton = false,
  onReset,
  onResetToDefaultList,
  autoFocus = false,
}: PatientSearchFormProps) {
  const [query, setQuery] = React.useState<string>(defaultQuery)
  const inputRef = React.useRef<HTMLInputElement>(null)

  const submit = (e?: React.FormEvent) => {
    e?.preventDefault()
    const trimmedQuery = query.trim()
    if (trimmedQuery) {
      onSearch({ searchTerm: trimmedQuery })
    }
  }

  const handleReset = () => {
    setQuery(defaultQuery)
    // Prefer reloading the default list if provided; otherwise fall back to legacy onReset
    if (onResetToDefaultList) {
      onResetToDefaultList()
    } else {
      onReset?.()
    }
  }

  // Auto-focus the input when component mounts or autoFocus prop changes
  React.useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  return (
    <form onSubmit={submit} className={className}>
      <div className="flex flex-wrap items-center gap-2">
        <Input
          ref={inputRef}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search by name, phone, email, or patient ID"
          type="text"
          className="h-11 md:h-9 w-[320px]"
        />

        {showSearchButton && (
          <Button type="submit" disabled={loading} className="h-11 md:h-9">
            {loading ? "Searching..." : "Search"}
          </Button>
        )}

        {showCreateButton && (
          <Button type="button" variant="outline" onClick={onCreateNew} className="h-11 md:h-9">
            Create Patient
          </Button>
        )}

        {showResetButton && (
          <Button type="button" variant="outline" onClick={handleReset} className="h-11 md:h-9 ml-auto">
            Reset
          </Button>
        )}
      </div>
    </form>
  )
}
