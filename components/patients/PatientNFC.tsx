"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Nfc } from "lucide-react"
import { toast } from "sonner"

export interface PatientNFCProps {
  onPatientFound?: (patientId: number) => void
  onPatientNotFound?: (uid: string) => void
  className?: string
  autoFocus?: boolean
}

interface PatientSearchResponse {
  success: boolean
  found: boolean
  patient?: {
    id: number
    firstName: string
    lastName: string
    uid: string
  }
  message?: string
}

export function PatientNFC({ onPatientFound, onPatientNotFound, className, autoFocus = false }: PatientNFCProps) {
  const router = useRouter()
  const [uid, setUid] = React.useState("")
  const [isSearching, setIsSearching] = React.useState(false)

  const searchPatientByUID = async (uidValue: string) => {
    if (!uidValue.trim()) {
      toast.error("Please enter an NFC UID")
      return
    }

    setIsSearching(true)
    try {
      const response = await fetch(`/api/patients/uid?uid=${encodeURIComponent(uidValue.trim())}`, {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      })

      const data: PatientSearchResponse = await response.json()

      if (data.success && data.found && data.patient) {
        // Patient found - navigate to edit page
        toast.success(`Patient found: ${data.patient.firstName} ${data.patient.lastName}`)
        if (onPatientFound) {
          onPatientFound(data.patient.id)
        } else {
          router.push(`/dashboard/patients/${data.patient.id}`)
        }
      } else {
        // Patient not found - trigger add new patient with pre-filled UID
        toast.info("Patient not found. Opening new patient form with UID pre-filled.")
        if (onPatientNotFound) {
          onPatientNotFound(uidValue.trim())
        } else {
          // Navigate to patients page and somehow trigger the modal
          // We'll need to handle this through URL parameters or state management
          router.push(`/dashboard/patients?add=true&uid=${encodeURIComponent(uidValue.trim())}`)
        }
      }
    } catch (error) {
      console.error("Failed to search patient by UID:", error)
      toast.error("Failed to search for patient. Please try again.")
    } finally {
      setIsSearching(false)
      setUid("") // Clear the input after search
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault()
      searchPatientByUID(uid)
    }
  }



  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardContent className="flex items-center gap-6 p-6">
        {/* NFC Icon */}
        <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full flex-shrink-0">
          <Nfc className="w-8 h-8 text-primary" />
        </div>

        {/* Content */}
        <div className="flex-1 space-y-3">
          <div>
            <h3 className="text-lg font-semibold">NFC Patient Lookup</h3>
            <p className="text-sm text-muted-foreground">
              Scan NFC tag or enter UID manually
            </p>
          </div>

          {/* Input Field */}
          <Input
            type="text"
            placeholder="Enter NFC UID and press Enter..."
            value={uid}
            onChange={(e) => setUid(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isSearching}
            autoFocus={autoFocus}
            className="w-full"
          />
        </div>
      </CardContent>
    </Card>
  )
}
