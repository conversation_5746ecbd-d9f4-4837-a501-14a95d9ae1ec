"use client"

import * as React from "react"
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { formatDateLocale } from "@/lib/date-utils"

export interface CaseSheetCardProps {
  hasCaseSheet: boolean
  lastUpdated?: string | Date | null
  onInitialize: () => void
  loading?: boolean
  className?: string
}

export function CaseSheetCard({
  hasCaseSheet,
  lastUpdated,
  onInitialize,
  loading,
  className,
}: CaseSheetCardProps) {
  const formattedUpdatedAt = React.useMemo(() => {
    if (!lastUpdated) return "—"
    const d = lastUpdated instanceof Date ? lastUpdated : new Date(lastUpdated)
    return isNaN(d.getTime()) ? "—" : formatDateLocale(d)
  }, [lastUpdated])

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Case Sheet</CardTitle>
        <CardDescription>
          {hasCaseSheet ? "Initialized" : "Not initialized"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground">Last updated: {formattedUpdatedAt}</div>
      </CardContent>
      {!hasCaseSheet && (
        <CardFooter>
          <Button onClick={onInitialize} disabled={loading} aria-busy={loading}>
            {loading ? "Initializing..." : "Initialize Case Sheet"}
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}
