"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { loginSchema, type LoginData, type LoginResponse } from "@/lib/auth/schemas";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle, User, Lock, Eye, EyeOff, Shield } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface LoginFormProps {
  onSubmit: (data: LoginData & { rememberMe?: boolean }) => Promise<LoginResponse>;
  isLoading?: boolean;
  error?: string;
  redirectUrl?: string;
  enableRememberMe?: boolean;
}

export function LoginForm({
  onSubmit,
  isLoading = false,
  error,
  redirectUrl,
  enableRememberMe = true,
}: LoginFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [apiError, setApiError] = useState<string | null>(error || null);
  const [attemptCount, setAttemptCount] = useState(0);
  const [isRateLimited, setIsRateLimited] = useState(false);

  const form = useForm<LoginData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // Load saved username from localStorage if remember me was previously used
  useEffect(() => {
    if (enableRememberMe) {
      const savedUsername = localStorage.getItem('rememberedUsername');
      if (savedUsername) {
        form.setValue('username', savedUsername);
        setRememberMe(true);
      }
    }
  }, [enableRememberMe, form]);

  const handleSubmit = async (data: LoginData) => {
    try {
      setApiError(null);
      setAttemptCount(prev => prev + 1);
      
      const response = await onSubmit({ ...data, rememberMe });
      
      if (!response.success) {
        // Handle specific error types
        if (response.message.includes("rate limit") || response.message.includes("Too many")) {
          setIsRateLimited(true);
          setApiError("Too many failed attempts. Please wait before trying again.");
        } else if (response.message.includes("credentials") || response.message.includes("Invalid")) {
          setApiError("Invalid username or password. Please check your credentials and try again.");
        } else {
          setApiError(response.message);
        }
        return;
      }
      
      // Success - handle remember me functionality
      if (enableRememberMe) {
        if (rememberMe) {
          localStorage.setItem('rememberedUsername', data.username);
        } else {
          localStorage.removeItem('rememberedUsername');
        }
      }
      
      // Clear sensitive form data
      form.reset();
      setShowPassword(false);
      
      console.log("Login successful:", response);
      
    } catch (error) {
      console.error("Login form submission error:", error);
      setAttemptCount(prev => prev + 1);
      
      // Provide user-friendly error messages
      if (error instanceof Error) {
        if (error.message.includes('fetch')) {
          setApiError("Unable to connect to the server. Please check your internet connection and try again.");
        } else if (error.message.includes('timeout')) {
          setApiError("Request timed out. Please try again.");
        } else {
          setApiError("An unexpected error occurred. Please try again.");
        }
      } else {
        setApiError("An unexpected error occurred. Please try again.");
      }
    }
  };

  // Clear API error when user starts typing
  const clearError = () => {
    if (apiError) {
      setApiError(null);
    }
    if (isRateLimited) {
      setIsRateLimited(false);
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Enter to submit form
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();
        form.handleSubmit(handleSubmit)();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [form, handleSubmit]);

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Welcome Back</h1>
        <p className="text-muted-foreground">
          Sign in to your clinic management account
        </p>
      </div>

      {apiError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex flex-col gap-1">
            <span>{apiError}</span>
            {isRateLimited && (
              <span className="text-sm text-muted-foreground">
                For security reasons, please wait before attempting to log in again.
              </span>
            )}
            {attemptCount >= 3 && !isRateLimited && (
              <span className="text-sm text-muted-foreground">
                Having trouble? Make sure your username and password are correct.
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Sign In
          </CardTitle>
          <CardDescription>
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your username"
                        autoComplete="username"
                        {...field}
                        disabled={isLoading}
                        onChange={(e) => {
                          field.onChange(e);
                          clearError();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Lock className="h-4 w-4" />
                      Password
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter your password"
                          autoComplete="current-password"
                          {...field}
                          disabled={isLoading}
                          onChange={(e) => {
                            field.onChange(e);
                            clearError();
                          }}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                          disabled={isLoading}
                          tabIndex={-1}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                          <span className="sr-only">
                            {showPassword ? "Hide password" : "Show password"}
                          </span>
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {enableRememberMe && (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remember-me"
                    checked={rememberMe}
                    onCheckedChange={(checked) => setRememberMe(!!checked)}
                    disabled={isLoading}
                  />
                  <label
                    htmlFor="remember-me"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                  >
                    Remember my username
                  </label>
                </div>
              )}

              <Button
                type="submit"
                className="w-full"
                size="lg"
                disabled={isLoading || isRateLimited}
              >
                {isLoading ? (
                  <>
                    <Shield className="mr-2 h-4 w-4 animate-pulse" />
                    Signing In...
                  </>
                ) : (
                  "Sign In"
                )}
              </Button>

              {isRateLimited && (
                <p className="text-sm text-center text-muted-foreground">
                  Too many failed attempts. Please wait a few minutes before trying again.
                </p>
              )}
            </form>
          </Form>
        </CardContent>
      </Card>

      
    </div>
  );
}
