"use client"

import React, { useEffect, useState, useCallback } from "react"
import { useRouter } from "next/navigation"

interface SubscriptionGuardProps {
  children: React.ReactNode
  tenantId?: string
  onSubscriptionExpired?: () => void
}

interface SubscriptionValidation {
  isValid: boolean
  status: 'active' | 'inactive' | 'overdue' | 'grace_period'
  message: string
  daysOverdue?: number
}

export function SubscriptionGuard({ 
  children, 
  tenantId, 
  onSubscriptionExpired 
}: SubscriptionGuardProps) {
  const router = useRouter()
  const [isValidating, setIsValidating] = useState(true)
  const [hasValidSubscription, setHasValidSubscription] = useState(true)

  const validateSubscription = useCallback(async () => {
    try {
      setIsValidating(true)
      
      const response = await fetch(`/api/tenant/subscription-validation?tenantId=${tenantId}`, {
        method: 'GET',
        credentials: 'include',
      })

      if (response.ok) {
        const result = await response.json()
        
        if (result.success && result.data) {
          const validation: SubscriptionValidation = result.data
          
          if (!validation.isValid) {
            // Subscription is invalid - show error message
            console.warn('Subscription validation failed:', validation.message)
            setHasValidSubscription(false)
            
            if (onSubscriptionExpired) {
              onSubscriptionExpired()
            }
            return
          }
          
          setHasValidSubscription(true)
        } else {
          // Failed to get validation data
          console.error('Failed to validate subscription:', result.message)
          setHasValidSubscription(false)
        }
      } else {
        console.error('Subscription validation request failed')
        setHasValidSubscription(false)
      }
    } catch (error) {
      console.error('Error validating subscription:', error)
      setHasValidSubscription(false)
    } finally {
      setIsValidating(false)
    }
  }, [tenantId, onSubscriptionExpired])

  useEffect(() => {
    if (!tenantId) {
      setIsValidating(false)
      return
    }

    validateSubscription()
  }, [tenantId, validateSubscription])

  const handleReturn = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      })
      
      const result = await response.json()
      
      if (result.success) {
        router.push('/')
      } else {
        console.error('Logout failed:', result.message)
        router.push('/')
      }
    } catch (error) {
      console.error('Logout error:', error)
      router.push('/')
    }
  }

  if (isValidating) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-sm text-muted-foreground">Checking subscription status...</p>
        </div>
      </div>
    )
  }

  if (!hasValidSubscription) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-6xl">⚠️</div>
          <h1 className="text-2xl font-bold text-destructive">Subscription Required</h1>
          <p className="text-muted-foreground max-w-md">
            Your subscription has expired or is invalid. Please contact support to renew your subscription to continue using the application.
          </p>
          <div className="flex items-center justify-center">
            <button
              onClick={handleReturn}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              Return
            </button>
          </div>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
