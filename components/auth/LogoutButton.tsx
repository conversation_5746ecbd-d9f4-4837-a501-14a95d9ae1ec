"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";

export function LogoutButton({
  size = "sm",
  variant = "outline",
  className,
}: {
  size?: "default" | "sm" | "lg" | "icon";
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  className?: string;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      const res = await fetch("/api/auth/logout", { method: "POST" });
      const data = await res.json().catch(() => ({}));
      const redirectUrl = data?.redirectUrl || "/auth/login?message=logout-success";
      router.push(redirectUrl);
    } catch (e) {
      router.push("/auth/login?message=logout-success");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button onClick={handleLogout} size={size} variant={variant} className={className} disabled={isLoading}>
      <LogOut className="h-4 w-4" />
      <span className="sr-only sm:not-sr-only">{isLoading ? "Logging out..." : "Logout"}</span>
    </Button>
  );
}
