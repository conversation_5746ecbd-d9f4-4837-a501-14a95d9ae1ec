enum PromotionType {
    BANNER
    PREVIOUS_CASES
}

model Promotion {
    id       Int           @id @default(autoincrement())
    tenantId Int
    tenant   Tenant        @relation(fields: [tenantId], references: [id])
    type     PromotionType
    title    String?
    fileUrl  String?
    fileType String?


    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
    userId    Int?
    user      User?    @relation("PreviousCases", fields: [userId], references: [id])
}
