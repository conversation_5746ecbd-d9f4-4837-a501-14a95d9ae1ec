enum InvoiceStatus {
  DRAFT
  SENT
  PARTIALLY_PAID
  PAID
  OVERDUE
}

model Invoice {
  id                  Int                   @id @default(autoincrement())
  tenantId            Int
  tenant              Tenant                @relation(fields: [tenantId], references: [id])
  serial              String                @default("")
  // Essential relationships
  patientId           Int
  patient             Patient               @relation(fields: [patientId], references: [id])
  
  // Essential invoice fields
  invoiceDate         DateTime
  status              InvoiceStatus         @default(DRAFT)
  
  // Essential financial amounts
  totalAmount         Decimal               @default(0.00)
  amountPaid          Decimal               @default(0.00)
  balanceDue          Decimal               @default(0.00)
  
  // Basic audit fields
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  createdById         Int?
  updatedById         Int?
  
  // Essential relationships
  payments            Payment[]
  treatments          Treatment[]
  
  @@unique([tenantId, serial])
  @@index([tenantId, patientId])
  @@index([tenantId, status])
}