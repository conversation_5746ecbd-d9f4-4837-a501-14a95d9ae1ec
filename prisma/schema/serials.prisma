model CaseSheetSerial {
  id       Int    @id @default(autoincrement())
  tenantId Int
  tenant   Tenant @relation(fields: [tenantId], references: [id])
}

model InvoiceSerial {
  id       Int    @id @default(autoincrement())
  tenantId Int
  tenant   Tenant @relation(fields: [tenantId], references: [id])
}

model PaymentSerial {
  id       Int    @id @default(autoincrement())
  tenantId Int
  tenant   Tenant @relation(fields: [tenantId], references: [id])
}
