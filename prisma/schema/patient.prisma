enum PatientStatus {
  ACTIVE
  INACTIVE
}

model Patient {
  id            Int           @id @default(autoincrement())
  tenantId      Int
  tenant        Tenant        @relation(fields: [tenantId], references: [id])
  
  // User relationship
  userId        Int?          @unique
  user          User?         @relation("PatientUser", fields: [userId], references: [id])

  // patient nfc tag
  uid           String?
  
  // Core demographics
  firstName     String
  lastName      String
  dateOfBirth   DateTime?
  
  // Contact information
  phoneNumber   String?
  email         String?
  address       String?
  
  // Status
  status        PatientStatus @default(ACTIVE)
  
  // Audit fields
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  createdById   Int?
  updatedById   Int?
  
  // Relations
  appointments  Appointment[]
  caseSheet     CaseSheet?
  invoices      Invoice[]
  payments      Payment[]
  
  @@unique([tenantId, uid])
  @@index([tenantId, lastName, firstName])
  @@index([tenantId, status])
}