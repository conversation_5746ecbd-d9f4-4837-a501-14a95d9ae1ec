enum ToothStatus {
  PRESENT
  MISSING
  EXTRACTED
  CROWN
  FILLING
}

model Tooth {
  id                  Int         @id @default(autoincrement())
  tenantId            Int
  tenant              Tenant      @relation(fields: [tenantId], references: [id])
  
  // Relationships
  caseSheetId         Int
  caseSheet           CaseSheet   @relation(fields: [caseSheetId], references: [id])
  
  // FDI notation
  toothNumber         Int         // 11-48 for adult teeth
  quadrant            Int         // Auto-populated from tooth number
  positionInQuadrant  Int         // Auto-populated from tooth number
  toothName           String      // Auto-populated anatomical name
  
  // Status
  status              ToothStatus @default(PRESENT)
  
  // Audit fields
  createdAt           DateTime    @default(now())
  updatedAt           DateTime    @updatedAt
  createdById         Int?
  updatedById         Int?
  
  // Relations
  findings            Finding[]
  
  @@unique([tenantId, caseSheetId, toothNumber])
  @@index([tenantId, toothNumber])
  @@index([tenantId, status])
}