enum UserType {
  ADMIN
  DENTIST
  RECEPTIONIST
  PATIENT
}

model User {
  id       Int    @id @default(autoincrement())
  tenantId Int
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // Authentication fields
  username    String? @unique
  phoneNumber String? @unique
  email       String? @unique
  password    String

  // User details
  firstName String?
  lastName  String?
  userType  UserType @default(PATIENT)

  // Basic status
  isActive Boolean @default(true)

  // dentist specific fields for working hours
  sat      <PERSON>olean @default(true)
  satstart String?
  satend   String?
  sun      Boolean @default(true)
  sunstart String?
  sunend   String?
  mon      Boolean @default(true)
  monstart String?
  monend   String?
  tue      Boolean @default(true)
  tuestart String?
  tueend   String?
  wed      Boolean @default(true)
  wedstart String?
  wedend   String?
  thu      Boolean @default(true)
  thustart String?
  thuend   String?
  fri      <PERSON>an @default(true)
  fristart String?
  friend   String?

  // Basic audit fields (preserved for compliance)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById Int?
  updatedById Int?

  // Relations
  patientProfile      Patient?      @relation("PatientUser")
  primaryAppointments Appointment[] @relation("PrimaryProvider")
  completedTreatments Treatment[]   @relation("CompletedBy")
  recordedFindings    Finding[]     @relation("RecordedBy")
  previousCases       Promotion[]   @relation("PreviousCases")

  @@unique([tenantId, username])
  @@unique([tenantId, phoneNumber])
  @@unique([tenantId, email])
  @@index([tenantId, userType])
}
