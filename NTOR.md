# Next.js to Remix Migration Guide (NTOR)

## Table of Contents
1. [Project Profile](#project-profile)
2. [Migration Plan](#migration-plan)
3. [File-by-File Mapping](#file-by-file-mapping)
4. [Dependency Migration](#dependency-migration)
5. [Code Examples](#code-examples)
6. [Risk Identification and Mitigation](#risk-identification-and-mitigation)
7. [Validation and Testing](#validation-and-testing)
8. [Rollback Plan](#rollback-plan)

## Project Profile

### Application Overview
The DnD (Dental and Diagnosis) application is a comprehensive dental practice management system built with Next.js 15.4.5 using the App Router. It serves as a modern replacement for a legacy Django-based system, providing features for patient management, appointment scheduling, treatment planning, billing, and multi-tenant architecture.

### Key Characteristics

#### Technology Stack
- **Frontend Framework**: Next.js 15.4.5 with App Router
- **React Version**: 19.1.0
- **Styling**: Tailwind CSS 4 with shadcn/ui components
- **Form Handling**: React Hook Form 7.62.0 with Zod 4.0.14 validation
- **Icons**: Lucide React (Note: Will need to migrate to @phosphor-icons/react as per requirements)
- **Database**: Prisma 6.13.0 with PostgreSQL
- **Authentication**: Custom JWT-based implementation
- **Testing**: Vitest 3.2.4 with Testing Library
- **Package Manager**: pnpm

#### Architecture Patterns
- **File-based Routing**: Using Next.js App Router convention
- **Server Components**: Extensive use of React Server Components for data fetching
- **API Routes**: RESTful API endpoints under `/app/api/`
- **Middleware**: Authentication and tenant context management
- **Multi-tenant Architecture**: Tenant isolation with context propagation
- **Component Organization**: Feature-based structure with shared UI components

#### Application Structure
```
app/
├── api/                    # API routes
│   ├── billing/
│   │   ├── invoices/
│   │   │   ├── route.ts    # GET/POST invoices
│   │   │   └── [id]/
│   │   │       ├── route.ts # GET/PUT/DELETE specific invoice
│   │   │       └── payments/
│   │   │           └── route.ts # Payment operations
├── auth/                  # Authentication pages
│   ├── login/
│   │   └── page.tsx       # Login page
│   └── register/
│       └── page.tsx       # Registration page
├── dashboard/             # Main application dashboard
│   ├── layout.tsx         # Dashboard layout
│   ├── page.tsx           # Dashboard home
│   ├── appointments/
│   ├── billing/
│   ├── patients/
│   ├── profile/
│   ├── reports/
│   └── settings/
├── layout.tsx             # Root layout
├── page.tsx               # Landing page
├── error.tsx              # Error boundary
└── not-found.tsx          # 404 page

components/
├── appointments/          # Appointment-related components
├── auth/                  # Authentication components
├── billing/               # Billing and invoice components
├── clinical/              # Clinical/dental chart components
├── profile/               # Profile management components
├── settings/              # Settings components
└── ui/                    # Shared UI components (shadcn/ui)

lib/
├── auth/                  # Authentication utilities
├── clinical/              # Clinical domain logic
├── prisma-extensions/     # Prisma extensions
├── types/                 # TypeScript type definitions
└── utils.ts               # Utility functions
```

#### Data Flow Patterns
1. **Server-Side Data Fetching**: Using `fetch()` in Server Components with various caching strategies
2. **Client-Side Data Fetching**: Using `useSWR` for dynamic data in Client Components
3. **Form Handling**: React Hook Form with server actions for mutations
4. **Authentication Flow**: JWT-based with middleware protection
5. **Tenant Context**: Multi-tenant data isolation with context propagation

#### Key Features
- **Patient Management**: Complete patient records with medical history
- **Appointment Scheduling**: Calendar-based scheduling with conflict resolution
- **Treatment Planning**: Digital treatment plans with cost estimates
- **Billing & Invoicing**: Automated billing with payment processing
- **Multi-Tenant Support**: Tenant isolation with centralized controls
- **Security**: HIPAA compliance with role-based access control

## Migration Plan

### Phase 1: Foundation Setup (Week 1-2)

#### Objectives
- Set up Remix project structure
- Configure build tools and development environment
- Establish core routing structure

#### Tasks
1. **Initialize Remix Project**
   ```bash
   npx create-remix@latest
   # Choose TypeScript, Tailwind CSS, and other relevant options
   ```

2. **Configure Project Structure**
   - Create app directory structure matching Next.js organization
   - Set up routes directory with flat routing convention
   - Configure entry.server.tsx and entry.client.tsx

3. **Install Core Dependencies**
   ```bash
   pnpm add @remix-run/node @remix-run/react @remix-run/serve
   pnpm add react-router-dom
   pnpm add tailwindcss postcss autoprefixer
   pnpm add @radix-ui/react-slot class-variance-authority clsx tailwind-merge
   pnpm add react-hook-form @hookform/resolvers zod
   pnpm add @phosphor-icons/react
   ```

4. **Configure Tailwind CSS**
   - Migrate Tailwind configuration from Next.js project
   - Set up CSS imports in Remix root route

#### Completion Criteria
- [ ] Remix project builds successfully
- [ ] Development server starts without errors
- [ ] Basic route structure matches Next.js organization
- [ ] Tailwind CSS styling works correctly
- [ ] TypeScript configuration is properly set up

### Phase 2: Core Infrastructure (Week 2-3)

#### Objectives
- Migrate authentication system
- Implement database connectivity
- Set up middleware and context providers

#### Tasks
1. **Authentication Migration**
   - Convert Next.js middleware to Remix loader/action pattern
   - Implement session management using Remix sessions
   - Create authentication utilities and hooks

2. **Database Integration**
   - Migrate Prisma schema and client
   - Set up database connection utilities
   - Create data access patterns using Remix loaders

3. **Context Providers**
   - Convert Next.js context providers to Remix-compatible format
   - Implement tenant context management
   - Set up global state management patterns

#### Completion Criteria
- [ ] Authentication flow works end-to-end
- [ ] Database queries execute successfully through Remix loaders
- [ ] Tenant context is properly propagated
- [ ] Protected routes require authentication
- [ ] User sessions persist correctly

### Phase 3: Route Migration (Week 3-5)

#### Objectives
- Migrate all page routes from Next.js to Remix
- Convert API routes to Remix action functions
- Implement data loading patterns

#### Tasks
1. **Page Routes Migration**
   - Convert each page from Next.js App Router to Remix route
   - Migrate Server Components to Remix loader pattern
   - Implement client-side navigation with Remix Link

2. **API Routes Migration**
   - Convert Next.js API routes to Remix action functions
   - Implement RESTful endpoints in Remix routes
   - Migrate error handling patterns

3. **Dynamic Routes**
   - Convert Next.js dynamic routes to Remix parameterized routes
   - Implement nested routing patterns
   - Set up route-based code splitting

#### Completion Criteria
- [ ] All page routes render correctly
- [ ] API endpoints respond with expected data
- [ ] Dynamic routes handle parameters correctly
- [ ] Navigation between routes works seamlessly
- [ ] Form submissions process correctly

### Phase 4: Component Migration (Week 5-6)

#### Objectives
- Migrate all React components
- Update component patterns for Remix
- Ensure compatibility with Remix data loading

#### Tasks
1. **UI Components Migration**
   - Convert shadcn/ui components to Remix-compatible format
   - Update icon usage from Lucide to Phosphor
   - Ensure proper client/server component boundaries

2. **Feature Components Migration**
   - Migrate appointment, billing, and clinical components
   - Update form handling patterns
   - Implement proper error boundaries

3. **Layout Components**
   - Convert root and nested layouts
   - Implement outlet patterns for nested routes
   - Ensure proper meta tag handling

#### Completion Criteria
- [ ] All components render without errors
- [ ] Forms submit and validate correctly
- [ ] Icons display properly with Phosphor
- [ ] Layouts maintain consistent structure
- [ ] Component boundaries are correctly defined

### Phase 5: Testing and Optimization (Week 6-7)

#### Objectives
- Ensure comprehensive test coverage
- Optimize performance and bundle size
- Implement proper error handling

#### Tasks
1. **Testing Migration**
   - Convert Vitest tests to work with Remix patterns
   - Update Testing Library utilities for Remix
   - Implement integration tests for key user flows

2. **Performance Optimization**
   - Implement proper caching strategies
   - Optimize bundle size with code splitting
   - Set up proper image optimization

3. **Error Handling**
   - Implement error boundaries at all levels
   - Create proper error pages and handling
   - Set up logging and monitoring

#### Completion Criteria
- [ ] All tests pass with new Remix implementation
- [ ] Performance metrics meet or exceed Next.js implementation
- [ ] Error handling covers all edge cases
- [ ] Bundle size is optimized
- [ ] Application is production-ready

## File-by-File Mapping

### Root Files

#### Next.js `app/layout.tsx` → Remix `app/root.tsx`

**Next.js Implementation:**
```tsx
// app/layout.tsx:1-34
import type { Metadata } from "next";
import { Geist, Geist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
```

**Remix Equivalent:**
```tsx
// app/root.tsx
import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from "@remix-run/react";
import { Geist, Geist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}

export function meta() {
  return [
    { title: "Dental & Diagnosis" },
    { name: "description", content: "Modern dental practice management system" },
  ];
}

export function links() {
  return [
    { rel: "preconnect", href: "https://fonts.googleapis.com" },
    { rel: "preconnect", href: "https://fonts.gstatic.com", crossOrigin: "anonymous" },
  ];
}
```

#### Next.js `app/page.tsx` → Remix `app/routes/_index.tsx`

**Next.js Implementation:**
```tsx
// app/page.tsx:1-262
// Modern, polished landing page for Dental and Diagnosis (DnD)
// - Server component renders different actions based on authentication state
// - Contemporary visual design with subtle gradients, glass surfaces, and icons
// Based on Django-to-Prisma migration requirements for dental practice software

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { AuthService } from "@/lib/auth/services/auth-service";
import { LogoutButton } from "@/components/auth/LogoutButton";
import { Users, Calendar, ClipboardList, Receipt, Layers, Shield } from "lucide-react";

export default async function Home() {
  const validation = await AuthService.validateSession();
  const isAuthenticated = validation.isValid && !!validation.session;
  const username = validation.session?.username;

  return (
    <div className="min-h-screen bg-background relative">
      {/* Background aesthetics */}
      <div className="pointer-events-none absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 left-1/2 h-[32rem] w-[32rem] -translate-x-1/2 rounded-full bg-primary/10 blur-3xl" />
        <div className="absolute bottom-0 right-0 h-[24rem] w-[24rem] translate-x-1/3 translate-y-1/3 rounded-full bg-accent/10 blur-3xl" />
      </div>

      {/* Navigation */}
      <nav className="sticky top-0 z-20 border-b border-border/40 bg-background/80 backdrop-blur-lg">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="size-6 rounded-md bg-primary/15 ring-1 ring-primary/30 flex items-center justify-center">
                <div className="size-3 rounded-sm bg-primary/50" />
              </div>
              <span className="text-sm font-semibold tracking-wide text-muted-foreground">Dental & Diagnosis</span>
            </div>

            <div className="flex items-center gap-2">
              {isAuthenticated ? (
                <>
                  <Button size="sm" asChild>
                    <Link href="/dashboard">Dashboard</Link>
                  </Button>
                  <LogoutButton size="sm" variant="ghost" />
                </>
              ) : (
                <>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href="/auth/login">Login</Link>
                  </Button>
                  <Button size="sm" asChild>
                    <Link href="/auth/register">Register</Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-28">
        <div className="mx-auto max-w-5xl text-center">
          <Badge variant="secondary" className="mb-5 inline-flex items-center gap-2">
            <span className="inline-block size-2 rounded-full bg-primary" />
            Modern dental practice management
          </Badge>

          <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight">
            <span className="bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-transparent">
              Streamline care. Elevate outcomes.
            </span>
          </h1>

          <p className="mt-6 text-balance text-lg sm:text-xl leading-8 text-muted-foreground max-w-3xl mx-auto">
            DnD unifies patient records, scheduling, treatment planning, and billing — all secured and optimized for multi-clinic growth.
          </p>

          <div className="mt-10 flex flex-col sm:flex-row gap-3 justify-center">
            {isAuthenticated ? (
              <>
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link href="/dashboard">Go to Dashboard</Link>
                </Button>
              </>
            ) : (
              <>
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link href="/auth/register">Get Started</Link>
                </Button>
                <Button variant="outline" size="lg" className="text-base px-7 py-5" asChild>
                  <Link href="/auth/login">Login</Link>
                </Button>
              </>
            )}
          </div>

          <p className="mt-6 text-sm text-muted-foreground">
            HIPAA compliant • End‑to‑end encryption • Role‑based access
          </p>
          {isAuthenticated && (
            <p className="mt-2 text-xs text-muted-foreground">Signed in{username ? ` as ${username}` : ""}</p>
          )}
        </div>
      </section>

      <Separator className="container mx-auto" />

      {/* Features Section */}
      <section id="features" className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="mx-auto max-w-2xl text-center mb-14">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Everything your practice needs</h2>
          <p className="mt-4 text-lg leading-8 text-muted-foreground">
            Purpose‑built features that help teams deliver excellent patient care.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Patient Management</CardTitle>
              <CardDescription className="text-base">
                Unified records, medical histories, and charts with secure access controls.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Calendar className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Appointment Scheduling</CardTitle>
              <CardDescription className="text-base">
                Smart calendars with reminders, conflict resolution, and multi‑provider views.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <ClipboardList className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Treatment Planning</CardTitle>
              <CardDescription className="text-base">
                Digital plans with cost estimates, progress tracking, and patient communication.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Receipt className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Billing & Insurance</CardTitle>
              <CardDescription className="text-base">
                Automated billing, claims, and financial reporting across tenants.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Layers className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Multi‑Tenant Architecture</CardTitle>
              <CardDescription className="text-base">
                Tenant isolation, centralized controls, and scalable performance.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-xl">Advanced Security</CardTitle>
              <CardDescription className="text-base">
                Encryption, audit trails, and role‑based access — HIPAA compliant.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      <Separator className="container mx-auto" />

      {/* Call to Action Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <Card className="max-w-4xl mx-auto border-border/50">
          <CardContent className="p-10 sm:p-12 text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Ready to modernize your practice?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join clinics improving outcomes with efficient workflows and secure data.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center mb-6">
              {isAuthenticated ? (
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link href="/dashboard">Open Dashboard</Link>
                </Button>
              ) : (
                <>
                  <Button size="lg" className="text-base px-7 py-5" asChild>
                    <Link href="/auth/register">Register Now</Link>
                  </Button>
                  <Button variant="outline" size="lg" className="text-base px-7 py-5" asChild>
                    <Link href="/auth/login">Login</Link>
                  </Button>
                </>
              )}
            </div>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
              <span>✓ HIPAA compliant</span>
              <span>✓ Secure & reliable</span>
              <span>✓ Professional support</span>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t border-border/40 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-3 mb-4 md:mb-0">
              <div className="size-6 rounded-md bg-primary/15 ring-1 ring-primary/30 flex items-center justify-center">
                <div className="size-3 rounded-sm bg-primary/50" />
              </div>
              <h3 className="text-sm font-semibold tracking-tight">Dental & Diagnosis</h3>
            </div>
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              <a href="#" className="hover:text-foreground transition-colors">Privacy</a>
              <a href="#" className="hover:text-foreground transition-colors">Terms</a>
              <a href="#" className="hover:text-foreground transition-colors">Support</a>
              <a href="#" className="hover:text-foreground transition-colors">Contact</a>
            </div>
          </div>
          <Separator className="my-8" />
          <div className="text-center text-sm text-muted-foreground">
            © 2025 Dental and Diagnosis. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
```

**Remix Equivalent:**
```tsx
// app/routes/_index.tsx
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";
import { LogoutButton } from "~/components/auth/LogoutButton";
import { 
  Users, 
  Calendar, 
  ClipboardList, 
  Receipt, 
  Layers, 
  Shield 
} from "@phosphor-icons/react";

import { AuthService } from "~/lib/auth/services/auth-service";

export async function loader({ request }: LoaderFunctionArgs) {
  const validation = await AuthService.validateSession();
  return json({
    isAuthenticated: validation.isValid && !!validation.session,
    username: validation.session?.username,
  });
}

export default function Index() {
  const { isAuthenticated, username } = useLoaderData<typeof loader>();

  return (
    <div className="min-h-screen bg-background relative">
      {/* Background aesthetics */}
      <div className="pointer-events-none absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 left-1/2 h-[32rem] w-[32rem] -translate-x-1/2 rounded-full bg-primary/10 blur-3xl" />
        <div className="absolute bottom-0 right-0 h-[24rem] w-[24rem] translate-x-1/3 translate-y-1/3 rounded-full bg-accent/10 blur-3xl" />
      </div>

      {/* Navigation */}
      <nav className="sticky top-0 z-20 border-b border-border/40 bg-background/80 backdrop-blur-lg">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="size-6 rounded-md bg-primary/15 ring-1 ring-primary/30 flex items-center justify-center">
                <div className="size-3 rounded-sm bg-primary/50" />
              </div>
              <span className="text-sm font-semibold tracking-wide text-muted-foreground">Dental & Diagnosis</span>
            </div>

            <div className="flex items-center gap-2">
              {isAuthenticated ? (
                <>
                  <Button size="sm" asChild>
                    <Link to="/dashboard">Dashboard</Link>
                  </Button>
                  <LogoutButton size="sm" variant="ghost" />
                </>
              ) : (
                <>
                  <Button variant="ghost" size="sm" asChild>
                    <Link to="/auth/login">Login</Link>
                  </Button>
                  <Button size="sm" asChild>
                    <Link to="/auth/register">Register</Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-28">
        <div className="mx-auto max-w-5xl text-center">
          <Badge variant="secondary" className="mb-5 inline-flex items-center gap-2">
            <span className="inline-block size-2 rounded-full bg-primary" />
            Modern dental practice management
          </Badge>

          <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight">
            <span className="bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-transparent">
              Streamline care. Elevate outcomes.
            </span>
          </h1>

          <p className="mt-6 text-balance text-lg sm:text-xl leading-8 text-muted-foreground max-w-3xl mx-auto">
            DnD unifies patient records, scheduling, treatment planning, and billing — all secured and optimized for multi-clinic growth.
          </p>

          <div className="mt-10 flex flex-col sm:flex-row gap-3 justify-center">
            {isAuthenticated ? (
              <>
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link to="/dashboard">Go to Dashboard</Link>
                </Button>
              </>
            ) : (
              <>
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link to="/auth/register">Get Started</Link>
                </Button>
                <Button variant="outline" size="lg" className="text-base px-7 py-5" asChild>
                  <Link to="/auth/login">Login</Link>
                </Button>
              </>
            )}
          </div>

          <p className="mt-6 text-sm text-muted-foreground">
            HIPAA compliant • End‑to‑end encryption • Role‑based access
          </p>
          {isAuthenticated && (
            <p className="mt-2 text-xs text-muted-foreground">Signed in{username ? ` as ${username}` : ""}</p>
          )}
        </div>
      </section>

      <Separator className="container mx-auto" />

      {/* Features Section */}
      <section id="features" className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="mx-auto max-w-2xl text-center mb-14">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Everything your practice needs</h2>
          <p className="mt-4 text-lg leading-8 text-muted-foreground">
            Purpose‑built features that help teams deliver excellent patient care.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-primary" weight="regular" />
              </div>
              <CardTitle className="text-xl">Patient Management</CardTitle>
              <CardDescription className="text-base">
                Unified records, medical histories, and charts with secure access controls.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Calendar className="h-6 w-6 text-primary" weight="regular" />
              </div>
              <CardTitle className="text-xl">Appointment Scheduling</CardTitle>
              <CardDescription className="text-base">
                Smart calendars with reminders, conflict resolution, and multi‑provider views.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <ClipboardList className="h-6 w-6 text-primary" weight="regular" />
              </div>
              <CardTitle className="text-xl">Treatment Planning</CardTitle>
              <CardDescription className="text-base">
                Digital plans with cost estimates, progress tracking, and patient communication.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Receipt className="h-6 w-6 text-primary" weight="regular" />
              </div>
              <CardTitle className="text-xl">Billing & Insurance</CardTitle>
              <CardDescription className="text-base">
                Automated billing, claims, and financial reporting across tenants.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Layers className="h-6 w-6 text-primary" weight="regular" />
              </div>
              <CardTitle className="text-xl">Multi‑Tenant Architecture</CardTitle>
              <CardDescription className="text-base">
                Tenant isolation, centralized controls, and scalable performance.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-primary" weight="regular" />
              </div>
              <CardTitle className="text-xl">Advanced Security</CardTitle>
              <CardDescription className="text-base">
                Encryption, audit trails, and role‑based access — HIPAA compliant.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      <Separator className="container mx-auto" />

      {/* Call to Action Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <Card className="max-w-4xl mx-auto border-border/50">
          <CardContent className="p-10 sm:p-12 text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Ready to modernize your practice?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join clinics improving outcomes with efficient workflows and secure data.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center mb-6">
              {isAuthenticated ? (
                <Button size="lg" className="text-base px-7 py-5" asChild>
                  <Link to="/dashboard">Open Dashboard</Link>
                </Button>
              ) : (
                <>
                  <Button size="lg" className="text-base px-7 py-5" asChild>
                    <Link to="/auth/register">Register Now</Link>
                  </Button>
                  <Button variant="outline" size="lg" className="text-base px-7 py-5" asChild>
                    <Link to="/auth/login">Login</Link>
                  </Button>
                </>
              )}
            </div>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
              <span>✓ HIPAA compliant</span>
              <span>✓ Secure & reliable</span>
              <span>✓ Professional support</span>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t border-border/40 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-3 mb-4 md:mb-0">
              <div className="size-6 rounded-md bg-primary/15 ring-1 ring-primary/30 flex items-center justify-center">
                <div className="size-3 rounded-sm bg-primary/50" />
              </div>
              <h3 className="text-sm font-semibold tracking-tight">Dental & Diagnosis</h3>
            </div>
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              <a href="#" className="hover:text-foreground transition-colors">Privacy</a>
              <a href="#" className="hover:text-foreground transition-colors">Terms</a>
              <a href="#" className="hover:text-foreground transition-colors">Support</a>
              <a href="#" className="hover:text-foreground transition-colors">Contact</a>
            </div>
          </div>
          <Separator className="my-8" />
          <div className="text-center text-sm text-muted-foreground">
            © 2025 Dental and Diagnosis. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
```

### Authentication Routes

#### Next.js `app/auth/login/page.tsx` → Remix `app/routes/auth.login.tsx`

**Next.js Implementation:**
```tsx
// app/auth/login/page.tsx (example structure)
import { LoginForm } from "@/components/auth/LoginForm";

export default function LoginPage() {
  return <LoginForm />;
}
```

**Remix Equivalent:**
```tsx
// app/routes/auth.login.tsx
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, Form, Link } from "@remix-run/react";
import { LoginForm } from "~/components/auth/LoginForm";
import { AuthService } from "~/lib/auth/services/auth-service";

export async function loader({ request }: LoaderFunctionArgs) {
  const validation = await AuthService.validateSession();
  if (validation.isValid) {
    return redirect("/dashboard");
  }
  return json({ error: null });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const email = formData.get("email");
  const password = formData.get("password");
  
  // Authenticate user logic here
  const result = await AuthService.login({ email, password });
  
  if (!result.success) {
    return json({ error: result.message }, { status: 400 });
  }
  
  return redirect("/dashboard");
}

export default function Login() {
  const { error } = useLoaderData<typeof loader>();
  
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="w-full max-w-md">
        <h1 className="text-2xl font-bold mb-6">Login</h1>
        {error && <div className="text-red-500 mb-4">{error}</div>}
        <LoginForm />
        <p className="mt-4 text-center">
          Don't have an account?{" "}
          <Link to="/auth/register" className="text-blue-600 hover:underline">
            Register
          </Link>
        </p>
      </div>
    </div>
  );
}
```

### API Routes

#### Next.js `app/api/billing/invoices/route.ts` → Remix `app/routes/api.billing.invoices.tsx`

**Next.js Implementation:**
```tsx
// app/api/billing/invoices/route.ts:1-158
import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/auth/services"

// POST /api/billing/invoices
// Body: { patientId: number, treatmentIds?: number[] }
export const POST = AuthMiddleware.withAuth(
async (request: NextRequest, { authContext }) => {
  try {
    const body = await request.json()
    const patientId = Number(body?.patientId)
    const ids = Array.isArray(body?.treatmentIds) ? body.treatmentIds.map((x: any) => Number(x)).filter((n: number) => Number.isInteger(n) && n > 0) : []
    if (!Number.isInteger(patientId) || patientId <= 0) {
      return NextResponse.json({ success: false, message: "patientId must be a positive integer" }, { status: 400 })
    }

    const patient = await prisma.patient.findUnique({ where: { id: patientId } })
    if (!patient) return NextResponse.json({ success: false, message: "Patient not found" }, { status: 404 })

    // Fetch candidate treatments that belong to the patient's case sheet and are not linked to any invoice yet
    let treatments = [] as { id: number; cost: any }[]
    if (ids.length > 0) {
      treatments = await prisma.treatment.findMany({
        where: {
          id: { in: ids },
          invoiceId: null,
          finding: { tooth: { caseSheet: { patientId } } },
        },
        select: { id: true, cost: true },
      })
    } else {
      treatments = await prisma.treatment.findMany({
        where: {
          invoiceId: null,
          finding: { tooth: { caseSheet: { patientId } } },
        },
        select: { id: true, cost: true },
      })
    }

    const totalAmount = treatments.reduce((sum, t) => sum + Number(t.cost as any), 0)

    // If there are no eligible treatments to invoice:
    // 1) If specific treatmentIds were provided and they are already invoiced under a single invoice, return that invoice
    // 2) Otherwise, try to return an existing open invoice (DRAFT/SENT) for this patient
    // 3) If none found, do NOT create a new empty invoice; return 400 to avoid empty invoices
    if (treatments.length === 0) {
      // Case 1: All provided treatments already belong to a single invoice? Return it.
      if (ids.length > 0) {
        const selectedTreatments = await prisma.treatment.findMany({
          where: { id: { in: ids } },
          select: { invoiceId: true },
        })
        const invoiceIds = Array.from(
          new Set(
            selectedTreatments
              .map((t: { invoiceId: number | null }) => t.invoiceId)
              .filter((v: number | null): v is number => typeof v === 'number')
          )
        )
        if (invoiceIds.length === 1) {
          const existing = await prisma.invoice.findUnique({ where: { id: invoiceIds[0] } })
          if (existing) {
            return NextResponse.json({ success: true, invoice: existing }, { status: 200 })
          }
        }
      }

      // Case 2: Return an existing open invoice for the patient if any
      const openExisting = await prisma.invoice.findFirst({
        where: {
          patientId,
          OR: [{ status: "DRAFT" as any }, { status: "SENT" as any }, { status: "PARTIALLY_PAID" as any }],
        },
        orderBy: { invoiceDate: 'desc' },
      })
      if (openExisting) {
        return NextResponse.json({ success: true, invoice: openExisting }, { status: 200 })
      }

      // Case 3: Nothing to invoice and no open invoice to return
      return NextResponse.json({ success: false, message: "No eligible treatments to invoice" }, { status: 400 })
    }

    // Create invoice with a temporary unique number; then set to INV-{id}
    const serialCount = await prisma.invoiceSerial.count({ where: { tenantId: (patient as any).tenantId as string } });
    const serial = serialCount + 1;
    await prisma.invoiceSerial.create({ data: { tenantId: (patient as any).tenantId as string } });
    let invoice = await prisma.invoice.create({
      data: {
        tenantId: (patient as any).tenantId as string,
        patientId,
        serial: serial.toString(),
        invoiceDate: new Date(),
        status: "DRAFT" as any,
        totalAmount: totalAmount as any,
        amountPaid: 0 as any,
        balanceDue: totalAmount as any,
      },
    })

    // Update to deterministic invoice number INV-{invoiceId}
    invoice = await prisma.invoice.update({
      where: { id: invoice.id },
      data: { serial: `INV-${invoice.id}` },
    })

    if (treatments.length > 0) {
      await prisma.treatment.updateMany({
        where: { id: { in: treatments.map((t) => t.id) } },
        data: { invoiceId: invoice.id },
      })
    }

    return NextResponse.json({ success: true, invoice }, { status: 201 })
  } catch (e) {
    return NextResponse.json({ success: false, message: "Failed to create invoice" }, { status: 500 })
  },
{ requireAuth: true, requireTenant: true }
)

// GET /api/billing/invoices
// Returns a list of invoices with basic patient info
export const GET = AuthMiddleware.withAuth(
async (_request: NextRequest, { authContext }) => {
  try {
    // Get tenantId from authenticated context
    const tenantId = authContext?.user?.tenantId ?? authContext?.tenant?.id;
    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const invoices = await prisma.invoice.findMany({
      where: { tenantId },
      include: { patient: true },
      orderBy: { invoiceDate: "desc" },
    })
    return NextResponse.json(invoices, { status: 200 })
  } catch (e) {
    return NextResponse.json({ success: false, message: "Failed to load invoices" }, { status: 500 })
  }
},
{ requireAuth: true, requireTenant: true }
)

export async function PUT() {
  return NextResponse.json({ success: false, message: "Method not allowed" }, { status: 405 })
}

export async function DELETE() {
  return NextResponse.json({ success: false, message: "Method not allowed" }, { status: 405 })
}
```

**Remix Equivalent:**
```tsx
// app/routes/api.billing.invoices.tsx
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "~/lib/prisma";
import { requireAuth } from "~/lib/auth/services/auth-service";

// GET /api/billing/invoices
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const user = await requireAuth(request);
    const tenantId = user.tenantId;
    
    if (!tenantId) {
      return json(
        { success: false, message: "Missing tenant context" },
        { status: 400 }
      );
    }

    const invoices = await prisma.invoice.findMany({
      where: { tenantId },
      include: { patient: true },
      orderBy: { invoiceDate: "desc" },
    });
    
    return json(invoices);
  } catch (error) {
    return json({ success: false, message: "Failed to load invoices" }, { status: 500 });
  }
}

// POST /api/billing/invoices
export async function action({ request }: ActionFunctionArgs) {
  try {
    const user = await requireAuth(request);
    const body = await request.json();
    const patientId = Number(body?.patientId);
    const ids = Array.isArray(body?.treatmentIds) 
      ? body.treatmentIds.map((x: any) => Number(x)).filter((n: number) => Number.isInteger(n) && n > 0) 
      : [];
    
    if (!Number.isInteger(patientId) || patientId <= 0) {
      return json({ success: false, message: "patientId must be a positive integer" }, { status: 400 });
    }

    const patient = await prisma.patient.findUnique({ where: { id: patientId } });
    if (!patient) {
      return json({ success: false, message: "Patient not found" }, { status: 404 });
    }

    // Fetch candidate treatments that belong to the patient's case sheet and are not linked to any invoice yet
    let treatments = [] as { id: number; cost: any }[];
    if (ids.length > 0) {
      treatments = await prisma.treatment.findMany({
        where: {
          id: { in: ids },
          invoiceId: null,
          finding: { tooth: { caseSheet: { patientId } } },
        },
        select: { id: true, cost: true },
      });
    } else {
      treatments = await prisma.treatment.findMany({
        where: {
          invoiceId: null,
          finding: { tooth: { caseSheet: { patientId } } },
        },
        select: { id: true, cost: true },
      });
    }

    const totalAmount = treatments.reduce((sum, t) => sum + Number(t.cost as any), 0);

    // If there are no eligible treatments to invoice:
    // 1) If specific treatmentIds were provided and they are already invoiced under a single invoice, return that invoice
    // 2) Otherwise, try to return an existing open invoice (DRAFT/SENT) for this patient
    // 3) If none found, do NOT create a new empty invoice; return 400 to avoid empty invoices
    if (treatments.length === 0) {
      // Case 1: All provided treatments already belong to a single invoice? Return it.
      if (ids.length > 0) {
        const selectedTreatments = await prisma.treatment.findMany({
          where: { id: { in: ids } },
          select: { invoiceId: true },
        });
        const invoiceIds = Array.from(
          new Set(
            selectedTreatments
              .map((t: { invoiceId: number | null }) => t.invoiceId)
              .filter((v: number | null): v is number => typeof v === 'number')
          )
        );
        if (invoiceIds.length === 1) {
          const existing = await prisma.invoice.findUnique({ where: { id: invoiceIds[0] } });
          if (existing) {
            return json({ success: true, invoice: existing }, { status: 200 });
          }
        }
      }

      // Case 2: Return an existing open invoice for the patient if any
      const openExisting = await prisma.invoice.findFirst({
        where: {
          patientId,
          OR: [{ status: "DRAFT" as any }, { status: "SENT" as any }, { status: "PARTIALLY_PAID" as any }],
        },
        orderBy: { invoiceDate: 'desc' },
      });
      if (openExisting) {
        return json({ success: true, invoice: openExisting }, { status: 200 });
      }

      // Case 3: Nothing to invoice and no open invoice to return
      return json({ success: false, message: "No eligible treatments to invoice" }, { status: 400 });
    }

    // Create invoice with a temporary unique number; then set to INV-{id}
    const serialCount = await prisma.invoiceSerial.count({ where: { tenantId: (patient as any).tenantId as string } });
    const serial = serialCount + 1;
    await prisma.invoiceSerial.create({ data: { tenantId: (patient as any).tenantId as string } });
    let invoice = await prisma.invoice.create({
      data: {
        tenantId: (patient as any).tenantId as string,
        patientId,
        serial: serial.toString(),
        invoiceDate: new Date(),
        status: "DRAFT" as any,
        totalAmount: totalAmount as any,
        amountPaid: 0 as any,
        balanceDue: totalAmount as any,
      },
    });

    // Update to deterministic invoice number INV-{invoiceId}
    invoice = await prisma.invoice.update({
      where: { id: invoice.id },
      data: { serial: `INV-${invoice.id}` },
    });

    if (treatments.length > 0) {
      await prisma.treatment.updateMany({
        where: { id: { in: treatments.map((t) => t.id) } },
        data: { invoiceId: invoice.id },
      });
    }

    return json({ success: true, invoice }, { status: 201 });
  } catch (error) {
    return json({ success: false, message: "Failed to create invoice" }, { status: 500 });
  }
}
```

### Dashboard Routes

#### Next.js `app/dashboard/page.tsx` → Remix `app/routes/dashboard._index.tsx`

**Next.js Implementation:**
```tsx
// app/dashboard/page.tsx:1-84
"use client"

import * as React from "react"
import dynamic from "next/dynamic"
import type { DashboardMetricsData } from "@/components/dashboard/DashboardMetrics"
import type { ActivityItem } from "@/components/dashboard/RecentActivity"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Toaster } from "@/components/ui/sonner"

export default function DashboardPage() {
  const router = useRouter()
  const [metrics, setMetrics] = React.useState<DashboardMetricsData | null>(null)
  const [activities, setActivities] = React.useState<ActivityItem[]>([])
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  const loadData = React.useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const res = await fetch('/api/dashboard', { credentials: 'include' })
      if (!res.ok) throw new Error('Failed to fetch metrics')
      const json = await res.json()
      const stats = json?.data?.stats
      const todayPatients = Number(stats?.todayPatients ?? 0)
      const totalPatients = Number(stats?.totalPatients ?? 0)
      const pendingTreatments = Number(stats?.pendingTreatments ?? 0)
      const outstandingInvoices = Number(stats?.outstandingInvoices ?? 0)
      const completedTreatments = Number(stats?.completedTreatments ?? 0)

      setMetrics({ todayPatients, totalPatients, pendingTreatments, outstandingInvoices, completedTreatments })
      setActivities(Array.isArray(json?.data?.recentActivity) ? json.data.recentActivity : [])
    } catch (e) {
      setError("Failed to load dashboard data")
    } finally {
      setLoading(false)
    }
  }, [])

  React.useEffect(() => {
    loadData()
    // Example toast to demonstrate wiring; remove or adjust as needed
    // import dynamically to avoid SSR mismatch
    import("sonner").then(({ toast }) => {
      toast.success("Dashboard loaded", { description: "Metrics and recent activity are up to date." })
    }).catch(() => {})
  }, [loadData])

  const DashboardMetrics = React.useMemo(
    () => dynamic(() => import("@/components/dashboard/DashboardMetrics"), { ssr: false }),
    []
  )

  const RecentActivity = React.useMemo(
    () => dynamic(() => import("@/components/dashboard/RecentActivity"), { ssr: false }),
    []
  )

  return (
    <div className="space-y-6">
      <section>
        <DashboardMetrics metrics={metrics ?? { todayPatients: 0, totalPatients: 0, pendingTreatments: 0, outstandingInvoices: 0, completedTreatments: 0 }} loading={loading} />
      </section>

      <section>
        <RecentActivity activities={activities} loading={loading} />
      </section>

      {error ? (
        <Card>
          <CardContent className="flex items-center justify-between gap-4 py-4">
            <div className="text-sm text-destructive">{error}</div>
            <Button size="sm" onClick={loadData}>Retry</Button>
          </CardContent>
        </Card>
      ) : null}
      <Toaster richColors closeButton />
    </div>
  )
}
```

**Remix Equivalent:**
```tsx
// app/routes/dashboard._index.tsx
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Toaster } from "~/components/ui/sonner";
import { requireAuth } from "~/lib/auth/services/auth-service";
import { getDashboardData } from "~/lib/dashboard-service";

// Server-side data loading
export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireAuth(request);
  
  try {
    const dashboardData = await getDashboardData(user.tenantId);
    return json(dashboardData);
  } catch (error) {
    throw new Response("Failed to load dashboard data", { status: 500 });
  }
}

// Client component for dashboard
export default function DashboardPage() {
  const data = useLoaderData<typeof loader>();
  
  // Extract metrics and activities from loader data
  const metrics = {
    todayPatients: data.stats?.todayPatients ?? 0,
    totalPatients: data.stats?.totalPatients ?? 0,
    pendingTreatments: data.stats?.pendingTreatments ?? 0,
    outstandingInvoices: data.stats?.outstandingInvoices ?? 0,
    completedTreatments: data.stats?.completedTreatments ?? 0,
  };
  
  const activities = Array.isArray(data?.recentActivity) ? data.recentActivity : [];

  return (
    <div className="space
<div className="space-y-6">
      <section>
        <DashboardMetrics metrics={metrics} loading={false} />
      </section>

      <section>
        <RecentActivity activities={activities} loading={false} />
      </section>

      <Toaster richColors closeButton />
    </div>
  );
}
```

## Dependency Migration

### Package.json Transformation

#### Next.js Dependencies
```json
// package.json (Next.js)
{
  "name": "dnd",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "test": "vitest",
    "test:run": "vitest run",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest run --coverage",
    "prisma:migrate": "prisma migrate dev --schema=prisma/schema",
    "prisma:generate": "prisma generate --schema=prisma/schema",
    "prisma:studio": "prisma studio --schema=prisma/schema",
    "prisma:push": "prisma db push --schema=prisma/schema"
  },
  "prisma": {
    "schema": "./prisma/schema"
  },
  "dependencies": {
    "@hookform/resolvers": "^5.2.1",
    "@ilamy/calendar": "^0.2.1",
    "@radix-ui/react-accordion": "^1.2.11",
    "@radix-ui/react-alert-dialog": "^1.1.14",
    "@radix-ui/react-aspect-ratio": "^1.1.7",
    "@radix-ui/react-avatar": "^1.1.10",
    "@radix-ui/react-checkbox": "^1.3.2",
    "@radix-ui/react-collapsible": "^1.1.11",
    "@radix-ui/react-context-menu": "^2.2.15",
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-dropdown-menu": "^2.1.15",
    "@radix-ui/react-hover-card": "^1.1.14",
    "@radix-ui/react-label": "^2.1.7",
    "@radix-ui/react-menubar": "^1.1.15",
    "@radix-ui/react-navigation-menu": "^1.2.13",
    "@radix-ui/react-popover": "^1.1.14",
    "@radix-ui/react-progress": "^1.1.7",
    "@radix-ui/react-radio-group": "^1.3.7",
    "@radix-ui/react-scroll-area": "^1.2.9",
    "@radix-ui/react-select": "^2.2.5",
    "@radix-ui/react-separator": "^1.1.7",
    "@radix-ui/react-slider": "^1.3.5",
    "@radix-ui/react-slot": "^1.2.3",
    "@radix-ui/react-switch": "^1.2.5",
    "@radix-ui/react-tabs": "^1.1.12",
    "@radix-ui/react-toggle": "^1.1.9",
    "@radix-ui/react-toggle-group": "^1.1.10",
    "@radix-ui/react-tooltip": "^1.2.7",
    "bcryptjs": "^3.0.2",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cmdk": "^1.1.1",
    "date-fns": "^4.1.0",
    "dayjs": "^1.11.13",
    "embla-carousel-react": "^8.6.0",
    "input-otp": "^1.4.2",
    "jose": "^5.10.0",
    "lucide-react": "^0.536.0",
    "next": "15.4.5",
    "next-themes": "^0.4.6",
    "react": "19.1.0",
    "react-day-picker": "^9.8.1",
    "react-dom": "19.1.0",
    "react-hook-form": "^7.62.0",
    "react-resizable-panels": "^3.0.4",
    "recharts": "2.15.4",
    "sonner": "^2.0.7",
    "tailwind-merge": "^3.3.1",
    "vaul": "^1.1.2",
    "zod": "^4.0.14"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3",
    "@prisma/client": "^6.13.0",
    "@tailwindcss/postcss": "^4",
    "@testing-library/jest-dom": "^6.6.4",
    "@testing-library/react": "^16.3.0",
    "@testing-library/user-event": "^14.6.1",
    "@types/bcryptjs": "^3.0.0",
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "@vitest/ui": "^3.2.4",
    "eslint": "^9",
    "eslint-config-next": "15.4.5",
    "jsdom": "^26.1.0",
    "prisma": "^6.13.0",
    "tailwindcss": "^4",
    "tw-animate-css": "^1.3.6",
    "typescript": "^5",
    "vitest": "^3.2.4"
  }
}
```

#### Remix Dependencies
```json
// package.json (Remix)
{
  "name": "dnd",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "remix dev --manual",
    "build": "remix build",
    "start": "remix-serve build/index.js",
    "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .",
    "typecheck": "tsc",
    "test": "vitest",
    "test:run": "vitest run",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest run --coverage",
    "prisma:migrate": "prisma migrate dev --schema=prisma/schema",
    "prisma:generate": "prisma generate --schema=prisma/schema",
    "prisma:studio": "prisma studio --schema=prisma/schema",
    "prisma:push": "prisma db push --schema=prisma/schema"
  },
  "prisma": {
    "schema": "./prisma/schema"
  },
  "dependencies": {
    "@hookform/resolvers": "^5.2.1",
    "@ilamy/calendar": "^0.2.1",
    "@phosphor-icons/react": "^2.1.0",
    "@radix-ui/react-accordion": "^1.2.11",
    "@radix-ui/react-alert-dialog": "^1.1.14",
    "@radix-ui/react-aspect-ratio": "^1.1.7",
    "@radix-ui/react-avatar": "^1.1.10",
    "@radix-ui/react-checkbox": "^1.3.2",
    "@radix-ui/react-collapsible": "^1.1.11",
    "@radix-ui/react-context-menu": "^2.2.15",
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-dropdown-menu": "^2.1.15",
    "@radix-ui/react-hover-card": "^1.1.14",
    "@radix-ui/react-label": "^2.1.7",
    "@radix-ui/react-menubar": "^1.1.15",
    "@radix-ui/react-navigation-menu": "^1.2.13",
    "@radix-ui/react-popover": "^1.1.14",
    "@radix-ui/react-progress": "^1.1.7",
    "@radix-ui/react-radio-group": "^1.3.7",
    "@radix-ui/react-scroll-area": "^1.2.9",
    "@radix-ui/react-select": "^2.2.5",
    "@radix-ui/react-separator": "^1.1.7",
    "@radix-ui/react-slider": "^1.3.5",
    "@radix-ui/react-slot": "^1.2.3",
    "@radix-ui/react-switch": "^1.2.5",
    "@radix-ui/react-tabs": "^1.1.12",
    "@radix-ui/react-toggle": "^1.1.9",
    "@radix-ui/react-toggle-group": "^1.1.10",
    "@radix-ui/react-tooltip": "^1.2.7",
    "@remix-run/node": "^2.15.0",
    "@remix-run/react": "^2.15.0",
    "@remix-run/serve": "^2.15.0",
    "bcryptjs": "^3.0.2",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cmdk": "^1.1.1",
    "date-fns": "^4.1.0",
    "dayjs": "^1.11.13",
    "embla-carousel-react": "^8.6.0",
    "input-otp": "^1.4.2",
    "isbot": "^5.1.17",
    "jose": "^5.10.0",
    "react": "^19.1.0",
    "react-day-picker": "^9.8.1",
    "react-dom": "^19.1.0",
    "react-hook-form": "^7.62.0",
    "react-resizable-panels": "^3.0.4",
    "recharts": "2.15.4",
    "sonner": "^2.0.7",
    "tailwind-merge": "^3.3.1",
    "vaul": "^1.1.2",
    "zod": "^4.0.14"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3",
    "@prisma/client": "^6.13.0",
    "@remix-run/dev": "^2.15.0",
    "@remix-run/eslint-config": "^2.15.0",
    "@remix-run/testing": "^2.15.0",
    "@tailwindcss/postcss": "^4",
    "@testing-library/jest-dom": "^6.6.4",
    "@testing-library/react": "^16.3.0",
    "@testing-library/user-event": "^14.6.1",
    "@types/bcryptjs": "^3.0.0",
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "@vitest/ui": "^3.2.4",
    "autoprefixer": "^10.4.20",
    "eslint": "^9",
    "jsdom": "^26.1.0",
    "postcss": "^8.4.49",
    "prisma": "^6.13.0",
    "tailwindcss": "^4",
    "tw-animate-css": "^1.3.6",
    "typescript": "^5",
    "vite": "^6.0.0",
    "vitest": "^3.2.4"
  },
  "engines": {
    "node": ">=20.0.0"
  }
}
```

### Key Dependency Changes

1. **Framework Dependencies**
   - Remove: `next`, `next-themes`
   - Add: `@remix-run/node`, `@remix-run/react`, `@remix-run/serve`, `@remix-run/dev`
   - Add: `isbot` for bot detection

2. **Icon Library Migration**
   - Remove: `lucide-react`
   - Add: `@phosphor-icons/react`

3. **Development Dependencies**
   - Remove: `eslint-config-next`
   - Add: `@remix-run/eslint-config`, `@remix-run/testing`
   - Add: `vite` for build tooling
   - Add: `autoprefixer`, `postcss` for CSS processing

4. **Script Updates**
   - Replace Next.js scripts with Remix equivalents
   - Add `typecheck` script for TypeScript validation
   - Update dev script to use `--manual` flag for better control

## Risk Identification and Mitigation

### High-Risk Areas

#### 1. Authentication System Migration
**Risk**: The custom JWT-based authentication system in Next.js uses middleware and cookies, which has different implementation patterns in Remix.

**Mitigation Strategy**:
- Implement Remix's session storage utilities
- Create a compatibility layer for existing JWT tokens
- Use Remix's `createCookieSessionStorage` for session management
- Implement gradual migration with fallback support

**Implementation Example**:
```tsx
// app/utils/session.server.ts
import { createCookieSessionStorage } from "@remix-run/node";
import { SignJWT, jwtVerify } from "jose";

const sessionSecret = process.env.SESSION_SECRET;
if (!sessionSecret) {
  throw new Error("SESSION_SECRET environment variable is required");
}

const storage = createCookieSessionStorage({
  cookie: {
    name: "dnd-session",
    secure: process.env.NODE_ENV === "production",
    secrets: [sessionSecret],
    sameSite: "lax",
    path: "/",
    maxAge: 60 * 60 * 24 * 30, // 30 days
    httpOnly: true,
  },
});

export async function createUserSession(userId: string, tenantId: string, redirectTo: string) {
  const session = await storage.getSession();
  session.set("userId", userId);
  session.set("tenantId", tenantId);
  
  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": await storage.commitSession(session),
    },
  });
}

export async function getUserSession(request: Request) {
  return storage.getSession(request.headers.get("Cookie"));
}

export async function getUserId(request: Request): Promise<string | undefined> {
  const session = await getUserSession(request);
  const userId = session.get("userId");
  return userId;
}

export async function requireUserId(request: Request, redirectTo: string = new URL(request.url).pathname) {
  const userId = await getUserId(request);
  if (!userId || typeof userId !== "string") {
    const searchParams = new URLSearchParams([["redirectTo", redirectTo]]);
    throw redirect(`/auth/login?${searchParams}`);
  }
  return userId;
}

export async function getUser(request: Request) {
  const userId = await getUserId(request);
  if (userId === undefined) {
    return null;
  }
  
  const user = await getUserById(userId);
  if (user) {
    return user;
  }
  
  throw await logout(request);
}

export async function logout(request: Request) {
  const session = await getUserSession(request);
  return redirect("/", {
    headers: {
      "Set-Cookie": await storage.destroySession(session),
    },
  });
}
```

#### 2. Multi-Tenant Architecture
**Risk**: Tenant context propagation in Next.js relies on middleware and request headers, which needs to be adapted to Remix's loader/action pattern.

**Mitigation Strategy**:
- Implement tenant context as a Remix utility
- Create a `requireTenant` function for route protection
- Use loader functions to establish tenant context
- Implement tenant-aware data access patterns

**Implementation Example**:
```tsx
// app/utils/tenant.server.ts
import { json } from "@remix-run/node";
import { prisma } from "~/lib/prisma";
import { requireUserId } from "~/utils/session.server";

export async function requireTenant(request: Request) {
  const userId = await requireUserId(request);
  
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { tenant: true },
  });
  
  if (!user?.tenant) {
    throw json({ error: "User not associated with any tenant" }, { status: 403 });
  }
  
  return {
    user,
    tenant: user.tenant,
    tenantId: user.tenant.id,
  };
}

export async function createTenantContext(request: Request) {
  const { user, tenant, tenantId } = await requireTenant(request);
  
  return {
    user,
    tenant,
    tenantId,
    // Add any additional context needed
  };
}
```

#### 3. Data Fetching Patterns
**Risk**: Next.js uses Server Components and SWR for data fetching, while Remix uses loaders and actions. This pattern shift requires significant refactoring.

**Mitigation Strategy**:
- Create a data access layer that works with both patterns
- Implement Remix loaders for all data fetching
- Use `useLoaderData` and `useFetcher` hooks for client-side data
- Maintain existing data models and business logic

**Implementation Example**:
```tsx
// app/routes/dashboard._index.tsx
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { requireTenant } from "~/utils/tenant.server";
import { getDashboardMetrics, getRecentActivity } from "~/lib/dashboard-service";

export async function loader({ request }: LoaderFunctionArgs) {
  const { tenantId } = await requireTenant(request);
  
  try {
    const [metrics, activity] = await Promise.all([
      getDashboardMetrics(tenantId),
      getRecentActivity(tenantId),
    ]);
    
    return json({
      metrics,
      activity,
      success: true,
    });
  } catch (error) {
    console.error("Dashboard loader error:", error);
    return json(
      { 
        success: false, 
        error: "Failed to load dashboard data" 
      }, 
      { status: 500 }
    );
  }
}

export default function Dashboard() {
  const { metrics, activity, success, error } = useLoaderData<typeof loader>();
  
  if (!success) {
    return <div className="error">{error}</div>;
  }
  
  return (
    <div>
      {/* Render dashboard with metrics and activity */}
    </div>
  );
}
```

#### 4. Component Library Migration
**Risk**: shadcn/ui components need to be adapted for Remix, and icon library migration from Lucide to Phosphor requires manual updates.

**Mitigation Strategy**:
- Create a compatibility layer for shadcn/ui components
- Implement a systematic icon replacement process
- Use automated scripts for bulk icon replacement
- Test component functionality thoroughly

**Implementation Example**:
```tsx
// components/ui/button.tsx (Remix version)
import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "~/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost:
          "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
```

#### 5. Middleware Migration
**Risk**: Next.js middleware handles authentication, tenant context, and route protection, which needs to be reimplemented in Remix.

**Mitigation Strategy**:
- Convert middleware logic to Remix loader/action functions
- Implement route protection at the loader level
- Create utility functions for common middleware patterns
- Use Remix's `loader` and `action` for request interception

**Implementation Example**:
```tsx
// app/utils/auth.server.ts
import { redirect, json } from "@remix-run/node";
import { prisma } from "~/lib/prisma";

export async function authenticateUser(request: Request) {
  const authHeader = request.headers.get("Authorization");
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { success: false, error: "Missing or invalid authorization header" };
  }
  
  const token = authHeader.substring(7);
  
  try {
    // Verify JWT token
    const user = await verifyToken(token);
    
    if (!user.isActive) {
      return { success: false, error: "User account is inactive" };
    }
    
    return { success: true, user };
  } catch (error) {
    return { success: false, error: "Invalid token" };
  }
}

export async function requireAuth(request: Request) {
  const result = await authenticateUser(request);
  
  if (!result.success) {
    throw json(
      { success: false, message: result.error },
      { status: 401 }
    );
  }
  
  return result.user;
}

// Example usage in a loader
export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireAuth(request);
  
  // Continue with authenticated logic
  return json({ data: "Protected data" });
}
```

### Medium-Risk Areas

#### 1. Form Handling
**Risk**: React Hook Form patterns need to be adapted for Remix's action functions.

**Mitigation Strategy**:
- Maintain React Hook Form for client-side validation
- Use Remix actions for form submission
- Implement proper error handling and validation feedback
- Create form submission utilities

#### 2. Styling and CSS
**Risk**: Tailwind CSS configuration and custom styles need to be preserved.

**Mitigation Strategy**:
- Copy Tailwind configuration as-is
- Ensure CSS imports are properly set up in Remix
- Test all components for styling consistency
- Maintain existing design tokens and themes

#### 3. Testing Framework
**Risk**: Test files need to be adapted from Next.js to Remix patterns.

**Mitigation Strategy**:
- Update test utilities for Remix
- Adapt component tests for new data loading patterns
- Maintain test coverage during migration
- Use Remix's testing utilities where appropriate

### Low-Risk Areas

#### 1. Utility Functions
**Risk**: Most utility functions can be migrated with minimal changes.

**Mitigation Strategy**:
- Copy utilities as-is
- Update import paths as needed
- Test utility functions in isolation

#### 2. Database Schema
**Risk**: Prisma schema and migrations remain unchanged.

**Mitigation Strategy**:
- Keep existing Prisma setup
- Ensure database connection works in Remix environment
- Test all database operations

#### 3. Type Definitions
**Risk**: TypeScript types need minimal updates.

**Mitigation Strategy**:
- Copy type definitions as-is
- Update Remix-specific types
- Ensure type safety is maintained

## Validation and Testing

### Testing Strategy

#### 1. Unit Testing
**Objective**: Verify individual functions and components work correctly in isolation.

**Implementation**:
```tsx
// tests/utils/auth.server.test.ts
import { describe, it, expect, vi, beforeEach } from "vitest";
import { createRequest } from "@remix-run/node";
import { authenticateUser, requireAuth } from "~/utils/auth.server";

// Mock the database
vi.mock("~/lib/prisma", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
    },
  },
}));

describe("Authentication Utilities", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("authenticateUser", () => {
    it("should authenticate user with valid token", async () => {
      // Setup test
      const request = createRequest("/", {
        headers: {
          Authorization: "Bearer valid-token",
        },
      });
      
      // Mock token verification
      vi.mock("~/lib/jwt", () => ({
        verifyToken: vi.fn().mockResolvedValue({
          id: "user-123",
          email: "<EMAIL>",
          isActive: true,
        }),
      }));
      
      // Execute test
      const result = await authenticateUser(request);
      
      // Verify result
      expect(result.success).toBe(true);
      expect(result.user).toEqual({
        id: "user-123",
        email: "<EMAIL>",
        isActive: true,
      });
    });

    it("should reject request with missing authorization header", async () => {
      const request = createRequest("/");
      
      const result = await authenticateUser(request);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe("Missing or invalid authorization header");
    });
  });

  describe("requireAuth", () => {
    it("should return user for authenticated request", async () => {
      const request = createRequest("/", {
        headers: {
          Authorization: "Bearer valid-token",
        },
      });
      
      vi.mock("~/lib/jwt", () => ({
        verifyToken: vi.fn().mockResolvedValue({
          id: "user-123",
          email: "<EMAIL>",
          isActive: true,
        }),
      }));
      
      const user = await requireAuth(request);
      
      expect(user).toEqual({
        id: "user-123",
        email: "<EMAIL>",
        isActive: true,
      });
    });

    it("should throw error for unauthenticated request", async () => {
      const request = createRequest("/");
      
      await expect(requireAuth(request)).rejects.toThrow();
    });
  });
});
```

#### 2. Integration Testing
**Objective**: Verify that components work together correctly with Remix's data loading patterns.

**Implementation**:
```tsx
// tests/app/dashboard.test.tsx
import { describe, it, expect, vi, beforeEach } from "vitest";
import { json } from "@remix-run/node";
import { createRemixStub } from "@remix-run/testing";
import { render, screen, waitFor } from "@testing-library/react";
import { Dashboard } from "~/routes/dashboard._index";

// Mock the loader function
vi.mock("~/routes/dashboard._index", async () => {
  const actual = await vi.importActual("~/routes/dashboard._index");
  return {
    ...actual,
    loader: () => json({
      metrics: {
        todayPatients: 5,
        totalPatients: 120,
        pendingTreatments: 8,
        outstandingInvoices: 3,
        completedTreatments: 45,
      },
      activity: [
        { id: "1", type: "appointment", description: "New appointment scheduled" },
        { id: "2", type: "invoice", description: "Invoice #INV-001 created" },
      ],
      success: true,
    }),
  };
});

describe("Dashboard Integration", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render dashboard with metrics and activity", async () => {
    const RemixStub = createRemixStub([
      {
        path: "/dashboard",
        element: <Dashboard />,
        loader: () => json({
          metrics: {
            todayPatients: 5,
            totalPatients: 120,
            pendingTreatments: 8,
            outstandingInvoices: 3,
            completedTreatments: 45,
          },
          activity: [
            { id: "1", type: "appointment", description: "New appointment scheduled" },
            { id: "2", type: "invoice", description: "Invoice #INV-001 created" },
          ],
          success: true,
        }),
      },
    ]);

    render(<RemixStub initialEntries={["/dashboard"]} />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText("Dashboard")).toBeInTheDocument();
    });

    // Verify metrics are displayed
    expect(screen.getByText("5")).toBeInTheDocument(); // todayPatients
    expect(screen.getByText("120")).toBeInTheDocument(); // totalPatients
    
    // Verify activity is displayed
    expect(screen.getByText("New appointment scheduled")).toBeInTheDocument();
    expect(screen.getByText("Invoice #INV-001 created")).toBeInTheDocument();
  });

  it("should display error message when data loading fails", async () => {
    const RemixStub = createRemixStub([
      {
        path: "/dashboard",
        element: <Dashboard />,
        loader: () => json(
          { success: false, error: "Failed to load dashboard data" },
          { status: 500 }
        ),
      },
    ]);

    render(<RemixStub initialEntries={["/dashboard"]} />);

    // Wait for error to be displayed
    await waitFor(() => {
      expect(screen.getByText("Failed to load dashboard data")).toBeInTheDocument();
    });
  });
});
```

#### 3. End-to-End Testing
**Objective**: Verify complete user flows work correctly in the migrated application.

**Implementation**:
```typescript
// tests/e2e/authentication.spec.ts
import { test, expect } from "@playwright/test";

test.describe("Authentication Flow", () => {
  test("should allow user to login and access dashboard", async ({ page }) => {
    // Navigate to login page
    await page.goto("/auth/login");
    
    // Verify login form is displayed
    await expect(page.locator("h1")).toHaveText("Login");
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    
    // Fill login form
    await page.fill('input[name="email"]', "<EMAIL>");
    await page.fill('input[name="password"]', "password123");
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Verify redirect to dashboard
    await expect(page).toHaveURL("/dashboard");
    
    // Verify dashboard content
    await expect(page.locator("h1")).toHaveText("Dashboard");
    await expect(page.locator("text=Patients")).toBeVisible();
    await expect(page.locator("text=Appointments")).toBeVisible();
  });

  test("should redirect unauthenticated user to login page", async ({ page }) => {
    // Try to access dashboard directly
    await page.goto("/dashboard");
    
    // Verify redirect to login
    await expect(page).toHaveURL("/auth/login");
    
    // Verify redirect parameter is preserved
    await expect(page.locator('input[name="redirect"]')).toHaveValue("/dashboard");
  });

  test("should allow user to logout", async ({ page }) => {
    // Login first
    await page.goto("/auth/login");
    await page.fill('input[name="email"]', "<EMAIL>");
    await page.fill('input[name="password"]', "password123");
    await page.click('button[type="submit"]');
    
    // Verify dashboard is loaded
    await expect(page).toHaveURL("/dashboard");
    
    // Logout
    await page.click('button:has-text("Logout")');
    
    // Verify redirect to home page
    await expect(page).toHaveURL("/");
    
    // Try to access dashboard again
    await page.goto("/dashboard");
    
    // Verify redirect to login
    await expect(page).toHaveURL("/auth/login");
  });
});
```

### Validation Checklist

#### Phase 1: Foundation Setup
- [ ] Remix project builds successfully
- [ ] Development server starts without errors
- [ ] Basic route structure matches Next.js organization
- [ ] Tailwind CSS styling works correctly
- [ ] TypeScript configuration is properly set up
- [ ] All dependencies are installed correctly
- [ ] Hot reload works during development

#### Phase 2: Core Infrastructure
- [ ] Authentication flow works end-to-end
- [ ] Database queries execute successfully through Remix loaders
- [ ] Tenant context is properly propagated
- [ ] Protected routes require authentication
- [ ] User sessions persist correctly
- [ ] Error handling works for authentication failures
- [ ] JWT token validation works correctly

#### Phase 3: Route Migration
- [ ] All page routes render correctly
- [ ] API endpoints respond with expected data
- [ ] Dynamic routes handle parameters correctly
- [ ] Navigation between routes works seamlessly
- [ ] Form submissions process correctly
- [ ] Route-based code splitting works
- [ ] Meta tags are properly set

#### Phase 4: Component Migration
- [ ] All components render without errors
- [ ] Forms submit and validate correctly
- [ ] Icons display properly with Phosphor
- [ ] Layouts maintain consistent structure
- [ ] Component boundaries are correctly defined
- [ ] shadcn/ui components work correctly
- [ ] Client-side state management works

#### Phase 5: Testing and Optimization
- [ ] All tests pass with new Remix implementation
- [ ] Performance metrics meet or exceed Next.js implementation
- [ ] Error handling covers all edge cases
- [ ] Bundle size is optimized
- [ ] Application is production-ready
- [ ] Accessibility requirements are met
- [ ] Security vulnerabilities are addressed

### Performance Validation

#### Load Testing
```bash
# Install k6 for load testing
npm install -g k6

# Create load test script
# k6/load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '30s', target: 20 },  // Ramp up to 20 users
    { duration: '1m', target: 20 },   // Stay at 20 users
    { duration: '20s', target: 0 },   // Ramp down
  ],
};

export default function () {
  let res = http.get('https://your-remix-app.com/dashboard');
  check(res, {
    'status was 200': (r) => r.status == 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });
  sleep(1);
}

# Run load test
k6 run k6/load-test.js
```

#### Bundle Analysis
```bash
# Install bundle analyzer
npm install --save-dev @remix-run/dev-bundle-analyzer

# Add to package.json scripts
"analyze": "remix build --analyze"

# Run analysis
npm run analyze
```

## Rollback Plan

### Pre-Migration Backup Strategy

#### 1. Code Repository Backup
```bash
# Create a backup branch
git checkout -b nextjs-backup-$(date +%Y%m%d-%H%M%S)

# Tag the backup for easy reference
git tag -a nextjs-backup-$(date +%Y%m%d-%H%M%S) -m "Next.js backup before Remix migration"

# Push backup to remote
git push origin nextjs-backup-$(date +%Y%m%d-%H%M%S)
git push origin --tags
```

#### 2. Database Backup
```bash
# Create database backup
pg_dump -h localhost -U username -d database_name > backup-$(date +%Y%m%d-%H%M%S).sql

# Compress backup
gzip backup-$(date +%Y%m%d-%H%M%S).sql

# Store backup securely
scp backup-$(date +%Y%m%d-%H%M%S).sql.gz user@backup-server:/backups/
```

#### 3. Environment Configuration Backup
```bash
# Backup environment files
cp .env .env.backup-$(date +%Y%m%d-%H%M%S)
cp .env.local .env.local.backup-$(date +%Y%m%d-%H%M%S)

# Backup configuration files
cp next.config.ts next.config.ts.backup-$(date +%Y%m%d-%H%M%S)
cp tailwind.config.js tailwind.config.js.backup-$(date +%Y%m%d-%H%M%S)
cp tsconfig.json tsconfig.json.backup-$(date +%Y%m%d-%H%M%S)
```

### Rollback Triggers

#### Critical Issues Requiring Immediate Rollback
1. **Data Corruption**
   - Database integrity compromised
   - Data loss during migration
   - Inconsistent data across tenants

2. **Security Vulnerabilities**
   - Authentication bypass discovered
   - Tenant isolation compromised
   - Unauthorized access to sensitive data

3. **Performance Degradation**
   - Response times increased by > 200%
   - Error rates exceeding 5%
   - Memory leaks causing crashes

4. **Functional Failures**
   - Core business processes broken
   - Payment processing failures
   - Appointment scheduling failures

#### Monitoring Metrics
```typescript
// monitoring/rollback-triggers.ts
export interface RollbackMetrics {
  errorRate: number; // Percentage of failed requests
  responseTime: number; // Average response time in ms
  memoryUsage: number; // Memory usage percentage
  activeUsers: number; // Number of active users
  failedLogins: number; // Number of failed login attempts
}

export function checkRollbackTriggers(metrics: RollbackMetrics): {
  shouldRollback: boolean;
  reason: string;
} {
  // Check error rate
  if (metrics.errorRate > 5) {
    return {
      shouldRollback: true,
      reason: `Error rate too high: ${metrics.errorRate}%`,
    };
  }

  // Check response time
  if (metrics.responseTime > 1000) {
    return {
      shouldRollback: true,
      reason: `Response time too high: ${metrics.responseTime}ms`,
    };
  }

  // Check memory usage
  if (metrics.memoryUsage > 90) {
    return {
      shouldRollback: true,
      reason: `Memory usage too high: ${metrics.memoryUsage}%`,
    };
  }

  // Check failed logins
  if (metrics.failedLogins > metrics.activeUsers * 0.1) {
    return {
      shouldRollback: true,
      reason: `Too many failed logins: ${metrics.failedLogins}`,
    };
  }

  return {
    shouldRollback: false,
    reason: "All metrics within acceptable range",
  };
}
```

### Rollback Procedures

#### 1. Immediate Rollback (Emergency)
```bash
# Stop Remix application
pm2 stop remix-app

# Switch to Next.js backup branch
git checkout nextjs-backup-$(date +%Y%m%d-%H%M%S)

# Restore Next.js dependencies
rm -rf node_modules package-lock.json
npm install

# Restore environment configuration
cp .env.backup-$(date +%Y%m%d-%H%M%S) .env
cp .env.local.backup-$(date +%Y%m%d-%H%M%S) .env.local

# Build and start Next.js application
npm run build
pm2 start npm --name "nextjs-app" -- start

# Verify rollback
curl -f https://your-app.com/health || exit 1
```

#### 2. Gradual Rollback (Phased)
```bash
# Phase 1: Switch to maintenance mode
cp maintenance.html maintenance.html.backup
echo "System maintenance in progress" > maintenance.html

# Phase 2: Restore database if needed
# Only if data corruption occurred
gunzip backup-$(date +%Y%m%d-%H%M%S).sql.gz
psql -h localhost -U username -d database_name < backup-$(date +%Y%m%d-%H%M%S).sql

# Phase 3: Restore Next.js application
git checkout nextjs-backup-$(date +%Y%m%d-%H%M%S)
npm install
npm run build

# Phase 4: Disable maintenance mode
mv maintenance.html.backup maintenance.html

# Phase 5: Restart services
pm2 restart all
```

#### 3. Database Rollback Script
```bash
#!/bin/bash
# scripts/rollback-database.sh

set -e

# Configuration
DB_HOST="localhost"
DB_USER="username"
DB_NAME="database_name"
BACKUP_DIR="/backups"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# Find latest backup
LATEST_BACKUP=$(ls -t $BACKUP_DIR/*.sql.gz | head -n1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "No backup found!"
    exit 1
fi

echo "Rolling back database using: $LATEST_BACKUP"

# Create pre-rollback backup
echo "Creating pre-rollback backup..."
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME | gzip > $BACKUP_DIR/prerollback-$TIMESTAMP.sql.gz

# Stop application
echo "Stopping application..."
pm2 stop remix-app

# Restore database
echo "Restoring database..."
gunzip -c $LATEST_BACKUP | psql -h $DB_HOST -U $DB_USER -d $DB_NAME

# Start application
echo "Starting application..."
pm2 start remix-app

echo "Rollback completed successfully!"
```

### Side-by-Side Verification

#### 1. A/B Testing Setup
```typescript
// app/utils/ab-testing.server.ts
import { createCookie } from "@remix-run/node";

export const abTestCookie = createCookie("ab-test", {
  secrets: [process.env.SESSION_SECRET || "default-secret"],
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax",
});

export async function getABTestGroup(request: Request): Promise<"nextjs" | "remix"> {
  const cookieHeader = request.headers.get("Cookie");
  const abTestValue = await abTestCookie.parse(cookieHeader);
  
  if (abTestValue) {
    return abTestValue;
  }
  
  // Assign to random group (50/50 split)
  const group = Math.random() < 0.5 ? "nextjs" : "remix";
  
  return group;
}

export async function setABTestGroup(group: "nextjs" | "remix", headers: Headers = new Headers()) {
  headers.append("Set-Cookie", await abTestCookie.serialize(group));
  return headers;
}
```

#### 2. Performance Comparison
```typescript
// monitoring/performance-comparison.ts
export interface PerformanceMetrics {
  timestamp: Date;
  version: "nextjs" | "remix";
  responseTime: number;
  memoryUsage: number;
  errorRate: number;
  throughput: number;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];

  async recordMetrics(version: "nextjs" | "remix"): Promise<PerformanceMetrics> {
    const metrics: PerformanceMetrics = {
      timestamp: new Date(),
      version,
      responseTime: await this.measureResponseTime(),
      memoryUsage: await this.measureMemoryUsage(),
      errorRate: await this.measureErrorRate(),
      throughput: await this.measureThroughput(),
    };

    this.metrics.push(metrics);
    return metrics;
  }

  private async measureResponseTime(): Promise<number> {
    const start = Date.now();
    await fetch("https://your-app.com/api/health");
    return Date.now() - start;
  }

  private async measureMemoryUsage(): Promise<number> {
    // Implementation depends on your hosting environment
    return process.memoryUsage().heapUsed / process.memoryUsage().heapTotal * 100;
  }

  private async measureErrorRate(): Promise<number> {
    // Implementation depends on your error tracking
    return 0; // Placeholder
  }

  private async measureThroughput(): Promise<number> {
    // Implementation depends on your monitoring setup
    return 0; // Placeholder
  }

  generateReport(): {
    nextjs: PerformanceMetrics[];
    remix: PerformanceMetrics[];
    comparison: {
      responseTime: { nextjs: number; remix: number; improvement: number };
      memoryUsage: { nextjs: number; remix: number; improvement: number };
      errorRate: { nextjs: number; remix: number; improvement: number };
      throughput: { nextjs: number; remix: number; improvement: number };
    };
  } {
    const nextjsMetrics = this.metrics.filter(m => m.version === "nextjs");
    const remixMetrics = this.metrics.filter(m => m.version === "remix");

    const avgNextjsResponseTime = this.calculateAverage(nextjsMetrics.map(m => m.responseTime));
    const avgRemixResponseTime = this.calculateAverage(remixMetrics.map(m => m.responseTime));

    const avgNextjsMemoryUsage = this.calculateAverage(nextjsMetrics.map(m => m.memoryUsage));
    const avgRemixMemoryUsage = this.calculateAverage(remixMetrics.map(m => m.memoryUsage));

    const avgNextjsErrorRate = this.calculateAverage(nextjsMetrics.map(m => m.errorRate));
    const avgRemixErrorRate = this.calculateAverage(remixMetrics.map(m => m.errorRate));

    const avgNextjsThroughput = this.calculateAverage(nextjsMetrics.map(m => m.throughput));
    const avgRemixThroughput = this.calculateAverage(remixMetrics.map(m => m.throughput));

    return {
      nextjs: nextjsMetrics,
      remix: remixMetrics,
      comparison: {
        responseTime: {
          nextjs: avgNextjsResponseTime,
          remix: avgRemixResponseTime,
          improvement: ((avgNextjsResponseTime - avgRemixResponseTime) / avgNextjsResponseTime) * 100,
        },
        memoryUsage: {
          nextjs: avgNextjsMemoryUsage,
          remix: avgRemixMemoryUsage,
          improvement: ((avgNextjsMemoryUsage - avgRemixMemoryUsage) / avgNextjsMemoryUsage) * 100,
        },
        errorRate: {
          nextjs: avgNextjsErrorRate,
          remix: avgRemixErrorRate,
          improvement: ((avgNextjsErrorRate - avgRemixErrorRate) / avgNextjsErrorRate) * 100,
        },
        throughput: {
          nextjs: avgNextjsThroughput,
          remix: avgRemixThroughput,
          improvement: ((avgRemixThroughput - avgNextjsThroughput) / avgNextjsThroughput) * 100,
        },
      },
    };
  }

  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }
}
```

#### 3. User Feedback Collection
```typescript
// app/components/feedback-collector.tsx
import { useState } from "react";
import { Form } from "@remix-run/react";

export function FeedbackCollector() {
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Submit feedback to analytics service
    setSubmitted(true);
    setTimeout(() => {
      setShowFeedback(false);
      setSubmitted(false);
      setFeedback("");
    }, 3000);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!showFeedback ? (
        <button
          onClick={() => setShowFeedback(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
        >
          Feedback
        </button>
      ) : (
        <div className="bg-white p-4 rounded-lg shadow-lg w-80">
          {submitted ? (
            <div className="text-green-600 text-center">
              Thank you for your feedback!
            </div>
          ) : (
            <Form method="post" onSubmit={handleSubmit}>
              <h3 className="font-semibold mb-2">Share your experience</h3>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="w-full p-2 border rounded mb-2"
                rows={3}
                placeholder="What do you think of the new interface?"
                required
              />
              <div className="flex gap-2">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
                >
                  Submit
                </button>
                <button
                  type="button"
                  onClick={() => setShowFeedback(false)}
                  className="bg-gray-300 text-gray-700 px-3 py-1 rounded hover:bg-gray-400 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </Form>
          )}
        </div>
      )}
    </div>
  );
}
```

### Rollback Verification Checklist

#### Pre-Rollback Verification
- [ ] Identify the specific issue requiring rollback
- [ ] Confirm rollback trigger conditions are met
- [ ] Verify backup availability and integrity
- [ ] Notify stakeholders about planned rollback
- [ ] Schedule rollback during maintenance window
- [ ] Prepare rollback team and communication plan

#### During Rollback Verification
- [ ] Stop Remix application successfully
- [ ] Restore Next.js codebase without errors
- [ ] Restore database without data loss
- [ ] Restart Next.js application successfully
- [ ] Verify all services are running
- [ ] Confirm application is accessible

#### Post-Rollback Verification
- [ ] All core functionality works correctly
- [ ] Performance metrics return to baseline
- [ ] Error rates return to normal levels
- [ ] User authentication works correctly
- [ ] Data integrity is maintained
- [ ] All integrations function properly
- [ ] Stakeholders confirm rollback success

### Communication Plan

#### Rollback Notification Template
```
Subject: URGENT: Application Rollback in Progress

Dear Stakeholders,

We are performing an emergency rollback of the Remix migration due to:
[Specific issue - e.g., performance degradation, security vulnerability, etc.]

Timeline:
- Rollback initiated: [Timestamp]
- Expected completion: [Timestamp]
- Maintenance window: [Start time] - [End time]

Impact:
- Application will be unavailable during rollback
- All active sessions will be terminated
- Data entered during the migration period may be lost

We apologize for any inconvenience and appreciate your patience.

Regards,
Development Team
```

#### Post-Rollback Communication Template
```
Subject: Application Rollback Completed

Dear Stakeholders,

The application rollback has been completed successfully.

Summary:
- Rollback completed at: [Timestamp]
- Total downtime: [Duration]
- Data loss: [None/Minimal/Extensive]
- Root cause: [Analysis of what went wrong]

Next Steps:
- We will analyze the migration issues
- A new migration plan will be developed
- We will communicate the new timeline once available

We apologize for any inconvenience caused by this incident.

Regards,
Development Team
```

## Conclusion

This comprehensive migration guide provides a detailed roadmap for transitioning the DnD application from Next.js to Remix. The guide covers all aspects of the migration process, including:

1. **Project Analysis**: A thorough understanding of the current Next.js application structure, dependencies, and patterns.

2. **Phased Migration Plan**: A structured approach with clear phases, tasks, and completion criteria to ensure a smooth transition.

3. **File-by-File Mapping**: Detailed mapping of Next.js files to their Remix equivalents with code examples showing the transformation.

4. **Dependency Migration**: A complete transformation of the package.json with explanations of key dependency changes.

5. **Risk Mitigation**: Identification of high, medium, and low-risk areas with specific mitigation strategies and implementation examples.

6. **Testing Strategy**: Comprehensive testing approaches including unit, integration, and end-to-end testing with code examples.

7. **Rollback Plan**: A detailed rollback strategy with backup procedures, triggers, and verification steps to ensure business continuity.

8. **Side-by-Side Verification**: A/B testing approach and performance comparison to validate the migration success.

By following this guide, the development team can execute a systematic migration from Next.js to Remix while minimizing risks and ensuring business continuity. The guide is designed to be deterministic and verifiable, making it suitable for LLM execution while maintaining the high standards required for production applications.

The migration preserves all existing functionality while leveraging Remix's strengths in data loading, routing, and server-side rendering. The result will be a more maintainable, performant, and scalable application that meets the requirements of modern dental practice management.