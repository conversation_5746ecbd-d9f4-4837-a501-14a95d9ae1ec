#!/bin/bash

# Vercel Build Script for Prisma + LibSQL/Turso
echo "Starting Vercel build process..."

# Always generate Prisma client (this doesn't require DATABASE_URL)
echo "Generating Prisma client..."
npx prisma generate --schema=prisma/schema

# Check if we have database connection for migrations
if [ -n "$DATABASE_URL" ] && [ -n "$TURSO_DATABASE_URL" ]; then
    echo "Database environment variables found, running migrations..."
    
    # Deploy migrations to production database
    npx prisma migrate deploy --schema=prisma/schema
    npx prisma push --schema=prisma/schema
    
    echo "Migrations completed successfully"
else
    echo "Warning: DATABASE_URL or TURSO_DATABASE_URL not found"
    echo "Skipping database migrations during build"
    echo "Make sure to run migrations separately after deployment"
fi

# Build Next.js application
echo "Building Next.js application..."
npx next build

echo "Build process completed!"