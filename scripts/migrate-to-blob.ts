import { readdir, readFile, stat } from 'fs/promises';
import { join } from 'path';
import { put } from '@vercel/blob';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface MigrationResult {
  success: boolean;
  migratedFiles: number;
  failedFiles: number;
  errors: string[];
}

export async function migrateToBlob(): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: true,
    migratedFiles: 0,
    failedFiles: 0,
    errors: []
  };

  try {
    console.log('🚀 Starting Vercel Blob migration...');
    
    // 1. Migrate tenant logos
    await migrateTenantLogos(result);
    
    // 2. Migrate promotion files
    await migratePromotionFiles(result);
    
    console.log('✅ Migration completed:', result);
    return result;
  } catch (error) {
    result.success = false;
    result.errors.push(`Migration failed: ${error}`);
    console.error('❌ Migration failed:', error);
    return result;
  } finally {
    await prisma.$disconnect();
  }
}

async function migrateTenantLogos(result: MigrationResult) {
  const logosDir = join(process.cwd(), 'public/uploads/logos');
  
  try {
    const files = await readdir(logosDir);
    console.log(`📁 Found ${files.length} logo files to migrate`);
    
    for (const filename of files) {
      try {
        const filePath = join(logosDir, filename);
        const fileBuffer = await readFile(filePath);
        
        // Extract tenant ID from filename (format: tenantId-timestamp.ext)
        const tenantId = filename.split('-')[0];
        
        if (!tenantId) {
          result.errors.push(`Invalid filename format: ${filename}`);
          result.failedFiles++;
          continue;
        }
        
        // Upload to blob with new path structure
        const blobPath = `tenants/${tenantId}/logos/${filename}`;
        const blob = await put(blobPath, fileBuffer, {
          access: 'public',
          addRandomSuffix: false,
          allowOverwrite: true
        });
        
        // Update tenant record
        await prisma.tenant.update({
          where: { id: tenantId },
          data: { logoImage: blob.url }
        });
        
        result.migratedFiles++;
        console.log(`✓ Migrated logo: ${filename} → ${blob.url}`);
        
      } catch (error) {
        result.failedFiles++;
        result.errors.push(`Failed to migrate logo ${filename}: ${error}`);
        console.error(`✗ Failed to migrate logo ${filename}:`, error);
      }
    }
  } catch (error) {
    if ((error as any).code === 'ENOENT') {
      console.log('📁 No logos directory found, skipping logo migration');
    } else {
      result.errors.push(`Failed to read logos directory: ${error}`);
      console.error('❌ Failed to read logos directory:', error);
    }
  }
}

async function migratePromotionFiles(result: MigrationResult) {
  const promotionsDir = join(process.cwd(), 'public/uploads/promotions');
  
  try {
    const files = await readdir(promotionsDir);
    console.log(`📁 Found ${files.length} promotion files to migrate`);
    
    for (const filename of files) {
      try {
        const filePath = join(promotionsDir, filename);
        const fileBuffer = await readFile(filePath);
        
        // Extract tenant ID from filename
        const tenantId = filename.split('-')[0];
        
        if (!tenantId) {
          result.errors.push(`Invalid filename format: ${filename}`);
          result.failedFiles++;
          continue;
        }
        
        // Upload to blob with new path structure
        const blobPath = `tenants/${tenantId}/promotions/general/${filename}`;
        const blob = await put(blobPath, fileBuffer, {
          access: 'public',
          addRandomSuffix: false,
          allowOverwrite: true,
          multipart: fileBuffer.length > 5 * 1024 * 1024 // Use multipart for files > 5MB
        });
        
        // Update promotion records that use this file
        const oldUrl = `/uploads/promotions/${filename}`;
        const updateCount = await prisma.promotion.updateMany({
          where: { fileUrl: oldUrl },
          data: { fileUrl: blob.url }
        });
        
        result.migratedFiles++;
        console.log(`✓ Migrated promotion file: ${filename} → ${blob.url} (updated ${updateCount.count} records)`);
        
      } catch (error) {
        result.failedFiles++;
        result.errors.push(`Failed to migrate promotion file ${filename}: ${error}`);
        console.error(`✗ Failed to migrate promotion file ${filename}:`, error);
      }
    }
  } catch (error) {
    if ((error as any).code === 'ENOENT') {
      console.log('📁 No promotions directory found, skipping promotion migration');
    } else {
      result.errors.push(`Failed to read promotions directory: ${error}`);
      console.error('❌ Failed to read promotions directory:', error);
    }
  }
}

// Dry run function to preview what would be migrated
export async function dryRunMigration(): Promise<void> {
  console.log('🔍 Running migration preview...');
  
  // Check logos
  try {
    const logosDir = join(process.cwd(), 'public/uploads/logos');
    const logoFiles = await readdir(logosDir);
    console.log(`📁 Would migrate ${logoFiles.length} logo files:`);
    logoFiles.forEach(file => console.log(`  - ${file}`));
  } catch (error) {
    console.log('📁 No logos directory found');
  }
  
  // Check promotions
  try {
    const promotionsDir = join(process.cwd(), 'public/uploads/promotions');
    const promotionFiles = await readdir(promotionsDir);
    console.log(`📁 Would migrate ${promotionFiles.length} promotion files:`);
    promotionFiles.forEach(file => console.log(`  - ${file}`));
  } catch (error) {
    console.log('📁 No promotions directory found');
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const isDryRun = process.argv.includes('--dry-run');
  
  if (isDryRun) {
    dryRunMigration()
      .then(() => {
        console.log('🔍 Dry run completed');
        process.exit(0);
      })
      .catch(error => {
        console.error('❌ Dry run failed:', error);
        process.exit(1);
      });
  } else {
    migrateToBlob()
      .then(result => {
        if (result.success) {
          console.log('🎉 Migration completed successfully!');
          console.log(`📊 Results: ${result.migratedFiles} migrated, ${result.failedFiles} failed`);
          if (result.errors.length > 0) {
            console.log('⚠️  Errors encountered:');
            result.errors.forEach(error => console.log(`  - ${error}`));
          }
          process.exit(0);
        } else {
          console.error('❌ Migration failed with errors:', result.errors);
          process.exit(1);
        }
      })
      .catch(error => {
        console.error('❌ Migration script failed:', error);
        process.exit(1);
      });
  }
}
