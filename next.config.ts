import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    esmExternals: true,
  },
  transpilePackages: ["@ilamy/calendar"],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.public.blob.vercel-storage.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  webpack: (config, { isServer }) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    }

    // Externalize libSQL packages to avoid bundling issues
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        '@libsql/client': '@libsql/client',
        '@prisma/adapter-libsql': '@prisma/adapter-libsql',
        'libsql': 'commonjs libsql',
      });
    }

    // Ignore .node files and READMEs from libSQL packages
    config.module.rules.push({
      test: /\.node$/,
      use: 'ignore-loader',
    });

    config.module.rules.push({
      test: /node_modules\/@libsql\/.*\/README\.md$/,
      use: 'ignore-loader',
    });

    return config
  },
};

export default nextConfig;
