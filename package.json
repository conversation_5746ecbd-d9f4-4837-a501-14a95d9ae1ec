{"name": "dnd", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate --schema=prisma/schema && next build", "vercel-build": "./scripts/vercel-build.sh", "postinstall": "prisma generate --schema=prisma/schema", "start": "next start", "lint": "next lint", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "prisma:migrate": "prisma migrate dev --schema=prisma/schema", "prisma:generate": "prisma generate --schema=prisma/schema", "prisma:studio": "prisma studio --schema=prisma/schema", "prisma:push": "prisma db push --schema=prisma/schema", "prisma:migrate:prod": "prisma migrate deploy --schema=prisma/schema", "prisma:generate:prod": "prisma generate --schema=prisma/schema", "blob:migrate": "tsx scripts/migrate-to-blob.ts", "blob:migrate:dry-run": "tsx scripts/migrate-to-blob.ts --dry-run"}, "prisma": {"schema": "./prisma/schema"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@ilamy/calendar": "^0.2.1", "@libsql/client": "^0.15.12", "@prisma/adapter-libsql": "^6.14.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@vercel/blob": "^1.1.1", "@prisma/client": "^6.13.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jose": "^5.10.0", "lucide-react": "^0.536.0", "next": "15.4.5", "next-themes": "^0.4.6", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.4", "recharts": "2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/ui": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.4.5", "ignore-loader": "^0.1.2", "jsdom": "^26.1.0", "prisma": "^6.13.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "tsx": "^4.20.5", "tw-animate-css": "^1.3.6", "typescript": "^5", "vitest": "^3.2.4"}}