# Vercel Blob Implementation

## Overview

This document describes the implementation of Vercel Blob storage for file uploads in the DnD (Dental Management) application. The migration replaces local file storage with cloud-based blob storage for better scalability and performance.

## Implementation Status

✅ **Completed Components:**
- [x] @vercel/blob dependency installed
- [x] Blob utility functions with tenant isolation
- [x] Tenant logo upload API updated to use Vercel Blob
- [x] Promotion upload API updated to use Vercel Blob
- [x] Migration script for existing files
- [x] npm scripts for migration tasks

## Key Features

### 1. Tenant Isolation
All files are stored with tenant-specific paths:
```
/tenants/{tenantId}/logos/{filename}
/tenants/{tenantId}/promotions/general/{filename}
```

### 2. File Types Supported
- **Logos**: PNG, JPG, JPEG, WebP (max 2MB)
- **Promotions**: Images (PNG, JPG, JPEG, WebP, GIF) and Videos (MP4, WebM, OGG, QuickTime) (max 20MB)

### 3. Upload Features
- Progress tracking with `onUploadProgress`
- Multipart uploads for files > 5MB (promotions) or > 20MB (logos)
- Automatic cleanup of old files when replacing
- Comprehensive error handling

## Configuration

### Environment Variables
You need to set the following environment variable:

```env
BLOB_READ_WRITE_TOKEN=vercel_blob_rw_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

**How to get your token:**
1. Go to your Vercel dashboard
2. Navigate to "Storage" > "Blob" > "Settings"
3. Copy the "Read and Write Token"

### Blob Store Details
- **Store ID**: `store_gC5PM97Og0t5y2x9`
- **Base URL**: `https://gc5pm97og0t5y2x9.public.blob.vercel-storage.com`

## API Endpoints

### 1. Tenant Logo Upload
**Endpoint**: `POST /api/tenant/logo`
- Accepts form data with `logo` field
- Returns blob URL in response
- Automatically cleans up old logo when replacing

### 2. Promotion File Upload
**Endpoint**: `POST /api/uploads/promotion`
- Accepts form data with `file` field
- Supports both images and videos
- Returns blob URL for use in promotion records

## Migration

### Running Migration
The migration script moves existing files from local storage to Vercel Blob:

```bash
# Preview what will be migrated (dry run)
pnpm run blob:migrate:dry-run

# Run actual migration
pnpm run blob:migrate
```

### Migration Process
1. **Tenant Logos**: Moves files from `public/uploads/logos/` to blob storage
2. **Promotion Files**: Moves files from `public/uploads/promotions/` to blob storage
3. **Database Updates**: Updates all database records with new blob URLs
4. **Error Handling**: Continues migration even if some files fail

### Current Migration Status
Based on dry run analysis:
- **3 logo files** ready for migration
- **6 promotion files** ready for migration

## File Structure

### New Files Added
```
lib/blob-utils.ts                    # Core blob utility functions
scripts/migrate-to-blob.ts           # Migration script
scripts/run-migration.js             # Migration runner (legacy)
docs/vercel-blob-implementation.md   # This documentation
```

### Modified Files
```
app/api/tenant/logo/route.ts         # Updated to use Vercel Blob
app/api/uploads/promotion/route.ts   # Updated to use Vercel Blob
package.json                         # Added migration scripts and dependencies
```

## Blob Utility Functions

### BlobService Class
The `BlobService` class provides the following methods:

- `uploadFile()`: Upload files with tenant isolation
- `deleteFile()`: Delete files by URL
- `generatePath()`: Create tenant-specific paths
- `generateFilename()`: Generate unique filenames
- `isBlobUrl()`: Check if URL is a blob URL
- `createFilename()`: Create filename from File object

## Testing

### Manual Testing Checklist
- [ ] Logo upload in tenant settings
- [ ] Logo replacement (old file cleanup)
- [ ] Logo deletion
- [ ] Promotion file upload (images)
- [ ] Promotion file upload (videos)
- [ ] Error handling for invalid file types
- [ ] Error handling for oversized files

### Migration Testing
- [ ] Run dry migration to preview files
- [ ] Backup database before migration
- [ ] Run actual migration
- [ ] Verify all URLs updated correctly
- [ ] Test file accessibility from new URLs

## Rollback Strategy

If issues occur, you can rollback by:

1. **Database Rollback**: Restore database from backup taken before migration
2. **Code Rollback**: Revert API route changes to use local storage
3. **Environment Toggle**: Use feature flags to switch storage methods

## Performance Considerations

### Optimizations Implemented
- **CDN Distribution**: Files automatically distributed via Vercel's global CDN
- **Multipart Uploads**: Large files use multipart upload for reliability
- **Progress Tracking**: Real-time upload progress for better UX
- **Lazy Loading**: Consider implementing for image display

### Monitoring
- Track upload success rates
- Monitor blob storage usage in Vercel dashboard
- Performance metrics for file serving

## Security Features

- **Tenant Isolation**: Strict path-based isolation with tenant prefixes
- **File Validation**: Enhanced file type and size validation
- **Public Access**: Files are publicly accessible (suitable for logos and promotion materials)
- **Content-Type Detection**: Automatic content-type handling

## Cost Considerations

### Vercel Blob Pricing
- **Free Tier**: 1GB storage, 10GB bandwidth
- **Pro Tier**: $10/month for 100GB storage, 1TB bandwidth
- **Additional**: $1/GB storage, $1/10GB bandwidth

### Migration Benefits
- No server storage costs
- Reduced server bandwidth usage
- Predictable pricing model
- Global CDN distribution

## Future Enhancements

### Potential Improvements
1. **Client-side Uploads**: Direct browser uploads using `@vercel/blob/client`
2. **Upload Progress UI**: Real-time progress bars in frontend
3. **Image Optimization**: Integration with Next.js image optimization
4. **Backup Strategy**: Cross-cloud backup for critical files
5. **Analytics**: Upload and access analytics

### Advanced Features to Explore
1. **Private Files**: Use `access: 'private'` for sensitive documents
2. **Signed URLs**: Time-limited access for private files
3. **Batch Operations**: Bulk upload and delete operations
4. **Stream Processing**: Direct upload from external URLs

## Troubleshooting

### Common Issues

1. **Environment Variable Not Set**
   - Error: "Missing BLOB_READ_WRITE_TOKEN"
   - Solution: Set the environment variable in your deployment

2. **Upload Fails**
   - Check file size limits
   - Verify file type is allowed
   - Ensure network connectivity

3. **Migration Issues**
   - Run dry migration first
   - Check file permissions
   - Verify database connectivity

### Debug Mode
Set `NODE_ENV=development` for additional logging during uploads and migration.

## Support

For issues related to:
- **Vercel Blob**: Check Vercel documentation and status page
- **Implementation**: Review this documentation and code comments
- **Migration**: Run dry migration first and check logs

---

**Last Updated**: Implementation completed with working migration script and API endpoints.
**Status**: Ready for production deployment after environment variable configuration.
