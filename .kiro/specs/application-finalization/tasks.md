# Implementation Plan

- [x] 1. Update dashboard layout to use SidebarProvider

  - Import SidebarProvider, SidebarInset, and SidebarTrigger from shadcn sidebar
  - Wrap the entire dashboard layout content in SidebarProvider
  - Replace the main content wrapper with SidebarInset
  - _Requirements: 1.1, 1.3_

- [x] 2. Integrate AppSidebar into dashboard layout

  - Add AppSidebar component to the dashboard layout
  - Position sidebar correctly within SidebarProvider structure
  - Ensure sidebar renders alongside main content
  - _Requirements: 1.1_

- [x] 3. Add sidebar trigger for mobile navigation

  - Add SidebarTrigger button to dashboard header for mobile
  - Position trigger appropriately in header layout
  - Test mobile sidebar toggle functionality
  - _Requirements: 1.3_

- [x] 4. Remove redundant navigation from dashboard header

  - Remove the navigation menu from DashboardHeader component
  - Keep only the application title and user menu (Profile, Logout)
  - Clean up unused navigation-related code and styles
  - _Requirements: 3.2, 3.4_

- [x] 5. Fix AppSidebar navigation paths

  - Update sidebar navigation paths to use `/dashboard` prefix
  - Change `/patients` to `/dashboard/patients`
  - Change `/billing` to `/dashboard/billing`
  - Change `/reports` to `/dashboard/reports`
  - _Requirements: 1.2, 3.3_

- [x] 6. Implement active navigation state detection

  - Add usePathname hook to AppSidebar component
  - Create logic to detect current active navigation item
  - Apply active styling to current navigation item
  - _Requirements: 1.4_

- [x] 7. Test sidebar navigation functionality

  - Verify all sidebar links navigate correctly
  - Test sidebar collapse/expand behavior
  - Ensure navigation state persists across page changes
  - _Requirements: 1.2, 1.4_

- [x] 8. Create breadcrumb component for dashboard

  - Create or update `components/layout/Breadcrumbs.tsx`
  - Implement breadcrumb generation based on current route
  - Add proper navigation functionality to breadcrumb items
  - _Requirements: 9.1_

- [x] 9. Add breadcrumbs to patient pages

  - Integrate breadcrumbs into patient list page
  - Add breadcrumbs to patient details page (Dashboard > Patients > [Patient Name])
  - Ensure breadcrumbs update correctly when navigating
  - _Requirements: 9.2_

- [x] 10. Add breadcrumbs to billing pages

  - Integrate breadcrumbs into billing list page
  - Add breadcrumbs to invoice details page (Dashboard > Billing > Invoice #[Number])
  - Test breadcrumb navigation functionality
  - _Requirements: 9.2_

- [x] 11. Update dashboard metrics to show real data

  - Connect DashboardMetrics component to actual API endpoints
  - Display real counts for today's patients, pending treatments, outstanding invoices
  - Add proper loading states for metrics
  - _Requirements: 2.1_

- [x] 12. Implement functional quick actions

  - Update QuickActions component with working navigation
  - Connect "Create Patient" action to patient creation modal
  - Connect "Search Patients" to patient search functionality
  - Connect "Process Payments" to billing section
  - _Requirements: 2.2_

- [x] 13. Update recent activity with real data

  - Connect RecentActivity component to actual data sources
  - Display recent patient visits, treatments, and payments
  - Add proper loading states and empty states
  - _Requirements: 2.3_

- [x] 14. Test patient creation workflow

  - Verify patient creation form works from dashboard quick actions
  - Test patient creation API integration
  - Ensure successful patient creation navigates to patient details
  - _Requirements: 4.1_

- [ ] 15. Test case sheet initialization workflow

  - Verify case sheet initialization button works on patient details
  - Test that initialization creates all 32 teeth with FDI numbering (11-48)
  - Ensure dental chart displays correctly after initialization
  - _Requirements: 4.2, 4.3_

- [ ] 16. Test dental chart display and interaction

  - Verify dental chart shows all teeth in correct FDI layout
  - Test tooth selection functionality
  - Ensure tooth status indicators display correctly
  - _Requirements: 5.2, 5.3_

- [ ] 17. Test patient search functionality

  - Verify patient search by phone number works
  - Test patient search by name
  - Test patient search by ID
  - Ensure search results display correctly in table
  - _Requirements: 5.1_

- [ ] 18. Test findings documentation workflow

  - Verify tooth selection opens findings panel
  - Test finding creation form and validation
  - Ensure findings save correctly and display in list
  - _Requirements: 4.4, 5.4_

- [ ] 19. Test treatment planning workflow

  - Verify finding creation prompts for treatment planning
  - Test treatment creation form and cost validation
  - Ensure treatments save and display correctly
  - _Requirements: 4.5, 5.4_

- [ ] 20. Test dental chart status updates

  - Verify dental chart updates when findings are added
  - Test color coding for different tooth statuses
  - Ensure status indicators reflect current tooth state
  - _Requirements: 6.1, 6.7_

- [ ] 21. Test invoice generation workflow

  - Verify treatments automatically create/update invoices
  - Test invoice cost calculations
  - Ensure invoice totals are correct
  - _Requirements: 4.6_

- [ ] 22. Test payment processing workflow

  - Verify payment form validation
  - Test payment processing API integration
  - Ensure invoice status updates after payment
  - _Requirements: 4.7, 6.4_

- [ ] 23. Test billing list and filters

  - Verify invoice list displays correctly
  - Test invoice filtering by patient, date, status
  - Test invoice search functionality
  - _Requirements: 5.5_

- [ ] 24. Test payment history and balance calculations

  - Verify payment history displays correctly
  - Test balance calculations after payments
  - Ensure payment status updates are accurate
  - _Requirements: 6.5, 6.6_

- [ ] 25. Implement notification count calculation

  - Create function to calculate pending treatments count
  - Create function to calculate outstanding invoices count
  - Add notification count API endpoints if needed
  - _Requirements: 6.1_

- [ ] 26. Add notification badges to sidebar

  - Update AppSidebar to accept notification counts
  - Display badges for non-zero notification counts
  - Style badges appropriately with proper contrast
  - _Requirements: 6.2_

- [ ] 27. Connect notification counts to dashboard

  - Fetch notification counts in dashboard layout
  - Pass counts to AppSidebar component
  - Ensure counts update when data changes
  - _Requirements: 6.1, 6.2_

- [ ] 28. Add error boundary to dashboard layout

  - Wrap dashboard content in error boundary
  - Create error fallback component for dashboard errors
  - Test error boundary with simulated errors
  - _Requirements: 10.1_

- [ ] 29. Add loading states to dashboard components

  - Add loading spinners to dashboard metrics
  - Add skeleton loaders to recent activity
  - Ensure loading states display during data fetching
  - _Requirements: 10.2_

- [ ] 30. Add empty states for dashboard components

  - Create empty state for when no recent activity exists
  - Add empty state for when no patients exist
  - Ensure empty states provide helpful guidance
  - _Requirements: 10.3_

- [ ] 31. Implement user-friendly error messages

  - Update API error responses to be user-friendly
  - Add error message display in forms
  - Ensure error messages provide recovery guidance
  - _Requirements: 10.4_

- [ ] 32. Run test suite and identify failures

  - Execute `npm test` or `pnpm test` to run all tests
  - Document any failing tests and their error messages
  - Categorize failures by type (unit, integration, component)
  - _Requirements: 7.1_

- [ ] 33. Fix failing unit tests

  - Fix component unit tests that are failing
  - Fix utility function tests
  - Fix hook tests
  - _Requirements: 7.2_

- [ ] 34. Fix failing integration tests

  - Fix API endpoint integration tests
  - Fix workflow integration tests
  - Fix database integration tests
  - _Requirements: 7.3_

- [ ] 35. Fix failing component tests

  - Fix React component rendering tests
  - Fix component interaction tests
  - Fix form validation tests
  - _Requirements: 7.4_

- [ ] 36. Add tests for sidebar integration

  - Write tests for sidebar navigation functionality
  - Test sidebar responsive behavior
  - Test active navigation state detection
  - _Requirements: 7.1_

- [ ] 37. Add tests for breadcrumb navigation

  - Write tests for breadcrumb generation
  - Test breadcrumb navigation functionality
  - Test breadcrumb display on different pages
  - _Requirements: 7.1_

- [ ] 38. Add tests for dashboard components

  - Write tests for dashboard metrics with real data
  - Test quick actions functionality
  - Test recent activity display
  - _Requirements: 7.2_

- [ ] 39. Add tests for complete workflow integration

  - Write end-to-end test for patient creation to payment
  - Test workflow with multiple findings and treatments
  - Test error scenarios and recovery
  - _Requirements: 7.3_

- [ ] 40. Test sidebar mobile responsiveness

  - Test sidebar collapse/expand on mobile
  - Verify touch interactions work correctly
  - Test sidebar overlay behavior on small screens
  - _Requirements: 1.3, 8.4_

- [ ] 41. Test dental chart mobile responsiveness

  - Verify dental chart displays correctly on mobile
  - Test touch interactions for tooth selection
  - Ensure chart is usable on small screens
  - _Requirements: 8.4_

- [ ] 42. Test form inputs on mobile devices

  - Verify form inputs trigger correct virtual keyboards
  - Test form validation on mobile
  - Ensure forms are usable with touch input
  - _Requirements: 8.4_

- [ ] 43. Test table layouts on mobile

  - Verify tables scroll horizontally on small screens
  - Test table filtering and search on mobile
  - Ensure table actions are touch-friendly
  - _Requirements: 8.4_

- [ ] 44. Test keyboard navigation

  - Verify all interactive elements are keyboard accessible
  - Test tab order throughout the application
  - Ensure keyboard shortcuts work correctly
  - _Requirements: 8.4_

- [ ] 45. Test screen reader compatibility

  - Verify proper ARIA labels are present
  - Test with screen reader software
  - Ensure content is properly announced
  - _Requirements: 8.4_

- [ ] 46. Validate color contrast ratios

  - Check all text/background combinations meet WCAG AA standards
  - Test with color contrast analyzer tools
  - Fix any contrast issues found
  - _Requirements: 8.4_

- [ ] 47. Test focus management in modals

  - Verify focus trapping in modal dialogs
  - Test focus restoration when modals close
  - Ensure proper focus indicators are visible
  - _Requirements: 8.4_

- [ ] 48. Optimize bundle size

  - Analyze current bundle size with webpack analyzer
  - Implement code splitting for large components
  - Remove unused dependencies and code
  - _Requirements: 8.1_

- [ ] 49. Add loading performance optimizations

  - Implement lazy loading for non-critical components
  - Add proper caching headers for static assets
  - Optimize image loading and sizing
  - _Requirements: 8.1_

- [ ] 50. Add smooth transitions and animations

  - Add subtle transitions for navigation changes
  - Implement smooth animations for sidebar toggle
  - Add loading animations for better perceived performance
  - _Requirements: 8.2_

- [ ] 51. Validate consistent styling

  - Review all components for consistent spacing
  - Ensure typography is consistent throughout
  - Verify color usage follows design system
  - _Requirements: 8.2_

- [ ] 52. Test all user feedback mechanisms
  - Verify toast notifications work correctly
  - Test loading states display appropriately
  - Ensure success/error messages are clear
  - _Requirements: 8.2_

## Rules & Tips

- Use the existing shadcn sidebar primitives (SidebarProvider, SidebarInset, SidebarTrigger) for proper responsive behavior
- Maintain the existing AuthGuard functionality while integrating the sidebar
- Keep the DashboardHeader minimal with just title and user menu after removing navigation
- Test each workflow step individually before testing complete flows
- Use the existing component library and avoid creating duplicate components
- Ensure all navigation paths are consistent with the dashboard prefix structure
- Run tests frequently during development to catch regressions early
- Focus on integration rather than rebuilding existing functionality
- Maintain the existing responsive design patterns and mobile optimizations
- Use the existing error handling patterns and extend them where needed
