# Implementation Plan

- [x] 1. Create FDI tooth numbering utility functions

  - Create `lib/clinical/fdi-numbering.ts` with FDI number generation (11-48)
  - Implement `generateFDINumbers()` function returning array of valid FDI numbers
  - Implement `calculateQuadrant(toothNumber)` function (1-4 based on FDI)
  - Implement `calculatePosition(toothNumber)` function (1-8 position in quadrant)
  - _Requirements: 3.2_

- [x] 2. Create tooth name mapping for FDI system

  - Add `getToothName(toothNumber)` function in `lib/clinical/fdi-numbering.ts`
  - Create mapping for all FDI numbers to anatomical names (e.g., "Upper Right Central Incisor")
  - Add `getToothType(toothNumber)` function returning Incisor/Canine/Premolar/Molar
  - Write unit tests for all FDI numbering functions
  - _Requirements: 3.2_

- [x] 3. Create CaseSheet Prisma extension

  - Create `lib/prisma-extensions/case_sheet.ts` file
  - Implement `createCaseSheetWithTeeth(patientId, tenantId)` method
  - Add `findWithDentalChart(caseSheetId)` method with teeth included
  - Add `updateStatus(caseSheetId, status)` method
  - _Requirements: 8.3_

- [x] 4. Create Tooth Prisma extension

  - Create `lib/prisma-extensions/tooth.ts` file (enhance existing)
  - Implement `createCompleteSet(caseSheetId, tenantId)` method using FDI numbers
  - Add `findWithFindings(toothId)` method including findings relationship
  - Add `updateStatus(toothId, status)` method for tooth status changes
  - _Requirements: 8.4_

- [x] 5. Create Finding Prisma extension

  - Create `lib/prisma-extensions/finding.ts` file (enhance existing)
  - Implement `createFinding(toothId, description, recordedById)` method
  - Add `findWithTreatments(findingId)` method including treatments relationship
  - Add `updateDescription(findingId, description)` method
  - _Requirements: 8.5_

- [x] 6. Create Treatment Prisma extension

  - Create `lib/prisma-extensions/treatment.ts` file (enhance existing)
  - Implement `createTreatment(findingId, procedureName, cost)` method
  - Add `updateStatus(treatmentId, status)` method
  - Add `markCompleted(treatmentId, completedById)` method with date
  - _Requirements: 8.6_

- [x] 7. Create Invoice Prisma extension

  - Create `lib/prisma-extensions/invoice.ts` file (enhance existing)
  - Implement `createOrUpdateForPatient(patientId, treatmentCost)` method
  - Add `addTreatmentCost(invoiceId, cost)` method
  - Add `updateAmounts(invoiceId, totalAmount)` method
  - Add `findWithPayments(invoiceId)` method
  - _Requirements: 8.7_

- [x] 8. Create Payment Prisma extension

  - Create `lib/prisma-extensions/payment.ts` file (enhance existing)
  - Implement `processPayment(patientId, invoiceId, amount, paymentMethod)` method
  - Add `findByInvoice(invoiceId)` method
  - Add `calculateInvoiceBalance(invoiceId)` method
  - _Requirements: 8.8_

- [x] 9. Create basic ClinicalWorkflowService structure

  - Create `lib/clinical/workflow-service.ts` file
  - Define ClinicalWorkflowService class with method signatures
  - Add basic error handling types and interfaces
  - Create WorkflowResult and WorkflowError type definitions
  - _Requirements: 9.1_

- [x] 10. Implement createPatientWithWorkflow helper function

  - Add `createPatientWithWorkflow(patientData)` method to ClinicalWorkflowService
  - Implement transaction that creates patient, user account, case sheet, and teeth
  - Add proper error handling and rollback on failure
  - Return complete workflow result with all created entities
  - _Requirements: 9.1_

- [x] 11. Implement createCaseSheetWithTeeth helper function

  - Add `createCaseSheetWithTeeth(patientId)` method to ClinicalWorkflowService
  - Use CaseSheet and Tooth extensions to create case sheet and all teeth atomically
  - Add validation to ensure patient exists and has no existing case sheet
  - Return case sheet with complete dental chart
  - _Requirements: 9.2_

- [x] 12. Implement createFindingWithTreatment helper function

  - Add `createFindingWithTreatment(findingData)` method to ClinicalWorkflowService
  - Create finding and prompt for treatment creation in single transaction
  - Validate tooth exists and belongs to correct tenant
  - Return finding with associated treatment
  - _Requirements: 9.3_

- [x] 13. Implement createTreatmentWithInvoice helper function

  - Add `createTreatmentWithInvoice(treatmentData)` method to ClinicalWorkflowService
  - Create treatment and automatically update/create patient invoice
  - Calculate and update invoice totals
  - Return treatment with updated invoice information
  - _Requirements: 9.4_

- [x] 14. Implement processPayment helper function

  - Add `processPayment(paymentData)` method to ClinicalWorkflowService
  - Create payment record and update invoice balances automatically
  - Handle overpayment scenarios and update invoice status
  - Return payment with updated invoice balance information
  - _Requirements: 9.5_

- [x] 15. Create patient creation API endpoint

  - Create `app/api/patients/create-with-workflow/route.ts`
  - Implement POST handler using ClinicalWorkflowService.createPatientWithWorkflow
  - Add input validation for patient data (firstName, lastName, phoneNumber)
  - Add proper error handling and return structured response
  - _Requirements: 1.1, 1.3, 1.4_

- [x] 16. Create patient search API endpoint

  - Create `app/api/patients/search/route.ts`
  - Implement POST handler for searching by phoneNumber, name, or ID
  - Use existing patient extension search methods
  - Return patient list with case sheet status
  - _Requirements: 1.2, 1.9_

- [x] 17. Create case sheet initialization API endpoint

  - Create `app/api/clinical/case-sheets/[id]/initialize/route.ts`
  - Implement POST handler using ClinicalWorkflowService.createCaseSheetWithTeeth
  - Add validation that patient exists and has no case sheet
  - Return complete dental chart data
  - _Requirements: 2.1, 2.3, 2.4_

- [x] 18. Create findings documentation API endpoint

  - Create `app/api/clinical/teeth/[id]/findings/route.ts`
  - Implement POST handler for creating findings on specific teeth
  - Use ClinicalWorkflowService.createFindingWithTreatment
  - Add validation for tooth existence and description requirements
  - _Requirements: 4.1, 4.2, 4.5_

- [x] 19. Create treatment planning API endpoint

  - Create `app/api/clinical/findings/[id]/treatments/route.ts`
  - Implement POST handler using ClinicalWorkflowService.createTreatmentWithInvoice
  - Add validation for procedureName and cost requirements
  - Return treatment with invoice update information
  - _Requirements: 5.1, 5.2, 5.5_

- [x] 20. Create payment processing API endpoint

  - Create `app/api/billing/invoices/[id]/payments/route.ts`
  - Implement POST handler using ClinicalWorkflowService.processPayment
  - Add validation for payment amount and method
  - Return payment with updated invoice balance
  - _Requirements: 7.1, 7.2, 7.4_


- [x] 25. Write unit tests for FDI numbering system

  - Create `tests/lib/clinical/fdi-numbering.test.ts`
  - Test all FDI number generation and calculation functions
  - Test tooth name mapping for all valid FDI numbers
  - Test error handling for invalid tooth numbers
  - _Requirements: 3.2_

- [x] 26. Write unit tests for Prisma extensions

  - Create test files for each extension (case_sheet, tooth, finding, treatment, invoice, payment)
  - Test all CRUD operations and custom methods
  - Test error handling and validation
  - Test tenant isolation for all operations
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8_

- [x] 27. Write unit tests for ClinicalWorkflowService

  - Create `tests/lib/clinical/workflow-service.test.ts`
  - Test each workflow helper function independently
  - Test transaction rollback scenarios
  - Test error handling and validation
  - _Requirements: 9.1, 9.2, 9.3, 9.4, 9.5, 9.7_

- [x] 28. Write integration tests for API endpoints

  - Create test files for all workflow API endpoints
  - Test complete request/response cycles
  - Test authentication and authorization
  - Test error scenarios and validation
  - _Requirements: 1.1, 2.1, 4.1, 5.1, 7.1_

- [x] 29. Write end-to-end workflow integration test

  - Create `tests/integration/complete-workflow.test.ts`
  - Test complete patient creation to payment workflow
  - Test workflow with multiple findings and treatments
  - Test workflow rollback on failures
  - Validate data integrity across all steps
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1_

- [x] 30. Add comprehensive error handling and logging
  - Create workflow-specific error classes in `lib/clinical/errors.ts`
  - Add detailed logging to all workflow operations
  - Implement user-friendly error messages
  - Add error tracking and monitoring
  - _Requirements: 9.7_


## Rules & Tips

- Prefer pure, deterministic utilities for domain logic (e.g., FDI numbering) to maximize testability.
- Keep clinical constants (names/types) derived algorithmically when possible to avoid manual mapping errors.
- Add unit tests close to utilities; run targeted tests (`pnpm test:run <path>`) to validate atomic steps without flaky unrelated suites.
- Infer `tenantId` from related models (e.g., `tooth.tenantId`) inside Prisma extensions to maintain strict tenant isolation without duplicating inputs.
- Prefer numeric normalization for Prisma Decimal fields: accept number|string inputs, convert using `Number()`, and avoid `any` to satisfy strict ESLint rules.
- Prefer explicit type annotations in API route handlers (e.g., map callbacks) to satisfy strict ESLint rules and avoid implicit any.
- In API routes, validate path params and required fields upfront; let service layer enforce entity existence and translate its domain errors to 400 with user-friendly messages.
- Testing Prisma extensions: Decimal fields should be asserted via `Number(value)` and helper methods relying on `Prisma.getExtensionContext` may need direct model creations to validate behaviors.
- For end-to-end workflow tests, prefer exercising public API routes end-to-end against the test SQLite DB; keep steps minimal and deterministic, and use targeted test runs for this task.
