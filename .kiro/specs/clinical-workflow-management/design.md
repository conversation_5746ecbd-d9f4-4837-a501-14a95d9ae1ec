# Design Document

## Overview

This design implements a comprehensive clinical workflow management system that follows a sequential workflow: patient creation/search → automatic user account creation with WhatsA<PERSON> OTP → immediate case sheet creation → complete dental chart initialization → findings documentation → treatment planning → automatic invoice generation → payment settlement. The system leverages the existing Next.js architecture with Prisma ORM, tenant isolation, and service-oriented patterns.

## Architecture

### System Architecture
The system follows a layered architecture pattern consistent with the existing codebase:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  Next.js Pages/Components + API Routes                     │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Service Layer                            │
│  Workflow Services + Helper Functions                      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Data Access Layer                       │
│  Prisma Extensions + CRUD Operations                       │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Database Layer                           │
│  PostgreSQL with Tenant Isolation                          │
└─────────────────────────────────────────────────────────────┘
```

### Workflow Architecture
The clinical workflow follows a strict sequential pattern with automatic triggering:

```mermaid
graph TD
    A[Patient Creation/Search] --> B[User Account Creation]
    B --> C[WhatsApp OTP Setup]
    C --> D[Case Sheet Creation]
    D --> E[Dental Chart Initialization]
    E --> F[Findings Documentation]
    F --> G[Treatment Planning]
    G --> H[Invoice Generation]
    H --> I[Payment Processing]
    
    B --> B1[Automatic Trigger]
    D --> D1[Automatic Trigger]
    E --> E1[Automatic Trigger]
    G --> G1[Automatic Trigger]
```

## Components and Interfaces

### 1. Workflow Service Layer

#### ClinicalWorkflowService
Main orchestrator for the complete workflow:

```typescript
interface ClinicalWorkflowService {
  // Main workflow orchestration
  createPatientWithCompleteWorkflow(data: PatientCreationData): Promise<WorkflowResult>
  searchPatientAndInitializeWorkflow(searchTerm: string, tenantId: string): Promise<WorkflowResult>
  
  // Individual workflow steps
  createPatientWithUser(patientData: PatientData): Promise<PatientWithUser>
  createCaseSheetWithTeeth(patientId: number): Promise<CaseSheetWithTeeth>
  createFindingWithTreatment(findingData: FindingData): Promise<FindingWithTreatment>
  createTreatmentWithInvoice(treatmentData: TreatmentData): Promise<TreatmentWithInvoice>
  processPaymentWithUpdate(paymentData: PaymentData): Promise<PaymentResult>
}
```

#### WhatsAppOTPService
Handles WhatsApp OTP integration:

```typescript
interface WhatsAppOTPService {
  sendOTP(phoneNumber: string): Promise<OTPResult>
  verifyOTP(phoneNumber: string, otp: string): Promise<VerificationResult>
  resendOTP(phoneNumber: string): Promise<OTPResult>
}
```

### 2. Enhanced Prisma Extensions

#### PatientExtension (Enhanced)
```typescript
interface PatientExtension {
  // Workflow methods
  createPatientWithWorkflow(data: CreatePatientInput): Promise<PatientWithWorkflow>
  searchPatients(criteria: SearchCriteria): Promise<Patient[]>
  
  // Existing methods remain unchanged
  findByPhoneNumber(phoneNumber: string, tenantId: string): Promise<Patient | null>
  validatePatient(data: Partial<CreatePatientInput>): Promise<ValidationResult>
}
```

#### CaseSheetExtension (New)
```typescript
interface CaseSheetExtension {
  createCaseSheetWithTeeth(patientId: number, tenantId: string): Promise<CaseSheetWithTeeth>
  findWithDentalChart(caseSheetId: number): Promise<CaseSheetWithTeeth>
  updateStatus(caseSheetId: number, status: CaseSheetStatus): Promise<CaseSheet>
}
```

#### ToothExtension (New)
```typescript
interface ToothExtension {
  createCompleteSet(caseSheetId: number, tenantId: string): Promise<Tooth[]>
  findWithFindings(toothId: number): Promise<ToothWithFindings>
  updateStatus(toothId: number, status: ToothStatus): Promise<Tooth>
}
```

#### FindingExtension (New)
```typescript
interface FindingExtension {
  createFindingWithTreatment(data: CreateFindingInput): Promise<FindingWithTreatment>
  findWithTreatments(findingId: number): Promise<FindingWithTreatments>
  updateDescription(findingId: number, description: string): Promise<Finding>
}
```

#### TreatmentExtension (New)
```typescript
interface TreatmentExtension {
  createTreatmentWithInvoice(data: CreateTreatmentInput): Promise<TreatmentWithInvoice>
  updateStatus(treatmentId: number, status: TreatmentStatus): Promise<Treatment>
  markCompleted(treatmentId: number, completedById: number): Promise<Treatment>
}
```

#### InvoiceExtension (New)
```typescript
interface InvoiceExtension {
  createOrUpdateForPatient(patientId: number, treatmentCost: number): Promise<Invoice>
  addTreatmentCost(invoiceId: number, cost: number): Promise<Invoice>
  updateAmounts(invoiceId: number, totalAmount: number): Promise<Invoice>
  findWithPayments(invoiceId: number): Promise<InvoiceWithPayments>
}
```

#### PaymentExtension (New)
```typescript
interface PaymentExtension {
  processPayment(data: CreatePaymentInput): Promise<PaymentWithInvoiceUpdate>
  findByInvoice(invoiceId: number): Promise<Payment[]>
  calculateInvoiceBalance(invoiceId: number): Promise<BalanceInfo>
}
```

### 3. API Route Structure

#### Patient Management APIs
```
POST /api/patients/create-with-workflow
POST /api/patients/search
GET  /api/patients/[id]/workflow-status
```

#### Clinical Workflow APIs
```
POST /api/clinical/case-sheets/[id]/teeth/initialize
POST /api/clinical/teeth/[id]/findings
POST /api/clinical/findings/[id]/treatments
GET  /api/clinical/case-sheets/[id]/complete
```

#### Billing APIs
```
POST /api/billing/treatments/[id]/invoice
POST /api/billing/invoices/[id]/payments
GET  /api/billing/invoices/[id]/balance
```

#### WhatsApp OTP APIs
```
POST /api/auth/whatsapp/send-otp
POST /api/auth/whatsapp/verify-otp
POST /api/auth/whatsapp/resend-otp
```

## Data Models

### Enhanced Model Relationships
The existing Prisma models support the workflow with these key relationships:

```prisma
// Patient -> User (1:1 optional)
// Patient -> CaseSheet (1:1)
// CaseSheet -> Tooth[] (1:many, auto-created)
// Tooth -> Finding[] (1:many)
// Finding -> Treatment[] (1:many)
// Patient -> Invoice[] (1:many)
// Invoice -> Payment[] (1:many)
```

### Workflow State Tracking
```typescript
interface WorkflowState {
  patientId: number
  currentStep: 'PATIENT_CREATED' | 'USER_CREATED' | 'CASE_SHEET_CREATED' | 
               'TEETH_CREATED' | 'FINDINGS_DOCUMENTED' | 'TREATMENTS_PLANNED' | 
               'INVOICES_GENERATED' | 'PAYMENTS_PROCESSED'
  completedSteps: string[]
  lastUpdated: Date
  errors?: string[]
}
```

### FDI Tooth Numbering Implementation
```typescript
interface ToothNumbering {
  // FDI notation: 11-18, 21-28, 31-38, 41-48
  generateFDINumbers(): number[]
  calculateQuadrant(toothNumber: number): number
  calculatePosition(toothNumber: number): number
  getToothName(toothNumber: number): string
}
```

## Error Handling

### Workflow Error Handling
```typescript
interface WorkflowError extends Error {
  step: string
  patientId?: number
  rollbackRequired: boolean
  userMessage: string
}

class WorkflowErrorHandler {
  static async handleWorkflowError(error: WorkflowError): Promise<void>
  static async rollbackWorkflow(patientId: number, fromStep: string): Promise<void>
  static createUserFriendlyMessage(error: WorkflowError): string
}
```

### Transaction Management
All workflow operations use database transactions to ensure atomicity:

```typescript
async function createPatientWithWorkflow(data: PatientCreationData): Promise<WorkflowResult> {
  return await prisma.$transaction(async (tx) => {
    // 1. Create patient
    const patient = await tx.patient.create(...)
    
    // 2. Create user account
    const user = await tx.user.create(...)
    
    // 3. Create case sheet
    const caseSheet = await tx.caseSheet.create(...)
    
    // 4. Create all teeth
    const teeth = await tx.tooth.createMany(...)
    
    return { patient, user, caseSheet, teeth }
  })
}
```

## Testing Strategy

### Unit Testing
- **Service Layer**: Test each workflow service method independently
- **Prisma Extensions**: Test all CRUD operations and custom methods
- **Helper Functions**: Test workflow orchestration logic
- **WhatsApp Integration**: Mock WhatsApp API calls

### Integration Testing
- **Complete Workflow**: Test end-to-end patient creation to payment
- **Database Transactions**: Test rollback scenarios
- **API Endpoints**: Test all workflow API routes
- **Tenant Isolation**: Verify proper tenant context throughout workflow

### Test Data Setup
```typescript
interface TestDataFactory {
  createTestPatient(): PatientData
  createTestCaseSheet(): CaseSheetData
  createTestFindings(): FindingData[]
  createTestTreatments(): TreatmentData[]
  createTestInvoice(): InvoiceData
  createTestPayments(): PaymentData[]
}
```

### Performance Testing
- **Bulk Operations**: Test creating multiple patients with complete workflows
- **Database Performance**: Monitor query performance for complex joins
- **Memory Usage**: Test memory consumption during large workflow operations
- **Concurrent Users**: Test multiple simultaneous workflow executions

## Implementation Phases

### Phase 1: Core Workflow Infrastructure
1. Create enhanced Prisma extensions for all models
2. Implement ClinicalWorkflowService with basic orchestration
3. Create helper functions for atomic operations
4. Set up error handling and transaction management

### Phase 2: Patient and Case Sheet Management
1. Implement patient creation with user account workflow
2. Create case sheet initialization with dental chart
3. Add search functionality for existing patients
4. Implement FDI tooth numbering system

### Phase 3: Clinical Documentation
1. Implement findings documentation system
2. Create treatment planning integration
3. Add automatic invoice generation
4. Implement payment processing workflow

### Phase 4: WhatsApp Integration
1. Integrate WhatsApp OTP service
2. Implement OTP verification workflow
3. Add patient authentication via phone/OTP
4. Create OTP management APIs

### Phase 5: API and UI Integration
1. Create all necessary API routes
2. Implement error handling and validation
3. Add comprehensive logging and monitoring
4. Create UI components for workflow management

## Security Considerations

### Data Protection
- All patient data encrypted at rest and in transit
- Tenant isolation enforced at database level
- Audit trails for all clinical data modifications
- HIPAA compliance for patient information handling

### Authentication & Authorization
- WhatsApp OTP for patient authentication
- Role-based access control for clinical staff
- Session management with secure tokens
- Rate limiting for OTP requests

### Input Validation
- Comprehensive validation for all workflow inputs
- Sanitization of user-provided data
- SQL injection prevention through Prisma
- XSS protection for clinical notes and descriptions

## Performance Optimization

### Database Optimization
- Proper indexing for tenant-based queries
- Connection pooling for high concurrency
- Query optimization for complex workflow joins
- Bulk operations for teeth creation

### Caching Strategy
- Cache frequently accessed patient data
- Cache dental chart templates
- Cache treatment pricing information
- Cache invoice calculations

### Monitoring and Metrics
- Track workflow completion times
- Monitor database query performance
- Alert on workflow failures
- Track user adoption metrics