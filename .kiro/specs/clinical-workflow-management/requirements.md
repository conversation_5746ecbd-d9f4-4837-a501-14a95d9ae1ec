# Requirements Document

## Introduction

This feature implements a comprehensive clinical workflow management system for dental practices that follows a specific sequential workflow: patient creation/search → automatic user account creation with WhatsApp OTP → immediate case sheet creation → complete dental chart initialization → findings documentation → treatment planning → automatic invoice generation → payment settlement. The system ensures each step triggers the next automatically while maintaining data integrity and clinical workflow requirements.

## Requirements

### Requirement 1: Patient Creation and Search with Automatic User Account Setup

**User Story:** As a clinic staff member, I want to create new patients or search for existing ones, and automatically set up their WhatsApp OTP-based login, so that the clinical workflow can begin immediately with secure patient access.

#### Acceptance Criteria

1. WHEN a staff member initiates patient creation THEN the system SHALL display a form requiring: firstName, lastName, phoneNumber, email, address, and dateOfBirth
2. WHEN a staff member searches for a patient THEN the system SHALL search by phoneNumber, firstName/lastName, or patient ID and display matching results
3. WHEN a new patient is created THEN the system SHALL automatically create a linked user account using the patient's phoneNumber as the username
4. WHEN a user account is created for a patient THEN the system SHALL set the userType to "PATIENT" and link it to the patient record via userId
5. WHEN a patient user account is created THEN the system SHALL generate a secure password and set isActive to true
6. WHEN a patient attempts to login THEN the system SHALL send an OTP via WhatsApp to their phoneNumber
7. WHEN a patient enters the correct OTP THEN the system SHALL authenticate them and provide access to their records
8. IF a phoneNumber already exists in the system THEN the system SHALL prevent duplicate patient creation
9. WHEN an existing patient is selected THEN the system SHALL proceed to case sheet creation if no caseSheet exists

### Requirement 2: Mandatory Case Sheet Creation Upon Patient Selection

**User Story:** As a clinic staff member, I want a case sheet to be automatically created immediately after patient creation or selection, so that clinical documentation can begin without additional steps.

#### Acceptance Criteria

1. WHEN a new patient is successfully created THEN the system SHALL immediately create a CaseSheet linked to that patient
2. WHEN an existing patient is selected and has no caseSheet THEN the system SHALL create a new CaseSheet
3. WHEN a case sheet is created THEN the system SHALL populate it with: patientId, tenantId, status set to "ACTIVE", and current timestamp
4. WHEN a case sheet is created THEN the system SHALL ensure proper tenant isolation by using the patient's tenantId
5. WHEN a case sheet creation fails THEN the system SHALL rollback the patient creation
6. WHEN a case sheet is successfully created THEN the system SHALL immediately create all teeth records

### Requirement 3: Automatic Complete Dental Chart Creation

**User Story:** As a dental practitioner, I want all 32 teeth to be automatically created and linked to each new case sheet, so that I can immediately begin documenting findings on any tooth without manual chart setup.

#### Acceptance Criteria

1. WHEN a case sheet is successfully created THEN the system SHALL automatically create tooth records for teeth 11-48 using FDI notation
2. WHEN each tooth is created THEN the system SHALL populate: toothNumber (FDI notation), caseSheetId, tenantId, quadrant and positionInQuadrant (auto-calculated), toothName (auto-populated), and status as "PRESENT"
3. WHEN teeth creation is completed THEN the system SHALL make the dental chart available for findings documentation
4. WHEN tooth creation fails THEN the system SHALL rollback the case sheet creation

### Requirement 4: Multiple Clinical Findings Documentation Per Tooth

**User Story:** As a dental practitioner, I want to document multiple clinical findings for each tooth with detailed surface-level information, so that I can create comprehensive diagnostic records that support treatment planning.

#### Acceptance Criteria

1. WHEN a practitioner selects a tooth THEN the system SHALL display a findings entry form for that tooth
2. WHEN documenting a finding THEN the system SHALL require: description (free text) and toothId
3. WHEN a finding is saved THEN the system SHALL populate: toothId, tenantId, description, recordedDate (current timestamp), and recordedById
4. WHEN multiple findings are documented for the same tooth THEN the system SHALL allow multiple findings per tooth
5. WHEN a finding is documented THEN the system SHALL immediately enable treatment creation for that finding
6. WHEN viewing findings THEN the system SHALL display them with their associated treatments

### Requirement 5: Mandatory Treatment Planning for Each Finding

**User Story:** As a dental practitioner, I want to create specific treatment plans for each documented finding, so that every clinical issue has a corresponding treatment approach and can be properly billed.

#### Acceptance Criteria

1. WHEN a finding is successfully saved THEN the system SHALL prompt for treatment creation
2. WHEN creating a treatment THEN the system SHALL require: procedureName, cost, and findingId
3. WHEN a treatment is created THEN the system SHALL populate: findingId, tenantId, procedureName, cost, status as "PENDING"
4. WHEN multiple treatments are needed for a finding THEN the system SHALL allow creating multiple treatments
5. WHEN a treatment is saved THEN the system SHALL automatically create or update the patient's invoice
6. WHEN viewing a finding THEN the system SHALL display associated treatments with their status
7. WHEN a treatment is completed THEN the system SHALL update status to "COMPLETED", set completedDate and completedById

### Requirement 6: Automatic Invoice Generation with User-Configurable Pricing

**User Story:** As a clinic administrator, I want invoices to be automatically generated immediately when treatments are planned, with the ability to set custom amounts for each treatment, so that billing is streamlined but pricing remains flexible.

#### Acceptance Criteria

1. WHEN a treatment is successfully saved THEN the system SHALL create an invoice if none exists for the patient, or update existing invoice
2. WHEN creating a new invoice THEN the system SHALL populate: patientId, tenantId, invoiceNumber (unique), invoiceDate (current date), status as "DRAFT"
3. WHEN a treatment is added THEN the system SHALL add the treatment cost to the invoice totalAmount
4. WHEN the user sets final amounts THEN the system SHALL update the invoice totalAmount and balanceDue
5. WHEN invoice amounts are finalized THEN the system SHALL update status to "SENT" to enable payment processing

### Requirement 7: Multiple Payment Settlement System

**User Story:** As a clinic administrator, I want to record multiple payments against invoices with full tracking of partial payments and outstanding balances, so that I can manage complex payment scenarios and maintain accurate financial records.

#### Acceptance Criteria

1. WHEN an invoice status is "SENT" THEN the system SHALL allow payment recording
2. WHEN recording a payment THEN the system SHALL require: amount, paymentMethod, paymentDate, patientId, and optional invoiceId
3. WHEN a payment is saved THEN the system SHALL create a payment record with: patientId, invoiceId, amount, paymentDate, paymentMethod, tenantId, and status as "COMPLETED"
4. WHEN a payment is recorded THEN the system SHALL update the invoice amountPaid and recalculate balanceDue
5. WHEN multiple payments are made THEN the system SHALL track all payments linked to the invoice
6. WHEN amountPaid equals totalAmount THEN the system SHALL update invoice status to "PAID"
7. WHEN viewing an invoice THEN the system SHALL display totalAmount, amountPaid, balanceDue, and payment history

### Requirement 8: Comprehensive CRUD Operations with Business Logic Enforcement

**User Story:** As a system user, I want complete create, read, update, and delete capabilities for all models in the clinical workflow, with proper validation and referential integrity, so that I can maintain accurate records while preserving data relationships.

#### Acceptance Criteria

1. WHEN performing CRUD operations on Patient THEN the system SHALL provide: create, read, update, and delete with proper tenantId isolation
2. WHEN performing CRUD operations on User THEN the system SHALL provide: create, read, update, and delete with userType and isActive management
3. WHEN performing CRUD operations on CaseSheet THEN the system SHALL provide: create, read, update, and delete with status management
4. WHEN performing CRUD operations on Tooth THEN the system SHALL provide: read and update (status changes), but teeth are auto-created with case sheets
5. WHEN performing CRUD operations on Finding THEN the system SHALL provide: create, read, update, and delete
6. WHEN performing CRUD operations on Treatment THEN the system SHALL provide: create, read, update, and delete with status management
7. WHEN performing CRUD operations on Invoice THEN the system SHALL provide: create, read, update, and delete with status and amount management
8. WHEN performing CRUD operations on Payment THEN the system SHALL provide: create, read, update, and delete
9. WHEN any record is modified THEN the system SHALL update audit fields: createdAt, updatedAt, createdById, updatedById

### Requirement 9: Workflow Orchestration Helper Functions and Extensions

**User Story:** As a developer implementing the clinical workflow, I want specialized helper functions and Prisma extensions that encapsulate the complex multi-step workflow operations, so that the business logic is consistently applied and the workflow steps are executed atomically.

#### Acceptance Criteria

1. WHEN implementing patient registration THEN the system SHALL provide a `createPatientWithWorkflow()` helper function that: creates patient record, creates user account, creates case sheet, and creates all teeth
2. WHEN implementing case sheet initialization THEN the system SHALL provide a `createCaseSheetWithTeeth()` helper function that: creates case sheet and generates all tooth records
3. WHEN implementing findings workflow THEN the system SHALL provide a `createFindingWithTreatment()` helper function that: creates finding and prompts for treatment creation
4. WHEN implementing treatment workflow THEN the system SHALL provide a `createTreatmentWithInvoice()` helper function that: creates treatment and updates patient invoice
5. WHEN implementing payment processing THEN the system SHALL provide a `processPayment()` helper function that: creates payment record and updates invoice balances
6. WHEN querying workflow data THEN the system SHALL provide Prisma extensions for common queries with proper includes
7. WHEN workflow operations fail THEN the system SHALL provide proper error handling and transaction rollback