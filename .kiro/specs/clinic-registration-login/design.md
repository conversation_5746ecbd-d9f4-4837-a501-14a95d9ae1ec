# Design Document

## Overview

The clinic registration and login system provides secure authentication and tenant onboarding for the multi-tenant dental practice management application. The system consists of two main workflows: clinic registration (which creates a new tenant and admin user) and user authentication (which validates credentials and establishes session context).

The design leverages the existing Prisma-based data layer, Next.js App Router for routing, and the current tenant context management system. It implements secure authentication practices including password hashing, input validation, and session management.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Registration Page] --> B[Registration API]
    C[Login Page] --> D[Login API]
    B --> E[Tenant Service]
    B --> F[User Service]
    D --> F
    E --> G[Prisma Client]
    F --> G
    G --> H[SQLite Database]
    D --> I[Session Manager]
    I --> J[Tenant Context]
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant LP as Login Page
    participant LA as Login API
    participant US as User Service
    participant SM as Session Manager
    participant TC as Tenant Context
    
    U->>LP: Access login page
    LP->>U: Display login form
    U->>LP: Submit credentials
    LP->>LA: POST /api/auth/login
    LA->>US: Validate credentials
    US->>LA: Return user data
    LA->>SM: Create session
    SM->>TC: Set tenant context
    LA->>U: Redirect to dashboard
```

### Registration Flow

```mermaid
sequenceDiagram
    participant U as User
    participant RP as Registration Page
    participant RA as Registration API
    participant TS as Tenant Service
    participant US as User Service
    participant DB as Database
    
    U->>RP: Access registration page
    RP->>U: Display registration form
    U->>RP: Submit clinic & admin data
    RP->>RA: POST /api/auth/register
    RA->>TS: Create tenant
    TS->>DB: Insert tenant record
    RA->>US: Create admin user
    US->>DB: Insert user record
    RA->>U: Redirect to login with success
```

## Components and Interfaces

### Frontend Components

#### RegistrationForm Component
- **Location**: `components/auth/RegistrationForm.tsx`
- **Purpose**: Collects clinic information and admin user details
- **Props**: 
  - `onSubmit: (data: RegistrationData) => Promise<void>`
  - `isLoading: boolean`
  - `errors: Record<string, string>`
- **State**: Form data, validation errors, loading state

#### LoginForm Component
- **Location**: `components/auth/LoginForm.tsx`
- **Purpose**: Handles user authentication
- **Props**:
  - `onSubmit: (credentials: LoginCredentials) => Promise<void>`
  - `isLoading: boolean`
  - `error: string | null`
- **State**: Form data, validation errors, loading state

### API Routes

#### Registration API
- **Route**: `/api/auth/register`
- **Method**: POST
- **Input**: `RegistrationData`
- **Output**: `{ success: boolean, message: string, tenantId?: string }`
- **Validation**: Clinic info, admin user data, password strength

#### Login API
- **Route**: `/api/auth/login`
- **Method**: POST
- **Input**: `LoginCredentials`
- **Output**: `{ success: boolean, user?: UserData, redirectUrl: string }`
- **Features**: Rate limiting, secure session creation

#### Logout API
- **Route**: `/api/auth/logout`
- **Method**: POST
- **Output**: `{ success: boolean }`
- **Features**: Session cleanup, tenant context clearing

### Services

#### TenantService
- **Location**: `lib/services/tenant.ts`
- **Methods**:
  - `createTenant(data: TenantData): Promise<Tenant>`
  - `validateTenantUniqueness(data: TenantData): Promise<boolean>`
  - `getTenantById(id: string): Promise<Tenant | null>`

#### UserService
- **Location**: `lib/services/user.ts`
- **Methods**:
  - `createAdminUser(tenantId: string, userData: AdminUserData): Promise<User>`
  - `authenticateUser(credentials: LoginCredentials): Promise<User | null>`
  - `hashPassword(password: string): Promise<string>`
  - `verifyPassword(password: string, hash: string): Promise<boolean>`

#### AuthService
- **Location**: `lib/services/auth.ts`
- **Methods**:
  - `createSession(user: User): Promise<string>`
  - `validateSession(sessionId: string): Promise<User | null>`
  - `destroySession(sessionId: string): Promise<void>`

## Data Models

### Registration Data Types

```typescript
interface RegistrationData {
  // Clinic Information
  clinicName: string;
  clinicAddress: string;
  clinicPhone: string;
  clinicEmail: string;
  
  // Admin User Information
  adminUsername: string;
  adminEmail: string;
  adminFirstName: string;
  adminLastName: string;
  adminPassword: string;
  adminPasswordConfirm: string;
}

interface TenantData {
  name: string;
  address: string;
  phoneNumber: string;
  email?: string;
}

interface AdminUserData {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  userType: 'ADMIN';
}
```

### Authentication Data Types

```typescript
interface LoginCredentials {
  username: string;
  password: string;
}

interface UserSession {
  userId: number;
  tenantId: string;
  userType: UserType;
  sessionId: string;
  expiresAt: Date;
}

interface AuthenticatedUser {
  id: number;
  tenantId: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  userType: UserType;
  isActive: boolean;
}
```

### Database Schema Extensions

The existing Prisma schema already supports the required fields. Key considerations:

- **Tenant Model**: Already has `name`, `phoneNumber`, `address` fields
- **User Model**: Has authentication fields (`username`, `email`, `password`) and user type enum
- **Multi-tenancy**: Existing `tenantId` foreign key relationships are in place

## Error Handling

### Validation Errors
- **Client-side**: Real-time form validation using React Hook Form
- **Server-side**: Zod schema validation for API inputs
- **Database**: Unique constraint violations handled gracefully

### Authentication Errors
- **Invalid Credentials**: Generic error message to prevent username enumeration
- **Account Inactive**: Specific error message for disabled accounts
- **Rate Limiting**: Temporary lockout with clear messaging

### Registration Errors
- **Duplicate Clinic**: Check for existing tenant with same business info
- **Username/Email Conflicts**: Validate uniqueness within tenant scope
- **Password Requirements**: Enforce strong password policies

## Testing Strategy

### Unit Tests
- **Services**: Test tenant creation, user authentication, password hashing
- **Validation**: Test form validation logic and schema validation
- **Utilities**: Test password strength, email format validation

### Integration Tests
- **API Routes**: Test registration and login endpoints
- **Database Operations**: Test tenant and user creation workflows
- **Session Management**: Test session creation and validation

### End-to-End Tests
- **Registration Flow**: Complete clinic registration process
- **Login Flow**: User authentication and dashboard redirect
- **Error Scenarios**: Invalid inputs, duplicate registrations, failed logins

### Security Tests
- **Password Security**: Verify hashing and storage practices
- **Input Validation**: Test for injection attacks and XSS
- **Rate Limiting**: Verify brute force protection
- **Session Security**: Test session management and expiration

## Security Considerations

### Password Security
- **Hashing**: Use bcrypt with appropriate salt rounds (12+)
- **Strength Requirements**: Minimum 8 characters, mixed case, numbers, symbols
- **Storage**: Never store plaintext passwords

### Session Management
- **Secure Cookies**: HttpOnly, Secure, SameSite attributes
- **Session Expiration**: Reasonable timeout periods
- **Session Invalidation**: Proper cleanup on logout

### Input Validation
- **Server-side Validation**: All inputs validated on server
- **SQL Injection Prevention**: Prisma ORM provides protection
- **XSS Prevention**: Proper input sanitization and output encoding

### Rate Limiting
- **Login Attempts**: Limit failed attempts per IP/username
- **Registration**: Prevent automated account creation
- **API Endpoints**: General rate limiting for all auth endpoints