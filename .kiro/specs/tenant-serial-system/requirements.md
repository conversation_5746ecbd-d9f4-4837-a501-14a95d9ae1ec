# Requirements Document

## Introduction

This feature implements a robust tenant-based serial number system for case sheets, invoices, and payments. The system uses dedicated serial tracking models (CaseSheetSerial, InvoiceSerial, PaymentSerial) to ensure each tenant has independent, sequential serial numbers starting from 1. The implementation assumes a clean database and focuses on minimal code changes to existing entity creation workflows.

## Requirements

### Requirement 1

**User Story:** As a clinic administrator, I want each tenant to have independent serial numbers for case sheets, invoices, and payments, so that our numbering system is isolated from other clinics and provides clear sequential identification.

#### Acceptance Criteria

1. WHEN a new case sheet is created THEN the system SHALL generate and assign the next sequential serial number for that tenant
2. WHEN a new invoice is created THEN the system SHALL generate and assign the next sequential serial number for that tenant  
3. WHEN a new payment is created THEN the system SHALL generate and assign the next sequential serial number for that tenant
4. WHEN viewing entities THEN each tenant SHALL see only their own sequential numbering starting from 1

### Requirement 2

**User Story:** As a developer, I want the serial number generation to be atomic and race-condition safe, so that no duplicate serial numbers are ever assigned even under concurrent operations.

#### Acceptance Criteria

1. WH<PERSON> generating a case sheet serial THEN the system SHALL create a new CaseSheetSerial record atomically
2. WH<PERSON> generating an invoice serial THEN the system SHALL create a new InvoiceSerial record atomically
3. WHEN generating a payment serial THEN the system SHALL create a new PaymentSerial record atomically
4. WHEN multiple entities are created simultaneously THEN each SHALL receive a unique sequential serial number
5. IF serial generation fails THEN the entity creation SHALL fail completely to maintain consistency

### Requirement 3

**User Story:** As a developer, I want the serial generation logic to be corrected to use the serial tracking models instead of counting existing entities, so that the system works reliably with the dedicated serial tables.

#### Acceptance Criteria

1. WHEN generateCaseSheetSerial is called THEN it SHALL count CaseSheetSerial records for the tenant and create a new record
2. WHEN generateInvoiceSerial is called THEN it SHALL count InvoiceSerial records for the tenant and create a new record
3. WHEN generatePaymentSerial is called THEN it SHALL count PaymentSerial records for the tenant and create a new record
4. WHEN a serial is generated THEN the corresponding serial record SHALL be persisted to the database
5. WHEN returning the serial THEN it SHALL be the count + 1 value as a string

### Requirement 4

**User Story:** As a clinic user, I want serial numbers to be automatically assigned during entity creation, so that I don't need to manually manage numbering and all entities have proper identification.

#### Acceptance Criteria

1. WHEN creating a case sheet THEN the system SHALL automatically call generateCaseSheetSerial and populate the serial field
2. WHEN creating an invoice THEN the system SHALL automatically call generateInvoiceSerial and populate the serial field
3. WHEN creating a payment THEN the system SHALL automatically call generatePaymentSerial and populate the serial field
4. WHEN entity creation completes THEN the serial number SHALL be included in the response
5. IF serial generation fails THEN the entire entity creation SHALL be rolled back

### Requirement 5

**User Story:** As a developer, I want the serial system to integrate into existing API endpoints with minimal changes, so that current functionality continues to work while adding serial number support.

#### Acceptance Criteria

1. WHEN case sheet creation APIs are called THEN they SHALL integrate serial generation into their existing workflow
2. WHEN invoice creation APIs are called THEN they SHALL integrate serial generation into their existing workflow
3. WHEN payment creation APIs are called THEN they SHALL integrate serial generation into their existing workflow
4. WHEN API responses are returned THEN they SHALL include the assigned serial numbers in existing response structures
5. WHEN integrating serial generation THEN existing API contracts SHALL remain unchanged

### Requirement 6

**User Story:** As a clinic user, I want to see serial numbers displayed in the UI and be able to use them for searching, so that I can easily identify and find specific entities.

#### Acceptance Criteria

1. WHEN viewing case sheet lists THEN serial numbers SHALL be prominently displayed
2. WHEN viewing invoice lists THEN serial numbers SHALL be prominently displayed  
3. WHEN viewing payment lists THEN serial numbers SHALL be prominently displayed
4. WHEN searching entities THEN I SHALL be able to filter by serial number
5. WHEN displaying entity details THEN the serial number SHALL be clearly visible as an identifier