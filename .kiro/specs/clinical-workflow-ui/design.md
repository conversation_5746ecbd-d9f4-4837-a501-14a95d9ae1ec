# Design Document

## Overview

This design implements a comprehensive user interface for the clinical workflow management system using Next.js, React, TypeScript, and shadcn/ui components. The interface follows a minimalistic, intuitive design approach with mobile-first responsive design principles. The system provides seamless navigation through the complete dental practice workflow while maintaining excellent user experience across all device types.

## Architecture

### UI Architecture Pattern
The system follows a component-based architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Layout Layer                             │
│  App Layout + Sidebar Navigation (shadcn)                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Page Layer                               │
│  Next.js App Router Pages                                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Component Layer                          │
│  Reusable UI Components (shadcn + custom)                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Hook Layer                               │
│  Custom React Hooks for State Management                   │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
│  Next.js API Routes + Client-side Fetch                    │
└─────────────────────────────────────────────────────────────┘
```

### Navigation Flow
The application follows a hierarchical navigation pattern:

```mermaid
graph TD
    A[Dashboard] --> B[Patients]
    A --> C[Billing]
    A --> D[Reports]
    
    B --> B1[Patient List]
    B --> B2[Create Patient]
    B1 --> B3[Patient Details]
    B3 --> B4[Case Sheet]
    B4 --> B5[Dental Chart]
    B5 --> B6[Tooth Findings]
    B6 --> B7[Treatment Planning]
    
    C --> C1[Invoice List]
    C --> C2[Payment Processing]
    C1 --> C3[Invoice Details]
    C3 --> C4[Payment History]
```

## Components and Interfaces

### 1. Layout Components

#### AppSidebar Component
Main navigation sidebar using shadcn sidebar component:

```typescript
interface AppSidebarProps {
  className?: string
}

interface SidebarItem {
  title: string
  icon: React.ComponentType
  href: string
  badge?: string | number
}

const sidebarItems: SidebarItem[] = [
  { title: "Dashboard", icon: Home, href: "/dashboard" },
  { title: "Patients", icon: Users, href: "/patients" },
  { title: "Billing", icon: CreditCard, href: "/billing" },
  { title: "Reports", icon: BarChart3, href: "/reports" }
]
```

#### DashboardLayout Component
Main layout wrapper with sidebar and content area:

```typescript
interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  breadcrumbs?: BreadcrumbItem[]
}

interface BreadcrumbItem {
  label: string
  href?: string
}
```

### 2. Patient Management Components

#### PatientSearchForm Component
Search interface with filters and quick actions:

```typescript
interface PatientSearchFormProps {
  onSearch: (criteria: SearchCriteria) => void
  onCreateNew: () => void
  loading?: boolean
}

interface SearchCriteria {
  query: string
  searchType: 'phone' | 'name' | 'id'
  dateRange?: { from: Date; to: Date }
}
```

#### PatientTable Component
Sortable, filterable table with pagination:

```typescript
interface PatientTableProps {
  patients: Patient[]
  loading?: boolean
  onPatientSelect: (patient: Patient) => void
  onSort: (column: string, direction: 'asc' | 'desc') => void
  onFilter: (filters: PatientFilters) => void
  pagination: PaginationState
  onPaginationChange: (pagination: PaginationState) => void
}

interface PatientFilters {
  status?: 'active' | 'inactive'
  hasActiveCaseSheet?: boolean
  lastVisitRange?: { from: Date; to: Date }
}
```

#### PatientForm Component
Form for creating/editing patients:

```typescript
interface PatientFormProps {
  patient?: Patient
  onSubmit: (data: PatientFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

interface PatientFormData {
  firstName: string
  lastName: string
  phoneNumber: string
  email?: string
  address?: string
  dateOfBirth: Date
}
```

### 3. Dental Chart Components

#### DentalChart Component
Interactive visual dental chart:

```typescript
interface DentalChartProps {
  caseSheet: CaseSheetWithTeeth
  onToothClick: (tooth: Tooth) => void
  selectedTooth?: Tooth
  readonly?: boolean
}

interface ToothStatus {
  present: boolean
  hasFindings: boolean
  hasPendingTreatments: boolean
  hasCompletedTreatments: boolean
}
```

#### ToothComponent Component
Individual tooth representation:

```typescript
interface ToothComponentProps {
  tooth: Tooth
  status: ToothStatus
  onClick: () => void
  selected?: boolean
  size?: 'small' | 'medium' | 'large'
}
```

#### ToothTooltip Component
Hover information for teeth:

```typescript
interface ToothTooltipProps {
  tooth: Tooth
  findings: Finding[]
  treatments: Treatment[]
}
```

### 4. Clinical Documentation Components

#### FindingsPanel Component
Side panel for managing tooth findings:

```typescript
interface FindingsPanelProps {
  tooth: Tooth
  findings: Finding[]
  onAddFinding: (data: CreateFindingData) => Promise<void>
  onEditFinding: (id: number, data: UpdateFindingData) => Promise<void>
  onDeleteFinding: (id: number) => Promise<void>
  onCreateTreatment: (findingId: number) => void
  open: boolean
  onClose: () => void
}
```

#### FindingForm Component
Form for creating/editing findings:

```typescript
interface FindingFormProps {
  finding?: Finding
  onSubmit: (data: FindingFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

interface FindingFormData {
  description: string
  severity?: 'low' | 'medium' | 'high'
  notes?: string
}
```

#### TreatmentPlanningPanel Component
Treatment management interface:

```typescript
interface TreatmentPlanningPanelProps {
  finding: Finding
  treatments: Treatment[]
  onAddTreatment: (data: CreateTreatmentData) => Promise<void>
  onUpdateTreatment: (id: number, data: UpdateTreatmentData) => Promise<void>
  onDeleteTreatment: (id: number) => Promise<void>
  onStatusChange: (id: number, status: TreatmentStatus) => Promise<void>
}
```

### 5. Billing Components

#### InvoiceCard Component
Invoice summary display:

```typescript
interface InvoiceCardProps {
  invoice: InvoiceWithDetails
  onViewDetails: () => void
  onProcessPayment: () => void
  onEdit: () => void
}

interface InvoiceWithDetails extends Invoice {
  patient: Patient
  treatments: Treatment[]
  payments: Payment[]
  totalAmount: number
  amountPaid: number
  balanceDue: number
}
```

#### PaymentForm Component
Payment processing form:

```typescript
interface PaymentFormProps {
  invoice: Invoice
  onSubmit: (data: PaymentFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

interface PaymentFormData {
  amount: number
  paymentMethod: PaymentMethod
  paymentDate: Date
  notes?: string
}

type PaymentMethod = 'cash' | 'card' | 'bank_transfer' | 'insurance' | 'other'
```

#### PaymentHistory Component
Payment history table:

```typescript
interface PaymentHistoryProps {
  payments: Payment[]
  invoice: Invoice
  onRefund?: (paymentId: number) => Promise<void>
}
```

### 6. Data Table Components

#### DataTable Component
Reusable table with sorting, filtering, and pagination:

```typescript
interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  loading?: boolean
  pagination?: PaginationState
  onPaginationChange?: (pagination: PaginationState) => void
  sorting?: SortingState
  onSortingChange?: (sorting: SortingState) => void
  filtering?: FilteringState
  onFilteringChange?: (filtering: FilteringState) => void
  onRowClick?: (row: T) => void
}
```

#### TableFilters Component
Filter controls for data tables:

```typescript
interface TableFiltersProps {
  filters: FilterConfig[]
  values: Record<string, any>
  onChange: (values: Record<string, any>) => void
  onReset: () => void
}

interface FilterConfig {
  key: string
  label: string
  type: 'text' | 'select' | 'date' | 'dateRange' | 'number'
  options?: { label: string; value: any }[]
  placeholder?: string
}
```

### 7. Dashboard Components

#### DashboardMetrics Component
Key performance indicators:

```typescript
interface DashboardMetricsProps {
  metrics: DashboardMetrics
  loading?: boolean
}

interface DashboardMetrics {
  todayPatients: number
  pendingTreatments: number
  outstandingInvoices: number
  recentPayments: number
  totalRevenue: number
  averageInvoiceValue: number
}
```

#### QuickActions Component
Dashboard quick action buttons:

```typescript
interface QuickActionsProps {
  onCreatePatient: () => void
  onSearchPatients: () => void
  onViewPendingTreatments: () => void
  onProcessPayments: () => void
}
```

#### RecentActivity Component
Recent activity feed:

```typescript
interface RecentActivityProps {
  activities: Activity[]
  loading?: boolean
}

interface Activity {
  id: string
  type: 'patient_created' | 'treatment_completed' | 'payment_received' | 'invoice_sent'
  description: string
  timestamp: Date
  user: string
  relatedEntity?: {
    type: 'patient' | 'invoice' | 'treatment'
    id: number
    name: string
  }
}
```

## Data Models

### UI State Management
Using React hooks and context for state management:

```typescript
// Patient Context
interface PatientContextValue {
  patients: Patient[]
  selectedPatient?: Patient
  loading: boolean
  error?: string
  searchPatients: (criteria: SearchCriteria) => Promise<void>
  createPatient: (data: PatientFormData) => Promise<Patient>
  selectPatient: (patient: Patient) => void
}

// Clinical Context
interface ClinicalContextValue {
  caseSheet?: CaseSheetWithTeeth
  selectedTooth?: Tooth
  findings: Finding[]
  treatments: Treatment[]
  loading: boolean
  error?: string
  selectTooth: (tooth: Tooth) => void
  addFinding: (data: CreateFindingData) => Promise<void>
  addTreatment: (data: CreateTreatmentData) => Promise<void>
}

// Billing Context
interface BillingContextValue {
  invoices: Invoice[]
  selectedInvoice?: Invoice
  payments: Payment[]
  loading: boolean
  error?: string
  processPayment: (data: PaymentFormData) => Promise<void>
  updateInvoice: (id: number, data: UpdateInvoiceData) => Promise<void>
}
```

### Form Validation Schemas
Using Zod for form validation:

```typescript
// Patient form schema
const patientFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  phoneNumber: z.string().regex(/^\+?[\d\s-()]+$/, "Invalid phone number"),
  email: z.string().email("Invalid email").optional(),
  address: z.string().optional(),
  dateOfBirth: z.date()
})

// Finding form schema
const findingFormSchema = z.object({
  description: z.string().min(10, "Description must be at least 10 characters"),
  severity: z.enum(['low', 'medium', 'high']).optional(),
  notes: z.string().optional()
})

// Treatment form schema
const treatmentFormSchema = z.object({
  procedureName: z.string().min(1, "Procedure name is required"),
  cost: z.number().min(0, "Cost must be positive"),
  notes: z.string().optional()
})

// Payment form schema
const paymentFormSchema = z.object({
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  paymentMethod: z.enum(['cash', 'card', 'bank_transfer', 'insurance', 'other']),
  paymentDate: z.date(),
  notes: z.string().optional()
})
```

## Error Handling

### Error Boundary Components
```typescript
interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; reset: () => void }>
}

interface ErrorFallbackProps {
  error: Error
  reset: () => void
}
```

### Toast Notifications
Using shadcn toast for user feedback:

```typescript
interface ToastConfig {
  title: string
  description?: string
  variant: 'default' | 'destructive' | 'success'
  duration?: number
}

// Usage patterns
const showSuccess = (message: string) => toast({ title: message, variant: 'success' })
const showError = (message: string) => toast({ title: message, variant: 'destructive' })
```

### Loading States
Consistent loading indicators throughout the app:

```typescript
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

interface SkeletonProps {
  className?: string
  lines?: number
}
```

## Testing Strategy

### Component Testing
Using React Testing Library and Jest:

```typescript
// Example test structure
describe('PatientTable', () => {
  it('should render patient data correctly', () => {})
  it('should handle sorting', () => {})
  it('should handle pagination', () => {})
  it('should handle row selection', () => {})
})

describe('DentalChart', () => {
  it('should render all 32 teeth', () => {})
  it('should handle tooth selection', () => {})
  it('should display tooth status correctly', () => {})
  it('should be responsive', () => {})
})
```

### Integration Testing
Testing component interactions and API integration:

```typescript
describe('Patient Workflow', () => {
  it('should create patient and navigate to case sheet', () => {})
  it('should search patients and select existing one', () => {})
  it('should handle form validation errors', () => {})
})

describe('Clinical Workflow', () => {
  it('should document findings and create treatments', () => {})
  it('should update dental chart status', () => {})
  it('should handle concurrent tooth selection', () => {})
})
```

### Accessibility Testing
Ensuring WCAG compliance:

```typescript
describe('Accessibility', () => {
  it('should have proper ARIA labels', () => {})
  it('should support keyboard navigation', () => {})
  it('should have sufficient color contrast', () => {})
  it('should work with screen readers', () => {})
})
```

## Performance Optimization

### Code Splitting
Implementing route-based code splitting:

```typescript
// Lazy loading for heavy components
const DentalChart = lazy(() => import('./components/DentalChart'))
const InvoiceDetails = lazy(() => import('./components/InvoiceDetails'))
const ReportsPage = lazy(() => import('./pages/ReportsPage'))
```

### Memoization
Optimizing re-renders with React.memo and useMemo:

```typescript
// Memoized components
const MemoizedToothComponent = memo(ToothComponent)
const MemoizedDataTable = memo(DataTable)

// Memoized calculations
const dentalChartData = useMemo(() => 
  processDentalChartData(caseSheet), [caseSheet]
)
```

### Virtual Scrolling
For large data tables:

```typescript
interface VirtualTableProps<T> {
  data: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
}
```

## Responsive Design

### Breakpoint Strategy
Using Tailwind CSS breakpoints:

```typescript
const breakpoints = {
  sm: '640px',   // Mobile landscape
  md: '768px',   // Tablet
  lg: '1024px',  // Desktop
  xl: '1280px',  // Large desktop
  '2xl': '1536px' // Extra large desktop
}
```

### Mobile-First Components
Designing components with mobile-first approach:

```typescript
// Example responsive dental chart
const DentalChart = ({ caseSheet, onToothClick }) => {
  const isMobile = useMediaQuery('(max-width: 768px)')
  
  return (
    <div className={cn(
      "dental-chart",
      isMobile ? "grid-cols-4 gap-1" : "grid-cols-8 gap-2"
    )}>
      {/* Tooth components */}
    </div>
  )
}
```

### Touch-Friendly Interactions
Optimizing for touch devices:

```typescript
// Touch-friendly button sizes
const touchTargetSize = "min-h-[44px] min-w-[44px]" // 44px minimum touch target

// Swipe gestures for mobile navigation
const useSwipeGesture = (onSwipeLeft: () => void, onSwipeRight: () => void) => {
  // Implementation for swipe detection
}
```

## Implementation Phases

### Phase 1: Core Layout and Navigation
1. Implement AppSidebar with shadcn sidebar component
2. Create DashboardLayout with responsive design
3. Set up routing structure with Next.js App Router
4. Implement breadcrumb navigation
5. Add global search functionality

### Phase 2: Patient Management Interface
1. Create PatientSearchForm with filters
2. Implement PatientTable with sorting and pagination
3. Build PatientForm for creation/editing
4. Add patient detail view
5. Implement patient workflow navigation

### Phase 3: Dental Chart and Clinical Interface
1. Build interactive DentalChart component
2. Create ToothComponent with status indicators
3. Implement FindingsPanel with CRUD operations
4. Build TreatmentPlanningPanel
5. Add clinical workflow integration

### Phase 4: Billing and Payment Interface
1. Create InvoiceCard and invoice list view
2. Implement PaymentForm with validation
3. Build PaymentHistory component
4. Add invoice management features
5. Implement payment processing workflow

### Phase 5: Dashboard and Analytics
1. Build DashboardMetrics component
2. Create QuickActions interface
3. Implement RecentActivity feed
4. Add dashboard widgets
5. Create basic reporting interface

### Phase 6: Data Tables and Advanced Features
1. Build reusable DataTable component
2. Implement TableFilters with various filter types
3. Add export functionality
4. Implement advanced search features
5. Add bulk operations support

### Phase 7: Mobile Optimization and Polish
1. Optimize all components for mobile devices
2. Implement touch gestures and interactions
3. Add offline support where applicable
4. Performance optimization and code splitting
5. Accessibility improvements and testing

## Security Considerations

### Client-Side Security
- Input sanitization for all form fields
- XSS prevention in dynamic content rendering
- Secure token storage and management
- CSRF protection for form submissions

### Data Protection
- Sensitive data masking in UI components
- Secure API communication with proper headers
- Client-side validation as first line of defense
- Proper error message handling to prevent information leakage

### Authentication Integration
- Seamless integration with existing auth system
- Role-based UI component rendering
- Session timeout handling
- Secure logout functionality

## Accessibility Standards

### WCAG 2.1 AA Compliance
- Proper semantic HTML structure
- ARIA labels and descriptions
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance
- Focus management
- Alternative text for images and icons
- Accessible form labels and error messages