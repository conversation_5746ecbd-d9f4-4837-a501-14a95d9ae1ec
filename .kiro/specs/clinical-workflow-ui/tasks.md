# Implementation Plan

- [x] 1. Create shadcn sidebar component

  - Create `components/layout/AppSidebar.tsx` using shadcn sidebar
  - Add navigation items: Dashboard, Patients, Billing, Reports
  - _Requirements: 7.3, 7.6_

- [x] 2. Build responsive dashboard layout

  - Create `components/layout/DashboardLayout.tsx` with sidebar integration
  - Implement mobile-friendly collapsible sidebar
  - _Requirements: 7.6, 9.1, 9.2_

- [x] 3. Add breadcrumb navigation

  - Create `components/layout/Breadcrumbs.tsx` component
  - Integrate breadcrumbs with layout for navigation context
  - _Requirements: 7.7_

- [x] 4. Create loading spinner component

  - Create `components/ui/LoadingSpinner.tsx` with size variants (sm, md, lg)
  - Add loading states for different use cases
  - _Requirements: 10.8_

- [x] 5. Build error boundary component

  - Create `components/ui/ErrorBoundary.tsx` for error handling
  - Add error fallback UI with retry functionality
  - _Requirements: 10.4, 10.5_

- [x] 6. Create empty state component

  - Create `components/ui/EmptyState.tsx` for empty data displays
  - Add action suggestions for empty states
  - _Requirements: 8.7_

- [x] 7. Build confirmation dialog component

  - Create `components/ui/ConfirmDialog.tsx` for destructive actions
  - _Requirements: 10.1_

- [x] 8. Create skeleton loading component

  - Create `components/ui/Skeleton.tsx` for loading placeholders
  - Add different skeleton types (text, card, table)
  - _Requirements: 7.11, 8.8_

- [x] 9. Build base data table component

  - Create `components/ui/DataTable.tsx` with basic structure
  - Implement column definitions and row rendering
  - _Requirements: 8.1_

- [x] 10. Add table sorting functionality

  - Implement column sorting (ascending/descending) in DataTable
  - Add sort indicators and click handlers
  - _Requirements: 8.1_

- [x] 11. Implement table filtering

  - Create `components/ui/TableFilters.tsx` with filter controls
  - Add text, select, date, and date range filter types
  - _Requirements: 8.2, 8.4_

- [x] 12. Add table pagination

  - Create `components/ui/TablePagination.tsx` component
  - Implement configurable page sizes (10, 25, 50, 100)
  - _Requirements: 8.3_

- [x] 13. Create table search functionality

  - Add real-time search across all table columns
  - Implement search input with debouncing
  - _Requirements: 8.2_

- [x] 14. Add table export functionality

  - Implement CSV and PDF export for filtered table data
  - Add export progress indicators
  - _Requirements: 8.5_

- [x] 15. Build patient search form

  - Create `components/patients/PatientSearchForm.tsx`
  - Add search by phone, name, and ID options
  - _Requirements: 1.1, 1.2_

- [x] 16. Create patient data table

  - Create `components/patients/PatientTable.tsx` using DataTable
  - Display patient name, phone, last visit, case sheet status
  - _Requirements: 1.2, 1.7_

- [x] 17. Build patient creation form

  - Create `components/patients/PatientForm.tsx` with all required fields
  - Add form validation using Zod schema
  - _Requirements: 1.3, 1.4, 10.1, 10.2_

- [x] 18. Create patient list page

  - Create `app/patients/page.tsx` with search and table
  - Implement patient search API integration
  - _Requirements: 1.1, 1.2, 1.8_

- [x] 19. Add patient creation modal

  - Implement patient creation modal with form validation
  - Add success/error handling and navigation
  - _Requirements: 1.3, 1.4, 1.5_

- [x] 20. Build patient details page

  - Create `app/patients/[id]/page.tsx` for patient details
  - Add patient header with basic information
  - _Requirements: 1.5, 1.6_

- [x] 21. Create patient header component

  - Create `components/patients/PatientHeader.tsx` with patient info
  - Display name, phone, email, and last visit date
  - _Requirements: 1.6_

- [x] 22. Build case sheet card component

  - Create `components/patients/CaseSheetCard.tsx` for case sheet status
  - Add case sheet initialization button
  - _Requirements: 1.6, 2.1_

- [x] 23. Create dental chart base structure

  - Create `components/clinical/DentalChart.tsx` with 32 teeth grid
  - Implement upper (11-18, 21-28) and lower (31-38, 41-48) rows
  - _Requirements: 2.1, 2.2_

- [x] 24. Build individual tooth component

  - Create `components/clinical/ToothComponent.tsx` with FDI number
  - Add visual representation for different tooth types
  - _Requirements: 2.2, 2.3_

- [x] 25. Add tooth status indicators

  - Implement color coding for tooth status (findings, treatments)
  - Add visual indicators for missing teeth
  - _Requirements: 2.3, 2.6_

- [x] 26. Create tooth tooltip component

  - Create `components/clinical/ToothTooltip.tsx` for hover information
  - Display tooth name and findings/treatments summary
  - _Requirements: 2.5_

- [x] 27. Implement tooth selection functionality

  - Add click handlers for tooth selection
  - Highlight selected tooth in dental chart
  - _Requirements: 2.4, 3.7_

- [x] 28. Add dental chart legend

  - Create legend explaining color coding and tooth statuses
  - Position legend appropriately in chart layout
  - _Requirements: 2.8_

- [x] 29. Optimize dental chart for mobile

  - Implement responsive design for tablet and mobile
  - Add touch-friendly interactions
  - _Requirements: 2.7, 9.1, 9.5_

- [x] 30. Create findings panel component

  - Create `components/clinical/FindingsPanel.tsx` as slide-out panel
  - Add panel open/close functionality
  - _Requirements: 3.1, 3.8_

- [x] 31. Build findings list component

  - Create `components/clinical/FindingsList.tsx` for existing findings
  - Display description, date, and recorded by information
  - _Requirements: 3.3_

- [x] 32. Create finding form component

  - Create `components/clinical/FindingForm.tsx` with validation
  - Add description text area and save/cancel buttons
  - _Requirements: 3.2, 10.1_

- [x] 33. Implement findings CRUD operations

  - Add create, edit, and delete functionality for findings
  - Include confirmation dialogs for destructive actions
  - _Requirements: 3.4, 3.5, 3.6_

- [x] 34. Add treatment creation prompt

  - Show treatment creation option after finding is saved
  - Integrate with treatment planning workflow
  - _Requirements: 3.4_

- [x] 35. Create treatment planning panel

  - Create `components/clinical/TreatmentPlanningPanel.tsx`
  - Group treatments by finding with status indicators
  - _Requirements: 4.2_

- [x] 36. Build treatment form component

  - Create `components/clinical/TreatmentForm.tsx` with cost validation
  - Add procedure name, cost, and notes fields
  - _Requirements: 4.1, 10.1_

- [x] 37. Create treatments list component

  - Create `components/clinical/TreatmentsList.tsx` with status management
  - Display treatments grouped by finding
  - _Requirements: 4.2_

- [x] 38. Implement treatment status updates

  - Add dropdown for status changes (pending, in-progress, completed)
  - Auto-record completion date and user
  - _Requirements: 4.3, 4.4_

- [x] 39. Add treatment cost calculations

  - Display individual costs and running totals
  - Update invoice automatically when treatments change
  - _Requirements: 4.5, 4.8_

- [x] 40. Implement treatment editing and deletion

  - Add edit functionality for treatment details
  - Include confirmation for treatment deletion
  - _Requirements: 4.6, 4.7_

- [x] 41. Create invoice card component

  - Create `components/billing/InvoiceCard.tsx` for invoice summary
  - Display invoice number, date, patient, and totals
  - _Requirements: 5.1_

- [x] 42. Build invoice table component

  - Create `components/billing/InvoiceTable.tsx` with filters
  - Add filters by patient, date range, status, amount
  - _Requirements: 5.8_

 - [x] 43. Create invoice details component

  - Create `components/billing/InvoiceDetails.tsx` for detailed view
  - Show itemized treatment list with costs
  - _Requirements: 5.1, 5.2_

- [x] 44. Implement invoice editing

  - Add functionality to adjust individual treatment costs
  - Recalculate totals automatically
  - _Requirements: 5.3_

- [x] 45. Add invoice status management

  - Implement "Send Invoice" button to change status
  - Display total, paid, and balance amounts prominently
  - _Requirements: 5.4, 5.5_

- [x] 46. Create payment history component

  - Create `components/billing/PaymentHistory.tsx` table
  - Display payments with date, amount, method, balance
  - _Requirements: 5.6_

- [x] 47. Build payment form component

  - Create `components/billing/PaymentForm.tsx` with method selection
  - Add amount, method, date, and notes fields
  - _Requirements: 6.1, 6.2_

- [x] 48. Implement payment validation

  - Validate payment amount against outstanding balance
  - Warn about overpayments
  - _Requirements: 6.3_

- [x] 49. Add payment processing functionality

  - Update invoice balance immediately after payment
  - Display confirmation and updated balance
  - _Requirements: 6.4_

- [x] 50. Handle partial payments

  - Display remaining balance clearly
  - Allow additional payments on same invoice
  - _Requirements: 6.6_

- [x] 51. Implement automatic status updates

  - Update invoice status to "PAID" when fully paid
  - Display completion message
  - _Requirements: 6.7_

- [x] 52. Create payment receipt component

  - Create `components/billing/PaymentReceipt.tsx` for printing
  - Include payment details and updated balance
  - _Requirements: 6.8_

- [x] 53. Build billing main page

  - Create `app/dashboard/billing/page.tsx` with invoice list and filters
  - Implement invoice search and filtering
  - _Requirements: 5.8_

- [x] 54. Create invoice details page

  - Create `app/dashboard/billing/invoices/[id]/page.tsx`
  - Include payment processing functionality
  - _Requirements: 5.1, 6.1_

- [x] 55. Add billing navigation

  - Implement breadcrumbs for billing workflow
  - Add navigation between invoice list and details
  - _Requirements: 6.4, 6.7_

- [x] 56. Create dashboard metrics component

  - Create `components/dashboard/DashboardMetrics.tsx` with KPI cards
  - Display today's patients, pending treatments, outstanding invoices
  - _Requirements: 7.1_

- [x] 57. Build quick actions component

  - Create `components/dashboard/QuickActions.tsx` with workflow shortcuts
  - Add create patient, search, view treatments, process payments
  - _Requirements: 7.2_

- [x] 58. Create recent activity component

  - Create `components/dashboard/RecentActivity.tsx` with activity feed
  - Show recent visits, treatments, payments
  - _Requirements: 7.10_

- [x] 59. Build dashboard main page

  - Create `app/dashboard/page.tsx` as main dashboard view
  - Integrate metrics, quick actions, and recent activity
  - _Requirements: 7.1, 7.2, 7.10, 7.11_

- [x] 60. Create global search component

  - Create `components/layout/GlobalSearch.tsx` with search input
  - Search  patients based on id, name, phone number
  - _Requirements: 7.8_

- [x] 61. Add search results dropdown

  - Implement search results with navigation
  - Add keyboard shortcuts for search
  - _Requirements: 7.8, 8.2_

- [x] 62. Create notification center

  - Create `components/layout/NotificationCenter.tsx`
  - Add notifications for overdue treatments and unpaid invoices
  - _Requirements: 7.9_

- [x] 63. Implement toast notifications

  - Add toast notifications for user actions
  - Create notification badge in sidebar
  - _Requirements: 7.9, 10.7_

- [x] 64. Create patient form validation schema

  - Create Zod schema for patient form validation
  - Add field-level validation with error messages
  - _Requirements: 10.1, 10.2_

- [x] 65. Create finding form validation schema

  - Create Zod schema for finding form validation
  - Implement real-time validation feedback
  - _Requirements: 10.1, 10.3_

- [x] 66. Create treatment form validation schema

  - Create Zod schema for treatment form validation
  - Add cost and procedure name validation
  - _Requirements: 10.1, 10.2_

- [x] 67. Create payment form validation schema

  - Create Zod schema for payment form validation
  - Validate amount, method, and date fields
  - _Requirements: 10.1, 10.2_

- [x] 68. Add form error handling

  - Implement form submission error handling
  - Display user-friendly error messages
  - _Requirements: 10.4_


- [x] 71. Create patient data hook

  - Create `hooks/usePatients.ts` for patient data management
  - Handle patient search, creation, and selection
  - _Requirements: 1.1_

- [x] 72. Create clinical workflow hook

  - Create `hooks/useClinical.ts` for clinical workflow state
  - Manage case sheet, findings, and treatments
  - _Requirements: 3.1, 4.1_

- [x] 73. Create billing workflow hook

  - Create `hooks/useBilling.ts` for billing and payment state
  - Handle invoices and payment processing
  - _Requirements: 5.1, 6.1_

- [x] 74. Create toast notification hook

  - Create `hooks/useToast.ts` for notification management
  - Provide consistent toast messaging
  - _Requirements: 10.7_

- [x] 75. Add mobile navigation optimization

  - Optimize sidebar for mobile devices
  - Implement touch-friendly navigation
  - _Requirements: 9.2, 9.4_

- [x] 76. Create responsive table layouts

  - Add horizontal scrolling for mobile tables
  - Implement card-based layouts for small screens
  - _Requirements: 9.6_

- [x] 77. Add touch gesture support

  - Implement touch gestures for dental chart
  - Add pinch-to-zoom for chart interaction
  - _Requirements: 9.7_

- [x] 78. Optimize input types for mobile

  - Use appropriate virtual keyboards
  - Add touch-friendly form elements
  - _Requirements: 9.5_

- [x] 79. Create printable invoice component

  - Create `components/billing/PrintableInvoice.tsx`
  - Add print styles and clinic branding
  - _Requirements: 5.7_

- [x] 80. Add print dialog integration

  - Implement browser print dialog integration
  - Optimize print layouts
  - _Requirements: 5.7, 6.8_

  - [x] 83. Ensure color contrast compliance

  - Verify color contrast meets WCAG standards
  - Test with accessibility tools
  - _Requirements: 10.2_

- [x] 84. Add focus management

  - Implement proper focus management for modals
  - Add focus trapping for dialogs and panels
  - _Requirements: 10.1_

- [x] 85. Create 404 error page

  - Create `app/not-found.tsx` for 404 errors
  - Add navigation back to main sections
  - _Requirements: 10.4_

- [x] 86. Create application error page

  - Create `app/error.tsx` for application errors
  - Add error reporting and retry functionality
  - _Requirements: 10.5_

- [x] 87. Create error fallback component

  - Create `components/ui/ErrorFallback.tsx` for component errors
  - Add retry and navigation options
  - _Requirements: 10.4, 10.5_

- [x] 88. Add column visibility controls

  - Implement column show/hide for data tables
  - Add column reordering functionality
  - _Requirements: 8.6_

- [x] 89. Create bulk selection functionality

  - Add bulk selection for patient and invoice tables
  - Implement bulk actions (export, status updates)
  - _Requirements: 8.6_

- [x] 92. Create reports page foundation

  - Create `app/dashboard/reports/page.tsx` with report categories
  - Add basic report filters and placeholders
  - _Requirements: 7.3_


- [x] 96. Create component unit tests

  - Write unit tests for all reusable UI components
  - Test form validation and error handling
  - _Requirements: 10.1, 10.2_

- [x] 97. Add responsive behavior tests

  - Test responsive behavior and mobile interactions
  - Verify touch-friendly functionality
  - _Requirements: 9.1, 9.7_


- [x] 99. Implement integration tests

  - Test complete workflows from UI to API
  - Verify cross-component interactions
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1, 6.1_

- [x] 100. Add final polish and optimization
  - Optimize bundle size and loading performance
  - Add smooth animations and transitions
  - _Requirements: 7.11, 9.8_

## Rules & Tips

- Use shadcn/ui components as the foundation for all UI elements
- Implement mobile-first responsive design with Tailwind CSS
- Keep components small and focused on single responsibilities
- Use TypeScript interfaces for all props and data structures
- Implement proper error boundaries and loading states
- Follow accessibility best practices (WCAG 2.1 AA)
- Use React Hook Form with Zod validation for all forms
- Implement proper keyboard navigation and focus management
- Use React Query or SWR for API state management and caching
- Keep the dental chart interactive and touch-friendly
- Implement proper toast notifications for user feedback
- Use consistent spacing and typography throughout the application
- Implement proper loading skeletons for better perceived performance
- Test components in isolation and integration scenarios
- Use semantic HTML elements for better accessibility
- Implement proper ARIA labels and descriptions
- Keep the sidebar navigation simple with large, touch-friendly items
- Use consistent color coding for dental chart status indicators
- Implement proper form validation with clear error messages
- Add proper breadcrumb navigation for complex workflows
- Prefer using the existing shadcn sidebar primitives (`components/ui/sidebar.tsx`) to compose layout-level sidebars instead of re-implementing behavior.
- Compose app layouts by wrapping `AppSidebar` and page content in `SidebarProvider` with `SidebarTrigger` + `SidebarInset` for responsive, mobile-friendly behavior.
